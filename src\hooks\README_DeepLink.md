# Deep Link Implementation

## Tổng quan

Hệ thống deep link đã được cập nhật để hỗ trợ các case chia sẻ mới:

1. **Chia sẻ bài viết** - Navigate đến `RootScreen.PostDetail`
2. **Chia sẻ tin tức** - Navigate đến `RootScreen.DetailNews`
3. **Chia sẻ sản phẩm** - Navigate đến `RootScreen.ProductDetail`
4. **Chia sẻ QR code** - Navigate đến `RootScreen.login`

## URL Patterns

### 1. Chia sẻ bài viết

```
chainivo://posts/{id}
https://chainivo.com/posts/{id}
```

**Ví dụ:**

- `chainivo://posts/123`
- `https://chainivo.com/posts/456?ref=share&campaign=social`

**Navigation:** `RootScreen.PostDetail` với params `{id, referrer?, campaign?}`

### 2. Chia sẻ tin tức

```
chainivo://news/{id}
https://chainivo.com/news/{id}
```

**Ví dụ:**

- `chainivo://news/789`
- `https://chainivo.com/news/101?ref=email&utm_source=newsletter`

**Navigation:** `RootScreen.DetailNews` với params `{id, referrer?, campaign?}`

### 3. Chia sẻ sản phẩm

```
chainivo://product/{id}
https://chainivo.com/product/{id}
```

**Ví dụ:**

- `chainivo://product/abc123`
- `https://chainivo.com/product/def456?ref=catalog&campaign=summer`

**Navigation:** `RootScreen.ProductDetail` với params `{id, referrer?, campaign?}`

### 4. Chia sẻ QR code

```
chainivo://login/{id}
https://chainivo.com/login/{id}
```

**Ví dụ:**

- `chainivo://login/qr123`
- `https://chainivo.com/login/qr456?ref=qrcode&utm_source=app`

**Navigation:** `RootScreen.login` với params `{id?, referrer?, campaign?}`

## Query Parameters

Tất cả các deeplink đều hỗ trợ các query parameters sau:

- `ref` - Nguồn tham chiếu (referrer)
- `campaign` - Tên chiến dịch marketing
- `utm_source` - Nguồn UTM cho analytics

## Cách sử dụng

### 1. Trong component

```tsx
import {useDeepLink} from '@/hooks/useDeepLink';

export default function MyComponent() {
  const {handleDeepLink} = useDeepLink();

  // Xử lý deeplink thủ công
  const handleCustomLink = () => {
    handleDeepLink('chainivo://posts/123');
  };
}
```

### 2. Tạo deeplink để chia sẻ

```tsx
// Chia sẻ bài viết
const sharePost = (postId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://posts/${postId}?ref=share&campaign=social`;
  Share.share({message: url});
};

// Chia sẻ tin tức
const shareNews = (newsId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://news/${newsId}?ref=share&utm_source=app`;
  Share.share({message: url});
};

// Chia sẻ sản phẩm
const shareProduct = (productId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://product/${productId}?ref=share&campaign=product`;
  Share.share({message: url});
};

// Chia sẻ QR code
const shareQRCode = (qrId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://login/${qrId}?ref=qrcode&utm_source=share`;
  Share.share({message: url});
};
```

## Files đã được cập nhật

1. **`src/utils/deepLinkUtils.ts`**

   - Thêm parsing cho `posts`, `news`, `login`
   - Thêm các hàm extract: `extractPostInfo`, `extractNewsInfo`, `extractLoginInfo`

2. **`src/hooks/useDeepLink.ts`**

   - Thêm các handler: `handlePostNavigation`, `handleNewsNavigation`, `handleLoginNavigation`
   - Cập nhật `navigateToScreen` để xử lý các case mới

3. **`src/Screen/Layout/navigation/ecomNavigator.tsx`**

   - Cập nhật `RootStackParamList` để hỗ trợ parameters mới

4. **`src/Screen/Layout/navigation/LinkingConfiguration.ts`**
   - Đã có sẵn cấu hình cho các URL patterns

## Testing

Chạy test để kiểm tra functionality:

```bash
npm test src/hooks/__tests__/useDeepLink.test.ts
```

## Analytics Tracking

Hệ thống đã chuẩn bị sẵn các điểm tracking analytics:

- `post_view_from_deeplink`
- `news_view_from_deeplink`
- `product_view_from_deeplink`
- `login_from_qr_deeplink`

Uncomment các dòng Analytics.track() trong code khi cần sử dụng.
