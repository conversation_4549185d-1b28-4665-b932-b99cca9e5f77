import {useState, useCallback, useMemo} from 'react';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import WalletDA from '../../../../modules/wallet/walletDa';
import RewardFundDa from '../../../../modules/rewardFund/da/rewardFundDa';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {TransactionStatus} from '../../../../Config/Contanst';
import {RewardFund} from 'redux/types/rewardFundTypes';

interface Transaction {
  Id: string;
  CustomerId: string;
  Name: string;
  Mobile: string;
  Value: number;
  Description: string;
  DateCreated: number;
  Status: number;
  Type: number;
  CustomerRecive: string;
  Code: string;
}

export const useBonusFund = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [rewardFund, setRewardFund] = useState<RewardFund | null>(null);
  const [loadingData, setLoadingData] = useState(false);

  // State cho popup confirm
  const [showConfirmPopup, setShowConfirmPopup] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [pendingShareData, setPendingShareData] = useState<{
    month: number;
    year: number;
    userId: string;
  } | null>(null);

  const walletDA = useMemo(() => new WalletDA(), []);
  const rewardFundDA = useMemo(() => new RewardFundDa(), []);

  const fetchTransactions = useCallback(
    async (start: number, end: number) => {
      try {
        setLoadingData(true);
        const result = await walletDA.getPointBonusFund({
          startDate: start,
          endDate: end,
          adminId: ConfigAPI.adminCHAINIVO,
        });

        if (result.code === 200) {
          setTransactions(result.data || []);
        } else {
          setTransactions([]);
          showSnackbar({
            message: 'Không thể tải dữ liệu giao dịch',
            status: ComponentStatus.ERROR,
          });
        }
      } catch (error) {
        console.error('Error fetching transactions:', error);
        setTransactions([]);
        showSnackbar({
          message: 'Có lỗi xảy ra khi tải dữ liệu',
          status: ComponentStatus.ERROR,
        });
      } finally {
        setLoadingData(false);
      }
    },
    [walletDA],
  );

  // Hàm gọi API để lấy RewardFund theo tháng và năm
  const fetchRewardFund = useCallback(
    async (month: number, year: number) => {
      try {
        let result = await rewardFundDA.getRewardFundByMonthYear(month, year);
        if (result) {
          result.Status = Number(result.Status);
          result.Value = Number(result.Value);
          result.Sort = Number(result.Sort);
          result.Year = Number(result.Year);
          result.DateCreated = Number(result.DateCreated);
        }
        setRewardFund(result);
        return result;
      } catch (error) {
        console.error('Error fetching reward fund:', error);
        setRewardFund(null);
        return null;
      }
    },
    [rewardFundDA],
  );

  const getTotalPoints = () => rewardFund?.Value || 0;

  const getSuccessTransactionCount = () =>
    transactions.filter(t => t.Status === TransactionStatus.success).length;

  const getTotalTransactionCount = () => transactions.length;

  const canStartBonus = () => {
    const currentDay = new Date().getDate();
    return currentDay >= 1 && currentDay <= 5 && getTotalPoints() > 0;
  };

  const handleStartBonus = useCallback(
    (selectedMonth: number, selectedYear: number, userId: string) => {
      // Lưu thông tin để xử lý sau khi confirm
      setPendingShareData({
        month: selectedMonth,
        year: selectedYear,
        userId: userId,
      });
      // Hiển thị popup confirm
      setShowConfirmPopup(true);
    },
    [],
  );

  // Xử lý khi user ấn "Hủy" trong popup
  const handleCancelConfirm = useCallback(() => {
    setShowConfirmPopup(false);
    setPendingShareData(null);
  }, []);

  // Xử lý khi user ấn "Xác nhận" trong popup
  const handleConfirmShare = useCallback(async () => {
    if (!pendingShareData) return;

    try {
      setConfirmLoading(true);

      // Gọi hàm shareRewardFund
      const result = await rewardFundDA.shareRewardFund({
        month: pendingShareData.month,
        year: pendingShareData.year,
        userId: pendingShareData.userId,
      });

      if (result.success) {
        showSnackbar({
          message: result.message || 'Chia thưởng thành công!',
          status: ComponentStatus.SUCCSESS,
        });

        // Refresh lại dữ liệu RewardFund để cập nhật Status
        await fetchRewardFund(pendingShareData.month, pendingShareData.year);

        // Tạo startDate và endDate từ tháng và năm đang chọn
        const startDate = new Date(
          pendingShareData.year,
          pendingShareData.month - 1,
          1,
        ).getTime();
        const endDate = new Date(
          pendingShareData.year,
          pendingShareData.month,
          0,
          23,
          59,
          59,
          999,
        ).getTime();

        // Fetch lại danh sách giao dịch để hiển thị các giao dịch chia thưởng mới
        await fetchTransactions(startDate, endDate);
      } else {
        showSnackbar({
          message: result.message || 'Chia thưởng thất bại!',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error sharing reward fund:', error);
      showSnackbar({
        message: 'Có lỗi xảy ra khi chia thưởng!',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setConfirmLoading(false);
      setShowConfirmPopup(false);
      setPendingShareData(null);
    }
  }, [pendingShareData, rewardFundDA, fetchRewardFund]);

  return {
    isLoading,
    transactions,
    rewardFund,
    loadingData,
    fetchTransactions,
    fetchRewardFund,
    getTotalPoints,
    getSuccessTransactionCount,
    getTotalTransactionCount,
    canStartBonus,
    handleStartBonus,
    // Popup confirm states
    showConfirmPopup,
    confirmLoading,
    handleCancelConfirm,
    handleConfirmShare,
  };
};
