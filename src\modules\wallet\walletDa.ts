import store from 'redux/store/store';
import {DataController} from '../../base/baseController';
import {
  MissionType,
  StatusOrder,
  TransactionStatus,
  TransactionType,
} from '../../Config/Contanst';
import {randomGID, Ultis} from '../../utils/Utils';

class WalletDA {
  private orderController: DataController;
  private historyRewardController: DataController;
  private missionCustomerController: DataController;
  private missionController: DataController;
  private shopRewardController: DataController;
  private shopCateController: DataController;
  private rewardController: DataController;

  constructor() {
    this.orderController = new DataController('Order');
    this.historyRewardController = new DataController('HistoryReward');
    this.missionCustomerController = new DataController('MissionCustomer');
    this.missionController = new DataController('Mission');
    this.shopRewardController = new DataController('ShopReward');
    this.shopCateController = new DataController('ShopCate');
    this.rewardController = new DataController('Reward');
  }

  async getPointCustomer({customerId}: {customerId: string}) {
    try {
      const res = await this.historyRewardController.getListSimple({
        query: `@CustomerId: {${customerId}} @Status: [1 2]`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });
      debugger
      if (res?.code === 200) {
        const transactions = res.data || [];
        let total = 0;
        let income = 0;
        let expense = 0;

        transactions.forEach((transaction: any) => {
          const value = Number(transaction.Value) || 0;
          const status = Number(transaction.Status);

          // Cộng các reward.Value > 0 và Status = 2
          if (value > 0 && status === 2) {
            total += value;
            income += value;
          }
          // Cộng các reward.Value < 0 và Status = 1 hoặc 2
          else if (value < 0 && (status === 1 || status === 2)) {
            total += value;
            expense += value;
          }
        });

        return {
          total,
          income,
          expense,
        };
      }
      return {
        total: 0,
        income: 0,
        expense: 0,
      };
    } catch (error) {
      return {
        total: 0,
        income: 0,
        expense: 0,
      };
    }
  }

  async transferCustomPoint(params: {
    senderId: string;
    senderName: string;
    senderMobile: string;
    recipientId?: string;
    recipientName?: string;
    recipientMobile?: string;
    receiverAmount: number;
    senderAmount: number;
    type: TransactionType;
    status?: TransactionStatus;
    descriptionSender?: string;
    descriptionRecipient?: string;
  }) {
    const {
      senderId,
      senderName,
      senderMobile,
      recipientId,
      recipientName,
      recipientMobile,
      receiverAmount,
      senderAmount,
      type,
      status,
      descriptionSender,
      descriptionRecipient,
    } = params;
    const lstData: any[] = [];
    const dataSend = {
      Id: randomGID(),
      CustomerId: senderId,
      Name: senderName,
      Mobile: senderMobile,
      DateCreated: new Date().getTime(),
      Status: status,
      Value: -senderAmount,
      Type: type,
      Description: descriptionSender,
      CustomerRecive: recipientId || '',
      Code: Ultis.randomString(12),
    };
    lstData.push(dataSend);
    const dataReceive = {
      ...dataSend,
      Id: randomGID(),
      CustomerId: recipientId!,
      Name: recipientName!,
      Mobile: recipientMobile,
      CustomerRecive: senderId,
      Value: receiverAmount,
      Description: descriptionRecipient,
      Code: Ultis.randomString(12),
    };
    lstData.push(dataReceive);
    const res = await this.historyRewardController.add(lstData);
    if (res?.code === 200) {
      const transactionData = {
        status: status,
        transactionId: dataSend.Code,
        amount: receiverAmount,
        recipientName,
        recipientPhone: recipientMobile,
        senderName,
        timestamp: new Date().toISOString(),
        type,
      };
      return {code: res.code, transactionData};
    }
    return {code: res?.code};
  }

  async transferPoints(params: {
    senderId: string;
    senderName: string;
    senderMobile: string;
    recipientId?: string;
    recipientName?: string;
    recipientMobile?: string;
    amount: number;
    type: TransactionType;
    status?: TransactionStatus;
    descriptionSender?: string;
    descriptionRecipient?: string;
  }) {
    const {
      senderId,
      senderName,
      senderMobile,
      recipientId,
      recipientName,
      recipientMobile,
      amount,
      type,
      status,
      descriptionSender,
      descriptionRecipient,
    } = params;
    const lstData: any[] = [];
    const dataSend = {
      Id: randomGID(),
      CustomerId: senderId,
      Name: senderName,
      Mobile: senderMobile,
      DateCreated: new Date().getTime(),
      Status: status || TransactionStatus.pending,
      Value: -amount,
      Type: type,
      Description: descriptionSender,
      CustomerRecive: recipientId || '',
      Code: Ultis.randomString(12),
    };
    lstData.push(dataSend);
    const dataReceive = {
      ...dataSend,
      Id: randomGID(),
      CustomerId: recipientId!,
      Name: recipientName!,
      Mobile: recipientMobile,
      Status: status || TransactionStatus.pending,
      CustomerRecive: senderId,
      Value: amount,
      Description: descriptionRecipient,
      Code: Ultis.randomString(12),
    };
    lstData.push(dataReceive);
    const res = await this.historyRewardController.add(lstData);
    if (res?.code === 200) {
      const transactionData = {
        status: status || TransactionStatus.pending,
        transactionId: dataSend.Code,
        amount,
        recipientName,
        recipientPhone: recipientMobile,
        senderName,
        timestamp: new Date().toISOString(),
        type,
      };
      return {code: res.code, transactionData};
    }
    return {code: res?.code};
  }

  renderTransferPoint(params: {
    senderId: string;
    senderName: string;
    senderMobile: string;
    recipientId?: string;
    recipientName?: string;
    recipientMobile?: string;
    amount: number;
    orderId?: string;
    type: TransactionType;
    status?: TransactionStatus;
    descriptionSender?: string;
    descriptionRecipient?: string;
    dateCreated?: number;
  }) {
    const {
      senderId,
      senderName,
      senderMobile,
      recipientId,
      recipientName,
      recipientMobile,
      amount,
      orderId,
      type,
      status,
      descriptionSender,
      descriptionRecipient,
      dateCreated,
    } = params;
    const lstData: any[] = [];
    const dataSend = {
      Id: randomGID(),
      CustomerId: senderId,
      Name: senderName,
      Mobile: senderMobile,
      DateCreated: dateCreated ? dateCreated : new Date().getTime(),
      Status: status || TransactionStatus.pending,
      Value: -amount,
      Type: type,
      OrderId: orderId ? orderId : null,
      Description: descriptionSender,
      CustomerRecive: recipientId || '',
      Code: Ultis.randomString(12),
    };
    lstData.push(dataSend);
    const dataReceive = {
      ...dataSend,
      Id: randomGID(),
      CustomerId: recipientId!,
      Name: recipientName!,
      Status: status || TransactionStatus.pending,
      Mobile: recipientMobile,
      CustomerRecive: senderId,
      Value: amount,
      OrderId: orderId ? orderId : null,
      Description: descriptionRecipient,
      Code: Ultis.randomString(12),
      DateCreated: dateCreated ? dateCreated : new Date().getTime(),
    };
    lstData.push(dataReceive);

    return lstData;
  }

  async transferManyBills(bills: any[]) {
    const res = await this.historyRewardController.add(bills);
    if (res?.code === 200) {
      return true;
    }
    return false;
  }

  async CaculateMisson(cusId: string, type: number) {
    let startOfDay = new Date().setHours(0, 0, 0, 0);
    let endOfDay = new Date().setHours(23, 59, 59, 999);
    let successMissionMonth = false;
    const rankInfo = store.getState().customer.rankInfo;
    // #region xử lý nhiệm vụ
    const [missionByType, missionOrder] = await Promise.all([
      this.missionController.getListSimple({
        page: 1,
        size: 1,
        query: `@MissonType: [${type}]`,
      }),
      this.missionController.getListSimple({
        page: 1,
        size: 1,
        query: `@MissonType: [${MissionType.month}] @Type: [2]`, // nhiệm vụ tháng
      }),
    ]);
    if (missionByType.code === 200 && missionByType.data.length > 0) {
      const missionCustomer =
        await this.missionCustomerController.getListSimple({
          query: `@CustomerId: {${cusId}} @MissionId: {${missionByType.data[0].Id}} @DateCreated: [${startOfDay} ${endOfDay}] @Type: [${type}]`,
        });

      if (missionCustomer.code === 200 && missionCustomer.data.length == 0) {
        // TH add nhiệm vụ tháng, nếu tháng hiện tại đã hoàn thành thì không thêm mới
        if (type == MissionType.month) {
          const {start, end} = Ultis.getMonthRange();
          const missionMonthCustomer =
            await this.missionCustomerController.getListSimple({
              query: `@CustomerId: {${cusId}} @MissionId: {${missionOrder.data[0].Id}} @DateCreated: [${start} ${end}] @Type: [${MissionType.month}]`,
            });
          if (missionMonthCustomer?.code == 200) {
            if (missionMonthCustomer.data.length > 0) {
              // nothing
              successMissionMonth = true;
            } else {
              // chỉ add khi mà ngày hôm nay chưa có lịch sử thực hiện nhiệm vụ
              await this.missionCustomerController.add([
                {
                  Id: randomGID(),
                  DateCreated: new Date().getTime(),
                  CustomerId: cusId,
                  MissionId: missionByType.data[0].Id,
                  Name: missionByType.data[0].Name,
                  Type: type,
                  Value: missionByType.data[0]?.Value,
                },
              ]);
            }
          }
        } else {
          // chỉ add khi mà ngày hôm nay chưa có lịch sử thực hiện nhiệm vụ
          await this.missionCustomerController.add([
            {
              Id: randomGID(),
              DateCreated: new Date().getTime(),
              CustomerId: cusId,
              MissionId: missionByType.data[0].Id,
              Name: missionByType.data[0].Name,
              Type: type,
              Value: missionByType.data[0]?.Value,
            },
          ]);
        }

        //#region kiểm tra điều kiện + điểm khi làm nhiệm vụ logic
        //lấy ngày bắt đẩu và kết thúc của tháng
        const {start, end} = Ultis.getMonthRange();
        const order = await this.orderController.group({
          reducers: 'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS Total',
          searchRaw: `@CustomerId: {${cusId}} @DateCreated: [${start} ${end}] @Status: [${StatusOrder.success}]`,
        });
        var status = TransactionStatus.pending;
        if (order.code === 200 && order.data.length > 0) {
          const total = order.data.reduce(
            (total: number, item: any) => total + parseFloat(item.Total),
            0,
          );
          if (
            missionOrder.code === 200 &&
            missionOrder.data.length > 0 &&
            total >= missionOrder.data[0]?.TotalPriceOrder
          ) {
            status = TransactionStatus.success;
          }
        }
        if (successMissionMonth && type == MissionType.month) {
          //update tất cả bản ghi trong tháng hiện tại của history reward
          const historyRewardController = new DataController('HistoryReward');
          const historyReward = await historyRewardController.getListSimple({
            query: `@CustomerId: {${order.CustomerId}} @DateCreated: [${start} ${end}] @Type: [${TransactionType.mission}] @Status: [${TransactionStatus.pending}]`,
          });
          if (historyReward.code === 200) {
            const updatedHistoryRewards = historyReward.data.map(
              (item: any) => ({
                ...item,
                Status: TransactionStatus.success,
              }),
            );
            await historyRewardController.edit(updatedHistoryRewards);
          }
          return;
        }
        await this.historyRewardController.add([
          {
            Id: randomGID(),
            CustomerId: cusId,
            Value: missionByType.data[0]?.Value * (rankInfo?.Coefficient ?? 1),
            Description: `Thưởng Nhiệm vụ ${
              missionByType.data[0].Name
            } ${Ultis.numberToTime(new Date().getTime(), true)}`,
            Type: TransactionType.mission,
            DateCreated: new Date().getTime(),
            Code: Ultis.randomString(12),
            Status: status,
            Name: missionByType.data[0].Name,
          },
        ]);
        //#endregion
      }
    }

    return null;
  }

  //lấy % hoa hồng theo shop
  async getPercentRewardByShop(ShopId: string, categoryIds: string[]) {
    try {
      const result = [
        {
          categoryId: null,
          f0: 0,
          f1: 0,
          f2: 0,
        },
      ];
      // Sử dụng Promise.all để đợi tất cả async operations
      await Promise.all(
        categoryIds.map(async (item: any) => {
          const responseShopCate = await this.shopCateController.getListSimple({
            query: `@ShopId: {${ShopId}} @CategoryId: {${item}}`,
          });
          if (
            responseShopCate?.code === 200 &&
            responseShopCate?.data.length > 0
          ) {
            result.push({
              categoryId: item,
              f0:
                responseShopCate.data.find((item: any) => item.Filial === 0)
                  ?.Percent || 0,
              f1:
                responseShopCate.data.find((item: any) => item.Filial === 1)
                  ?.Percent || 0,
              f2:
                responseShopCate.data.find((item: any) => item.Filial === 2)
                  ?.Percent || 0,
            });
          } else {
            //lấy từ ShopReward
            const responseShopReward =
              await this.shopRewardController.getListSimple({
                query: `@ShopId: {${ShopId}}`,
              });
            if (
              responseShopReward?.code === 200 &&
              responseShopReward?.data.length > 0
            ) {
              result.push({
                categoryId: item,
                f0:
                  responseShopReward.data.find((item: any) => item.Filial === 0)
                    ?.Percent || 0,
                f1:
                  responseShopReward.data.find((item: any) => item.Filial === 1)
                    ?.Percent || 0,
                f2:
                  responseShopReward.data.find((item: any) => item.Filial === 2)
                    ?.Percent || 0,
              });
            } else {
              //lấy từ Reward
              const responseReward = await this.rewardController.getListSimple({
                query: `*`,
              });
              if (
                responseReward?.code === 200 &&
                responseReward?.data.length > 0
              ) {
                result.push({
                  categoryId: item,
                  f0:
                    responseReward.data.find((item: any) => item.Filial === 0)
                      ?.Percent || 0,
                  f1:
                    responseReward.data.find((item: any) => item.Filial === 1)
                      ?.Percent || 0,
                  f2:
                    responseReward.data.find((item: any) => item.Filial === 2)
                      ?.Percent || 0,
                });
              }
            }
          }
        }),
      );

      return result;
    } catch (error) {
      console.error('Error getting percent reward by shop:', error);
      return [];
    }
  }

  async getPointBonusFund({
    startDate,
    endDate,
    adminId,
  }: {
    startDate: number;
    endDate: number;
    adminId: string;
  }) {
    try {
      // Chỉ lấy danh sách giao dịch với Type là WithDrawMoney (quỹ trả thưởng)
      // DateCreated từ startDate đến endDate
      // CustomerId là id của admin
      const res = await this.historyRewardController.getListSimple({
        query: `@Type: [${TransactionType.WithDrawMoney}] @DateCreated: [${startDate} ${endDate}] @CustomerId: {${adminId}} @Status: [${TransactionStatus.success}]`,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        page: 1,
        size: 999999,
        returns: [
          'Id',
          'CustomerId',
          'Name',
          'Mobile',
          'Value',
          'Description',
          'DateCreated',
          'Status',
          'Type',
          'CustomerRecive',
          'Code',
        ],
      });

      if (res?.code === 200) {
        return {
          code: 200,
          data: res.data || [],
        };
      }

      return {
        code: res?.code || 500,
        data: [],
        message: res?.message || 'Failed to get bonus fund transactions',
      };
    } catch (error) {
      console.error('Error getting bonus fund transactions:', error);
      return {
        code: 500,
        data: [],
        message: 'Internal server error',
      };
    }
  }
}

export default WalletDA;
