import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import FastImage from '@d11/react-native-fast-image';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {navigate, RootScreen} from '../../../../router/router';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';

const ProgressSection = ({loading}: any) => {
  const {rankInfo} = useSelector((state: RootState) => state.customer);
  const [isVip, setIsVip] = useState(false);
  const [showBenefits, setShowBenefits] = useState(false);

  useEffect(() => {
    if (!rankInfo || !rankInfo?.Sort) setIsVip(false);
    else setIsVip(rankInfo?.Sort >= 3);
  }, [rankInfo]);

  if (loading) {
    return (
      <View style={styles.progressSection}>
        <Text style={styles.progressInfoValue}>...</Text>
      </View>
    );
  }

  // Render cho trường hợp chưa có hạng
  const renderNoRank = () => (
    <TouchableOpacity
      onPress={() => navigate(RootScreen.ProfileRankScreen)}
      style={styles.progressSection}>
      <View style={styles.headerRow}>
        <Text style={styles.rankTitle}>Chưa có hạng</Text>
        <TouchableOpacity
          style={[
            styles.upgradeButton,
            {
              backgroundColor: isVip
                ? '#FFC043'
                : ColorThemes.light.primary_main_color,
            },
          ]}
          onPress={() => navigate(RootScreen.ProfileRankScreen)}>
          <Text style={styles.upgradeButtonText}>Nâng hạng</Text>
        </TouchableOpacity>
      </View>
      <Text style={styles.description}>
        Nâng hạng ngay để hưởng quyền lợi độc quyền từ Chainivo
      </Text>
    </TouchableOpacity>
  );

  // Render cho trường hợp đã có hạng
  const renderWithRank = () => {
    // Lấy benefits từ Description và split theo dấu phẩy
    const benefits = (rankInfo as any)?.Description
      ? (rankInfo as any).Description.split(',')
          .map((benefit: string) => benefit.trim())
          .filter((benefit: string) => benefit.length > 0)
      : [];

    return (
      <TouchableOpacity
        onPress={() => navigate(RootScreen.ProfileRankScreen)}
        style={[
          styles.progressSectionWithRank,
          {
            borderColor: isVip
              ? '#FFC043'
              : ColorThemes.light.primary_border_color,
          },
        ]}>
        <View style={styles.headerRow}>
          <View style={styles.rankInfo}>
            <FastImage
              source={{uri: ConfigAPI.urlImg + rankInfo?.Icon}}
              style={styles.rankIcon}
              resizeMode="contain"
            />
            <Text style={styles.rankName}>{rankInfo?.Name}</Text>
          </View>
          <TouchableOpacity
            style={[
              styles.upgradeButton,
              {
                backgroundColor: isVip
                  ? '#FFC043'
                  : ColorThemes.light.primary_main_color,
              },
            ]}
            onPress={() => navigate(RootScreen.ProfileRankScreen)}>
            <Text style={styles.upgradeButtonText}>Nâng hạng</Text>
          </TouchableOpacity>
        </View>

        {benefits.length > 0 && (
          <View>
            <TouchableOpacity
              style={styles.viewBenefitsButton}
              onPress={() => setShowBenefits(!showBenefits)}>
              <Text style={styles.viewBenefitsText}>
                {showBenefits ? 'Ẩn quyền lợi' : 'Xem quyền lợi'}
              </Text>
            </TouchableOpacity>

            {showBenefits && (
              <View style={styles.benefitsContainer}>
                {benefits.map((benefit: string, index: number) => (
                  <View key={index} style={styles.benefitRow}>
                    <View style={styles.checkIcon}>
                      <Text style={styles.checkMark}>✓</Text>
                    </View>
                    <Text style={styles.benefitText}>{benefit}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return rankInfo ? renderWithRank() : renderNoRank();
};

const styles = StyleSheet.create({
  progressSection: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 20,
    marginHorizontal: 16,
    padding: 16,
    gap: 12,
    borderColor: ColorThemes.light.primary_border_color,
    borderWidth: 1,
  },
  progressSectionWithRank: {
    borderRadius: 20,
    marginHorizontal: 16,
    padding: 16,
    gap: 12,
    borderColor: ColorThemes.light.primary_border_color,
    borderWidth: 1,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rankTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  rankInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  rankIcon: {
    width: 32,
    height: 32,
  },
  rankName: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  upgradeButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
  },
  upgradeButtonText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: '600',
  },
  description: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    lineHeight: 20,
  },
  benefitsContainer: {
    gap: 8,
  },
  benefitRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.success_main_color,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkMark: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 10,
    fontWeight: 'bold',
  },
  benefitText: {
    ...TypoSkin.title4,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 1,
  },
  progressInfoValue: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  viewBenefitsButton: {
    ...TypoSkin.subtitle5,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
    marginTop: 10,
    alignSelf: 'flex-end',
  },
  viewBenefitsText: {
    ...TypoSkin.title5,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  arrowIcon: {
    ...TypoSkin.title5,
    color: ColorThemes.light.primary_main_color,
    fontSize: 12,
    transform: [{rotate: '0deg'}],
  },
  arrowIconRotated: {
    transform: [{rotate: '180deg'}],
  },
});

export default ProgressSection;
