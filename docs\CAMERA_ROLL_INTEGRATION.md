# Camera Roll Integration với @react-native-camera-roll/camera-roll

## Tổng quan

Dự án đã được cập nhật để sử dụng package `@react-native-camera-roll/camera-roll` thay vì `react-native-fs` để lưu ảnh vào thư viện ảnh của thiết bị. Package này cung cấp API tối ưu và tuân thủ các quy định bảo mật mới nhất của iOS và Android.

## Cài đặt

### 1. Package đã được cài đặt
```bash
npm install @react-native-camera-roll/camera-roll --save
```

### 2. <PERSON><PERSON>u hình iOS

Đã thêm vào `ios/wini_core_mobile/Info.plist`:
```xml
<key>NSPhotoLibraryUsageDescription</key>
<string>This app requires to access your photo library to show image on profile and send via chat</string>
<key>NSPhotoLibraryAddUsageDescription</key>
<string>This app requires to save QR code to your photo library</string>
```

### 3. <PERSON><PERSON><PERSON> <PERSON>ình Android

Đã cập nhật `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
```

### 4. iOS Pod Installation
```bash
cd ios && pod install
```

## Sử dụng

### CameraRollHelper Class

Đã tạo helper class `src/utils/CameraRollHelper.ts` để xử lý:
- Kiểm tra và xin quyền truy cập thư viện ảnh
- Lưu ảnh vào thư viện ảnh với xử lý lỗi
- Hiển thị dialog yêu cầu quyền

### Ví dụ sử dụng trong MyWallet component

```typescript
import { CameraRollHelper } from '../../utils/CameraRollHelper';

const downloadQRCode = async () => {
  try {
    if (viewShotRef.current && viewShotRef.current.capture) {
      const uri = await viewShotRef.current.capture();
      
      // Lưu ảnh vào thư viện ảnh với album tùy chọn
      const result = await CameraRollHelper.saveImage(uri, 'Chainivo');
      
      // Hiển thị thông báo thành công
      Alert.alert('Thành công!', 'QR Code đã được lưu vào thư viện ảnh');
    }
  } catch (error) {
    // Xử lý lỗi tự động
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('quyền')) {
      CameraRollHelper.showPermissionDialog();
    } else {
      Alert.alert('Lỗi', `Không thể lưu QR Code: ${errorMessage}`);
    }
  }
};
```

## Tính năng chính

### 1. Xử lý quyền tự động
- **Android 13+**: Tự động xin quyền `READ_MEDIA_IMAGES` và `READ_MEDIA_VIDEO`
- **Android cũ hơn**: Sử dụng `READ_EXTERNAL_STORAGE`
- **iOS**: Không cần xin quyền runtime cho việc lưu ảnh

### 2. Lưu ảnh với album tùy chọn
- iOS: Tạo album riêng với tên "Chainivo"
- Android: Lưu vào thư viện ảnh chính

### 3. Xử lý lỗi thông minh
- Tự động phát hiện lỗi quyền truy cập
- Hiển thị dialog phù hợp cho từng loại lỗi
- Log chi tiết để debug

## So sánh với phương pháp cũ

### Trước (sử dụng RNFS)
- Phức tạp trong việc xử lý đường dẫn file
- Cần xử lý riêng cho từng platform
- Không tự động cập nhật thư viện ảnh
- Khó khăn trong việc xin quyền

### Sau (sử dụng @react-native-camera-roll/camera-roll)
- API đơn giản và thống nhất
- Tự động xử lý platform differences
- Tự động cập nhật thư viện ảnh
- Xử lý quyền tự động và chính xác

## Lưu ý quan trọng

1. **iOS Album**: Tính năng tạo album riêng chỉ hoạt động trên iOS
2. **Android Permissions**: Cần xử lý khác nhau cho Android 13+ và các phiên bản cũ hơn
3. **File URI**: Chỉ hỗ trợ local file URI (file://)
4. **Performance**: Package này được tối ưu cho performance tốt hơn RNFS

## Troubleshooting

### Lỗi thường gặp

1. **"Không có quyền truy cập thư viện ảnh"**
   - Kiểm tra cấu hình permissions trong AndroidManifest.xml và Info.plist
   - Đảm bảo user đã cấp quyền trong Settings

2. **"URI không hợp lệ"**
   - Đảm bảo URI là local file URI (bắt đầu bằng file://)
   - Kiểm tra file có tồn tại trước khi lưu

3. **iOS build lỗi**
   - Chạy `cd ios && pod install` sau khi cài package
   - Clean build folder nếu cần thiết

## Testing

Để test chức năng:
1. Chạy app trên device (không hoạt động trên simulator cho camera roll)
2. Mở màn hình MyWallet
3. Nhấn "Tải xuống mã QR"
4. Kiểm tra thư viện ảnh để xác nhận ảnh đã được lưu
