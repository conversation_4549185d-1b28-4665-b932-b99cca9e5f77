import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Modal,
  FlatList,
} from 'react-native';
import {LineChart} from 'react-native-chart-kit';
import {ScrollView} from 'react-native-gesture-handler';
import {FLoading} from 'wini-mobile-components';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {OrderDA} from '../order/orderDA';
import {StatusOrder} from '../../Config/Contanst';
import {useTranslation} from '../../locales/useTranslation';

dayjs.extend(weekOfYear);
dayjs.extend(timezone);
dayjs.extend(utc);

const {width} = Dimensions.get('window');
const ChartProduct = () => {
  const {t} = useTranslation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [allOrderData, setAllOrderData] = useState<any[]>([]);
  const [weeklyData, setWeeklyData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);
  const [weeklyData2, setWeeklyData2] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0,
  ]);
  const [monthlyData, setMonthlyData] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const [monthlyData2, setMonthlyData2] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const [yearData, setYearData] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const [yearData2, setYearData2] = useState<number[]>([
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  ]);
  const shopInfo = useSelectorShopState().data;
  const orderDA = new OrderDA();
  const [selectChart, setSelectChart] = useState<string>('');
  const [showWeekPicker, setShowWeekPicker] = useState<boolean>(false);
  const [selectedWeek, setSelectedWeek] = useState(dayjs());
  const [weeksInYear, setWeeksInYear] = useState<dayjs.Dayjs[]>([]);

  const handleSelectChart = (type: string) => {
    setSelectChart(type);
  };

  const generateWeeksInYear = () => {
    const year = dayjs().year();
    const startOfYear = dayjs().year(year).startOf('year');
    const endOfYear = dayjs().year(year).endOf('year');
    const weeks: dayjs.Dayjs[] = [];
    let currentWeekStart = startOfYear.startOf('week');

    while (currentWeekStart.isBefore(endOfYear)) {
      // Ensure the week has at least one day in the current year
      if (
        currentWeekStart.year() === year ||
        currentWeekStart.add(6, 'days').year() === year
      ) {
        weeks.push(currentWeekStart);
      }
      currentWeekStart = currentWeekStart.add(1, 'week');
    }

    setWeeksInYear(weeks);

    const currentWeek = dayjs();
    const matchingWeek = weeks.find(w => currentWeek.isSame(w, 'week'));
    setSelectedWeek(matchingWeek || currentWeek);
  };

  // Function to format Y-axis labels with shortened values

  useEffect(() => {
    setSelectChart('month');
    generateWeeksInYear();
  }, []);

  const fetchAllOrderData = async () => {
    if (!shopInfo || !shopInfo[0]?.Id) {
      console.log('No shop info available');
      return;
    }

    setIsLoading(true);
    try {
      const res = await orderDA.getOrderByShopId(
        shopInfo[0]?.Id,
        StatusOrder.success,
      );

      if (res?.code === 200) {
        setAllOrderData(res.data);
      } else {
        console.log('Failed to fetch order data:', res);
      }
    } catch (error) {
      console.error('Error fetching all order data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (shopInfo.length > 0) {
      fetchAllOrderData();
    }
  }, [shopInfo]);

  useEffect(() => {
    if (allOrderData.length > 0) {
      getanalysisDateWeek(selectedWeek);
      if (selectChart === 'month') {
        getanalysisDateCurrentMonth();
      } else if (selectChart === 'year') {
        getanalysisDateYear();
      }
    }
  }, [allOrderData, selectChart, selectedWeek]);

  // Data for BarChart (Income vs Spend)
  const lineDataOne = {
    labels: [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ],
    datasets: [
      {
        data: weeklyData,
        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
        strokeWidth: 2,
      },
      {
        data: weeklyData2,
        color: (opacity = 1) => `rgba(255, 99, 132, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  // Data for LineChart (Month vs Year)
  const lineDataThree = {
    labels: (() => {
      const currentDate = dayjs();
      const daysInMonth = currentDate.daysInMonth();
      return Array.from({length: daysInMonth}, (_, i) => (i + 1).toString());
    })(),
    datasets: [
      {
        data: monthlyData, // Monthly data from API
        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
        strokeWidth: 2,
      },
      {
        data: monthlyData2, // Yearly data from API
        color: (opacity = 1) => `rgba(255, 99, 132, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  const lineDataTwo = {
    labels: [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    datasets: [
      {
        data: yearData, // Monthly data from API for the year
        color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
        strokeWidth: 2,
      },
      {
        data: yearData2, // Monthly commission data from API for the year
        color: (opacity = 1) => `rgba(255, 99, 132, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  let getanalysisDateWeek = async (week: dayjs.Dayjs) => {
    try {
      const currentDate = week;
      const startOfWeek = currentDate.startOf('week');
      const endOfWeek = currentDate.endOf('week');

      if (!shopInfo || !shopInfo[0]?.Id) {
        console.log('No shop info available');
        setIsLoading(false);
        return;
      }
      if (allOrderData.length === 0) {
        setIsLoading(false);
        return;
      }

      // lấy số tiền của đơn hàng
      let listOrderDetailId: string[] = [];
      let dataDay = allOrderData.filter((item: any) => {
        const itemDate = dayjs(item?.DateCreated);
        return (
          (itemDate.isAfter(startOfWeek.subtract(1, 'day')) ||
            itemDate.isSame(startOfWeek, 'day')) &&
          (itemDate.isBefore(endOfWeek.add(1, 'day')) ||
            itemDate.isSame(endOfWeek, 'day'))
        );
      });
      for (let item of dataDay) {
        listOrderDetailId.push(item?.Id);
      }

      // Initialize arrays for weekly data
      let newWeeklyData = [0, 0, 0, 0, 0, 0, 0];
      let newWeeklyData2 = [0, 0, 0, 0, 0, 0, 0];
      let dataMoney: any[] = [];

      if (listOrderDetailId.length > 0) {
        let res = await orderDA.getMoneyDetailsByListOrderDetailId(
          listOrderDetailId,
        );
        if (res?.code === 200) {
          // lấy số tiền hoa hồng của đơn hàng
          dataMoney = res?.data.filter((item: any) => {
            const itemDate = dayjs(item?.DateCreated);
            return (
              (itemDate.isAfter(startOfWeek.subtract(1, 'day')) ||
                itemDate.isSame(startOfWeek, 'day')) &&
              (itemDate.isBefore(endOfWeek.add(1, 'day')) ||
                itemDate.isSame(endOfWeek, 'day'))
            );
          });
        }
      }

      for (let item of dataDay) {
        // Get the day of week (0-6, where 0 is Sunday)
        const dayOfWeek = dayjs(item?.DateCreated).day();

        // Convert Sunday (0) to index 6, Monday (1) to index 0, etc.
        // This maps: Sunday=6, Monday=0, Tuesday=1, ..., Saturday=5
        const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

        const orderAmount = Number(item?.Total || 0);

        // Add the order amount to the corresponding day
        newWeeklyData[dayIndex] += orderAmount;
        //   newWeeklyData2[dayIndex] += orderAmount * 0.8; // Different calculation for second dataset
      }

      for (let item of dataMoney) {
        const dayOfWeek = dayjs(item?.DateCreated).day();
        const dayIndex = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        const orderCommission = Number(item?.Value || 0);
        newWeeklyData2[dayIndex] += orderCommission;
      }

      setWeeklyData(newWeeklyData);
      setWeeklyData2(newWeeklyData2);
    } catch (error) {
      console.error('Error in getanalysisDateWeek:', error);
    } finally {
      setIsLoading(false);
    }
  };

  let getanalysisDateCurrentMonth = async () => {
    try {
      const currentDate = dayjs().tz('Asia/Ho_Chi_Minh');
      const startOfMonth = currentDate.startOf('month');
      const endOfMonth = currentDate.endOf('month');

      if (!shopInfo || !shopInfo[0]?.Id) {
        console.log('No shop info available');
        return;
      }
      if (allOrderData.length === 0) {
        return;
      }

      // lấy số tiền của đơn hàng
      let listOrderDetailId: string[] = [];
      let dataCurrentMonth = allOrderData.filter((item: any) => {
        const itemDate = dayjs(item?.DateCreated);
        return (
          (itemDate.isAfter(startOfMonth.subtract(1, 'day')) ||
            itemDate.isSame(startOfMonth, 'day')) &&
          (itemDate.isBefore(endOfMonth.add(1, 'day')) ||
            itemDate.isSame(endOfMonth, 'day'))
        );
      });
      for (let item of dataCurrentMonth) {
        listOrderDetailId.push(item?.Id);
      }

      // Initialize arrays for current month data (days in current month)
      const daysInMonth = currentDate.daysInMonth();
      let newCurrentMonthData = new Array(daysInMonth).fill(0);
      let newCurrentMonthData2 = new Array(daysInMonth).fill(0);
      let dataMoney: any[] = [];

      if (listOrderDetailId.length > 0) {
        let res = await orderDA.getMoneyDetailsByListOrderDetailId(
          listOrderDetailId,
        );
        console.log('check-res', res);
        if (res?.code === 200) {
          // lấy số tiền hoa hồng của đơn hàng
          dataMoney = res?.data.filter((item: any) => {
            const itemDate = dayjs(item?.DateCreated);
            return (
              (itemDate.isAfter(startOfMonth.subtract(1, 'day')) ||
                itemDate.isSame(startOfMonth, 'day')) &&
              (itemDate.isBefore(endOfMonth.add(1, 'day')) ||
                itemDate.isSame(endOfMonth, 'day'))
            );
          });
        }
      }

      for (let item of dataCurrentMonth) {
        // Get the day of month (1-31)
        const dayOfMonth = dayjs(item?.DateCreated).date();
        const dayIndex = dayOfMonth - 1; // Convert to 0-based index

        const orderAmount = Number(item?.Total || 0);

        // Add the order amount to the corresponding day
        newCurrentMonthData[dayIndex] += orderAmount;
      }

      for (let item of dataMoney) {
        const dayOfMonth = dayjs(item?.DateCreated).date();
        const dayIndex = dayOfMonth - 1; // Convert to 0-based index
        newCurrentMonthData2[dayIndex] += Number(item?.Value || 0);
      }

      setMonthlyData(newCurrentMonthData);
      setMonthlyData2(newCurrentMonthData2);
    } catch (error) {
      console.error('Error in getanalysisDateCurrentMonth:', error);
    }
  };

  let getanalysisDateYear = async () => {
    try {
      const currentDate = dayjs();
      const startOfYear = currentDate.startOf('year');
      const endOfYear = currentDate.endOf('year');

      if (!shopInfo || !shopInfo[0]?.Id) {
        console.log('No shop info available');
        return;
      }

      if (allOrderData.length === 0) {
        return;
      }

      // lấy số tiền của đơn hàng
      let listOrderDetailId: string[] = [];
      let dataMonth = allOrderData.filter((item: any) => {
        const itemDate = dayjs(item?.DateCreated);
        return (
          (itemDate.isAfter(startOfYear.subtract(1, 'day')) ||
            itemDate.isSame(startOfYear, 'day')) &&
          (itemDate.isBefore(endOfYear.add(1, 'day')) ||
            itemDate.isSame(endOfYear, 'day'))
        );
      });
      for (let item of dataMonth) {
        listOrderDetailId.push(item?.Id);
      }

      // Initialize arrays for monthly data
      let newMonthlyData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      let newMonthlyData2 = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      let dataMoney: any[] = [];

      if (listOrderDetailId.length > 0) {
        let res = await orderDA.getMoneyDetailsByListOrderDetailId(
          listOrderDetailId,
        );
        if (res?.code === 200) {
          // lấy số tiền hoa hồng của đơn hàng
          dataMoney = res?.data.filter((item: any) => {
            const itemDate = dayjs(item?.DateCreated);
            return (
              (itemDate.isAfter(startOfYear.subtract(1, 'day')) ||
                itemDate.isSame(startOfYear, 'day')) &&
              (itemDate.isBefore(endOfYear.add(1, 'day')) ||
                itemDate.isSame(endOfYear, 'day'))
            );
          });
        }
      }

      console.log('check-dataMoney', dataMoney);

      for (let item of dataMonth) {
        // Get the month (0-11, where 0 is January)
        const yearIndex = dayjs(item?.DateCreated).month();

        const orderAmount =
          Number(item?.Quantity || 0) * Number(item?.Price || 0);

        // Add the order amount to the corresponding month
        newMonthlyData[yearIndex] += Number(item?.Total || 0);
      }

      for (let item of dataMoney) {
        const yearIndex = dayjs(item?.DateCreated).month();
        newMonthlyData2[yearIndex] += Number(item?.Value || 0);
      }
      setYearData(newMonthlyData);
      setYearData2(newMonthlyData2);
    } catch (error) {
      console.error('Error in getanalysisDateMonth:', error);
    }
  };

  const renderWeekItem = ({item}: {item: dayjs.Dayjs}) => (
    <TouchableOpacity
      style={styles.weekItem}
      onPress={() => {
        setSelectedWeek(item);
        setShowWeekPicker(false);
      }}>
      <Text style={styles.weekText}>
        {t('chart.week')} {item.week()} (
        {item.startOf('week').format('DD/MM/YYYY')} -{' '}
        {item.endOf('week').format('DD/MM/YYYY')})
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <FLoading visible={isLoading} />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => setShowWeekPicker(true)}>
          <Text style={styles.title}>
            {t('chart.week')} {selectedWeek.week()} (
            {selectedWeek.startOf('week').format('DD/MM/YYYY')} -
            {selectedWeek.endOf('week').format('DD/MM/YYYY')})
          </Text>
        </TouchableOpacity>
        <Modal
          transparent={true}
          visible={showWeekPicker}
          onRequestClose={() => setShowWeekPicker(false)}>
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPressOut={() => setShowWeekPicker(false)}>
            <View style={styles.modalContent}>
              <FlatList
                data={weeksInYear}
                renderItem={renderWeekItem}
                keyExtractor={(item, index) => index.toString()}
              />
            </View>
          </TouchableOpacity>
        </Modal>
        <View style={{flexDirection: 'column', marginTop: 10}}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <View
              style={{
                width: 20,
                height: 20,
                backgroundColor: '#007bff',
                marginRight: 10,
              }}></View>
            <Text>{t('chart.revenue')}</Text>
          </View>
          <View
            style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
            <View
              style={{
                width: 20,
                height: 20,
                backgroundColor: '#ff6384',
                marginRight: 10,
              }}></View>
            <Text>{t('chart.commission')}</Text>
          </View>
        </View>
      </View>
      <ScrollView
        style={styles.chartContainer}
        horizontal={true}
        showsHorizontalScrollIndicator={false}>
        <LineChart
          key={`weekly-chart-${weeklyData.join('-')}-${weeklyData2.join('-')}`}
          data={lineDataOne}
          width={700}
          height={300}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 8,
            },
            propsForDots: {
              r: '3',
              strokeWidth: '3',
              stroke: '#007bff',
            },
            fillShadowGradient: '#e6f0fa',
            fillShadowGradientOpacity: 0.7,
          }}
          style={{
            marginVertical: 8,
            borderRadius: 8,
          }}
          withInnerLines={true}
          yAxisLabel=""
          yAxisSuffix=""
          bezier
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withDots={true}
          withShadow={false}
          segments={5}
          decorator={() => {
            return weeklyData.map((value, index) => {
              const x = (700 / (weeklyData.length - 1)) * index + 50;
              const maxValue = Math.max(...weeklyData, ...weeklyData2);
              const y1 = 250 - (value / maxValue) * 180;
              const y2 = 250 - (weeklyData2[index] / maxValue) * 180;

              // Tính toán khoảng cách giữa 2 giá trị để tránh đè lên nhau
              const minDistance = 30; // Khoảng cách tối thiểu giữa 2 label
              let adjustedY1 = y1;
              let adjustedY2 = y2;

              // Nếu 2 giá trị quá gần nhau, điều chỉnh vị trí
              if (Math.abs(y1 - y2) < minDistance) {
                if (value > weeklyData2[index]) {
                  adjustedY1 = y1 - 15;
                  adjustedY2 = y1 + 15;
                } else {
                  adjustedY1 = y2 + 15;
                  adjustedY2 = y2 - 15;
                }
              }

              return (
                <View key={`decorator-weekly-${index}`}>
                  {/* Giá trị line đầu tiên (màu xanh) - Thu nhập - chỉ hiện khi có giá trị */}
                  {value > 0 && (
                    <Text
                      style={{
                        position: 'absolute',
                        left: x - 25,
                        top: adjustedY1 - 25,
                        fontSize: 10,
                        fontWeight: 'bold',
                        color: '#007bff',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        paddingHorizontal: 4,
                        paddingVertical: 2,
                        borderRadius: 4,
                        borderWidth: 1,
                        borderColor: '#007bff',
                        textAlign: 'center',
                        minWidth: 35,
                      }}>
                      {value >= 1000000
                        ? `${(value / 1000000).toFixed(1)}M`
                        : value >= 1000
                        ? `${(value / 1000).toFixed(1)}K`
                        : value.toString()}
                    </Text>
                  )}

                  {/* Giá trị line thứ hai (màu đỏ) - Hoa hồng - chỉ hiện khi có giá trị */}
                  {weeklyData2[index] > 0 && (
                    <Text
                      style={{
                        position: 'absolute',
                        left: x - 25,
                        top: adjustedY2 - 25,
                        fontSize: 10,
                        fontWeight: 'bold',
                        color: '#ff6384',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        paddingHorizontal: 4,
                        paddingVertical: 2,
                        borderRadius: 4,
                        borderWidth: 1,
                        borderColor: '#ff6384',
                        textAlign: 'center',
                        minWidth: 35,
                      }}>
                      {weeklyData2[index] >= 1000000
                        ? `${(weeklyData2[index] / 1000000).toFixed(1)}M`
                        : weeklyData2[index] >= 1000
                        ? `${(weeklyData2[index] / 1000).toFixed(1)}K`
                        : weeklyData2[index].toString()}
                    </Text>
                  )}
                </View>
              );
            });
          }}
          formatYLabel={value => {
            const numValue = parseFloat(value);
            if (numValue >= 1000000) {
              return `${(numValue / 1000000).toFixed(2)} M`;
            } else if (numValue >= 1000) {
              return `${(numValue / 1000).toFixed(2)} K`;
            }
            return numValue.toString();
          }}
        />
      </ScrollView>
      <View style={styles.headerTwo}>
        <TouchableOpacity onPress={() => handleSelectChart('month')}>
          <Text
            style={
              selectChart === 'month' ? styles.titleOneAction : styles.titleOne
            }>
            {t('chart.month')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => handleSelectChart('year')}>
          <Text
            style={
              selectChart === 'year' ? styles.titleTwoAction : styles.titleTwo
            }>
            {t('chart.year')}
          </Text>
        </TouchableOpacity>
      </View>
      <ScrollView
        style={styles.chartContainer}
        horizontal={true}
        showsHorizontalScrollIndicator={false}>
        <LineChart
          key={`monthly-chart-${selectChart}-${monthlyData.join(
            '-',
          )}-${monthlyData2.join('-')}`}
          data={selectChart === 'year' ? lineDataTwo : lineDataThree}
          width={1070}
          height={300}
          chartConfig={{
            backgroundColor: '#fff',
            backgroundGradientFrom: '#fff',
            backgroundGradientTo: '#fff',
            decimalPlaces: 0,
            color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
            labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
            style: {
              borderRadius: 8,
            },
            propsForDots: {
              r: '3',
              strokeWidth: '3',
              stroke: '#007bff',
            },
            fillShadowGradient: '#e6f0fa',
            fillShadowGradientOpacity: 0.7,
          }}
          style={{
            marginVertical: 8,
            borderRadius: 8,
          }}
          withInnerLines={true}
          yAxisLabel=""
          yAxisSuffix=""
          bezier
          withVerticalLabels={true}
          withHorizontalLabels={true}
          withDots={true}
          withShadow={false}
          segments={5}
          decorator={() => {
            const currentData = selectChart === 'year' ? yearData : monthlyData;
            const currentData2 =
              selectChart === 'year' ? yearData2 : monthlyData2;

            return currentData.map((value, index) => {
              const x = (1000 / (currentData.length - 1)) * index + 50;
              const maxValue = Math.max(...currentData, ...currentData2);
              const y1 = 250 - (value / maxValue) * 180;
              const y2 = 250 - (currentData2[index] / maxValue) * 180;

              // Tính toán khoảng cách giữa 2 giá trị để tránh đè lên nhau
              const minDistance = 30; // Khoảng cách tối thiểu giữa 2 label
              let adjustedY1 = y1;
              let adjustedY2 = y2;

              // Nếu 2 giá trị quá gần nhau, điều chỉnh vị trí
              if (Math.abs(y1 - y2) < minDistance) {
                if (value > currentData2[index]) {
                  adjustedY1 = y1 - 15;
                  adjustedY2 = y1 + 15;
                } else {
                  adjustedY1 = y2 + 15;
                  adjustedY2 = y2 - 15;
                }
              }

              return (
                <View key={`decorator-${index}`}>
                  {/* Giá trị line đầu tiên (màu xanh) - chỉ hiện khi có giá trị */}
                  {value > 0 && (
                    <Text
                      style={{
                        position: 'absolute',
                        left: x - 25,
                        top: adjustedY1 - 25,
                        fontSize: 10,
                        fontWeight: 'bold',
                        color: '#007bff',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        paddingHorizontal: 4,
                        paddingVertical: 2,
                        borderRadius: 4,
                        borderWidth: 1,
                        borderColor: '#007bff',
                        textAlign: 'center',
                        minWidth: 35,
                      }}>
                      {value >= 1000000
                        ? `${(value / 1000000).toFixed(1)}M`
                        : value >= 1000
                        ? `${(value / 1000).toFixed(1)}K`
                        : value.toString()}
                    </Text>
                  )}

                  {/* Giá trị line thứ hai (màu đỏ) - chỉ hiện khi có giá trị */}
                  {currentData2[index] > 0 && (
                    <Text
                      style={{
                        position: 'absolute',
                        left: x - 25,
                        top: adjustedY2 - 25,
                        fontSize: 10,
                        fontWeight: 'bold',
                        color: '#ff6384',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        paddingHorizontal: 4,
                        paddingVertical: 2,
                        borderRadius: 4,
                        borderWidth: 1,
                        borderColor: '#ff6384',
                        textAlign: 'center',
                        minWidth: 35,
                      }}>
                      {currentData2[index] >= 1000000
                        ? `${(currentData2[index] / 1000000).toFixed(1)}M`
                        : currentData2[index] >= 1000
                        ? `${(currentData2[index] / 1000).toFixed(1)}K`
                        : currentData2[index].toString()}
                    </Text>
                  )}
                </View>
              );
            });
          }}
          formatYLabel={value => {
            const numValue = parseFloat(value);
            if (numValue >= 1000000) {
              return `${(numValue / 1000000).toFixed(2)} M`;
            } else if (numValue >= 1000) {
              return `${(numValue / 1000).toFixed(2)} K`;
            }
            return numValue.toString();
          }}
        />
      </ScrollView>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    height: 700,
  },
  header: {
    flexDirection: 'column',
    alignContent: 'center',
    justifyContent: 'space-around',
    margin: 'auto',
    marginTop: 2,
  },
  headerTwo: {
    flexDirection: 'row',
    alignContent: 'center',
    justifyContent: 'space-around',
    margin: 'auto',
    marginTop: 2,
    paddingBottom: 30,
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    padding: 7,
    margin: 'auto',
    backgroundColor: 'blue',
    textAlign: 'center',
    color: 'white',
    borderRadius: 50,
    paddingHorizontal: 15,
    minWidth: 120,
    height: 'auto',
  },
  titleOne: {
    fontSize: 14,
    fontWeight: 'bold',
    borderRightWidth: 1,
    padding: 7,
    margin: 'auto',
    width: 119.3,
    height: 29.82,
    borderTopLeftRadius: 50,
    borderBottomLeftRadius: 50,
    textAlign: 'center',
    color: 'blue',
    backgroundColor: '#D9D9D9',
  },
  titleOneAction: {
    fontSize: 14,
    fontWeight: 'bold',
    borderRightWidth: 1,
    padding: 7,
    margin: 'auto',
    width: 119.3,
    height: 29.82,
    backgroundColor: 'blue',
    borderTopLeftRadius: 50,
    borderBottomLeftRadius: 50,
    textAlign: 'center',
    color: 'white',
  },
  titleTwo: {
    fontSize: 14,
    fontWeight: 'bold',
    padding: 7,
    margin: 'auto',
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    textAlign: 'center',
    color: 'blue',
    backgroundColor: '#D9D9D9',
    width: 119.3,
    height: 29.82,
  },
  titleTwoAction: {
    fontSize: 14,
    fontWeight: 'bold',
    padding: 7,
    margin: 'auto',
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    textAlign: 'center',
    color: 'white',
    backgroundColor: 'blue',
    width: 119.3,
    height: 29.82,
  },
  chartContainer: {
    padding: 10,
    paddingBottom: 100,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '60%',
  },
  weekItem: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  weekText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default ChartProduct;
