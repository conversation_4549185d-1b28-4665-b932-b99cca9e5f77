import {createAsyncThunk} from '@reduxjs/toolkit';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {DataController} from '../../base/baseController';
import {RootState} from '../store/store';
import {FavoriteProduct} from '../models/favoriteProduct';
import {getProduct} from './productAction';
import {randomGID} from '../../utils/Utils';

export const favoriteProductAction = {
  create: async (data: {
    ProductId: string;
    CustomerId: string;
    Name: string;
  }) => {
    const controller = new DataController('ProductFavorite');
    try {
      const params = {
        Id: randomGID(),
        DateCreated: Date.now(),
        ...data,
      };
      const res = await controller.add([params]);
      if (res?.code === 200) {
        return res.data;
      }
      return null;
    } catch (error: any) {
      const errorMessage = error.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return null;
    }
  },
};

const getFavoriteProducts = async (
  config: any,
  thunkAPI: any,
): Promise<{data: FavoriteProduct[]; totalCount: number}> => {
  const controller = new DataController('ProductFavorite');
  const customerId = thunkAPI.getState().customer.data?.Id;
  const res = await controller.aggregateList({
    page: config?.page ?? 1,
    size: config?.size ?? 10,
    searchRaw: `@CustomerId:{${customerId}} ${config?.search ?? ''}`,
    sortby: [{prop: 'DateCreated', direction: 'DESC'}],
  });
  if (res?.code === 200) {
    const data = res.data;

    if (data.length === 0) {
      return {data: [], totalCount: res.totalCount};
    }

    const productIds = data.map((item: any) => item.ProductId).filter(Boolean);

    if (productIds.length === 0) {
      return {data: [], totalCount: res.totalCount};
    }

    const listProduct = await getProduct(productIds);

    const validData = data.filter((item: any) => {
      if (!item.ProductId) {
        return false;
      }

      item.Product = listProduct.find(
        (product: any) => product.Id === item.ProductId,
      );

      if (item.Product) {
        item.Product.IsFavorite = true;
        return true;
      } else {
        return false;
      }
    });

    return {data: validData, totalCount: res.totalCount};
  }
  return {data: [], totalCount: 0};
};

const deleteFavoriteProduct = async (ids: string[]) => {
  const controller = new DataController('ProductFavorite');
  const res = await controller.delete(ids);
  return res;
};

const fetchFavoriteProducts = createAsyncThunk<
  {data: FavoriteProduct[]; totalCount: number},
  {page?: number; size?: number; search?: string} | undefined,
  {state: RootState}
>(
  'favoriteProduct/fetchFavoriteProducts',
  async (config: any, thunkAPI: any) => {
    try {
      const data = await getFavoriteProducts(config, thunkAPI);
      return {data: data.data, totalCount: data.totalCount};
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred';
      console.error('Error fetching favorite products:', errorMessage);
      return {data: [], totalCount: 0};
    }
  },
);

const loadmoreFavoriteProducts = createAsyncThunk<
  FavoriteProduct[],
  {page?: number; size?: number; search?: string} | undefined,
  {state: RootState}
>(
  'favoriteProduct/loadmoreFavoriteProducts',
  async (config: any, thunkAPI: any): Promise<FavoriteProduct[]> => {
    try {
      const data = await getFavoriteProducts(config, thunkAPI);
      if (!data) {
        return [];
      }
      return data.data;
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred';
      console.error('Error fetching favorite products:', errorMessage);
      return [];
    }
  },
);

const unFavoriteProduct = createAsyncThunk<any, {id: string} | undefined>(
  'favoriteProduct/unFavoriteProduct',
  async (config: any) => {
    try {
      await deleteFavoriteProduct([config.id]).then((value: any) => {
        if (value.code === 200) {
          showSnackbar({
            message: 'Đã bỏ yêu thích',
            status: ComponentStatus.SUCCSESS,
          });
          return config.id;
        }
        return null;
      });
    } catch (err: any) {
      const errorMessage = err.message || 'An unknown error occurred';
      showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
      return null;
    }
  },
);

export {fetchFavoriteProducts, loadmoreFavoriteProducts, unFavoriteProduct};
