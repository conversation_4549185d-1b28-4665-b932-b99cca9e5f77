import React from 'react';
import {View, Text, StyleSheet, FlatList} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {Ultis} from '../../../../utils/Utils';
import TransactionItem from './TransactionItem';

export interface HistoryItem {
  id: number | string;
  type: 'income' | 'expense';
  amount: number;
  description?: string;
  time: string;
}

interface Props {
  data: HistoryItem[];
  onPressViewAll: () => void;
}

export const HistorySection: React.FC<Props> = ({data, onPressViewAll}) => {
  if (!data || data.length === 0) return null;
  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={styles.title}>Lịch sử giao dịch</Text>
        <Text style={styles.viewAll} onPress={onPressViewAll}>
          Xem tất cả
        </Text>
      </View>
      <FlatList
        data={data}
        renderItem={({item}) => <TransactionItem item={item} />}
        keyExtractor={item => item.id.toString()}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
  },
  viewAll: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
  item: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_background_color,
  },
  icon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {flex: 1},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  amount: {fontSize: 16, fontWeight: '600'},
  description: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
    lineHeight: 20,
  },
  time: {fontSize: 12, color: ColorThemes.light.neutral_text_subtitle_color},
});

export default HistorySection;
