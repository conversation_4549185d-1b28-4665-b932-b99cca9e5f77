import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {navigateBack} from '../../../router/router';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import {FLoading, FPopup} from 'wini-mobile-components';
import {useProfileRank} from './hooks/useProfileRank';
import {StatusCard} from './components/StatusCard';
import {RankingSection} from './components/RankingSection';

const ProfileRankScreen = () => {
  const popupRef = React.useRef<any>(null);
  const {ranksData, currentRankInfo, loading} = useProfileRank();

  return (
    <View style={styles.container}>
      <FPopup ref={popupRef} />
      <FLoading visible={loading} />
      {/* Header */}
      <InforHeader title="Điểm của bạn" onBack={navigateBack} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Status Card */}
        <StatusCard currentRankInfo={currentRankInfo} />

        {/* Ranking Section */}
        <RankingSection
          ranksData={ranksData}
          popupRef={popupRef}
          currentRankInfo={currentRankInfo}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  scrollView: {
    flex: 1,
    marginHorizontal: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});

export default ProfileRankScreen;
