import {useState, useRef, useCallback, useMemo, useEffect} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {
  showSnackbar,
  ComponentStatus,
  showBottomSheet,
  hideBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import CustomerBottomSheet from '../../../../../components/CustomerBottomSheet';
import {ColorThemes} from '../../../../../assets/skin/colors';
import {useSelectorCustomerState} from '../../../../../redux/hook/customerHook';
import {DataController} from '../../../../../base/baseController';
import {randomGID, Ultis} from '../../../../../utils/Utils';
import {
  FeeTransaction,
  TransactionStatus,
  TransactionType,
} from '../../../../../Config/Contanst';
import {CustomerActions} from '../../../../../redux/reducers/CustomerReducer';
import WalletDA from '../../../../../modules/wallet/walletDa';
import ConfigAPI from 'Config/ConfigAPI';

export interface TransferCANPointHookReturn {
  // State
  currentStep: number;
  transferAmount: string;
  recipientName: string;
  recipientPhone: string;
  recipientId: string;
  otpValue: string;
  isVerifying: boolean;
  transactionData: any;
  currentPoints: number;
  loading: boolean;
  customer: any;
  type: string;

  // Refs
  pagerRef: React.RefObject<any>;
  customerBottomSheetRef: React.RefObject<any>;

  // Handlers
  handleBack: () => void;
  handleSelectRecipient: () => void;
  handleStep1Next: () => void;
  handleOTPComplete: (otp: string) => void;
  handleResendOTP: () => void;
  handleVerifyOTP: (otp: string) => Promise<void>;
  handleDone: () => void;
  setTransferAmount: (amount: string) => void;
  setRecipientName: (name: string) => void;
  setRecipientPhone: (phone: string) => void;
  setRecipientId: (id: string) => void;
}

export const useTransferCANPoint = (): TransferCANPointHookReturn => {
  const walletDa = new WalletDA();
  const navigation = useNavigation();
  const pagerRef = useRef<any>(null);
  const customerBottomSheetRef = useRef<any>(null);
  const customer = useSelectorCustomerState().data;
  const route = useRoute<any>();
  const type = route?.params?.Type;
  const dispatch = useDispatch<any>();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [transferAmount, setTransferAmount] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [recipientPhone, setRecipientPhone] = useState('');
  const [recipientId, setRecipientId] = useState('');
  const [otpValue, setOtpValue] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [transactionData, setTransactionData] = useState<any>(null);
  const currentPoints = useSelectorCustomerState().point;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    CustomerActions.getPoint(dispatch);
  }, []);

  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      pagerRef.current?.setPage(prevStep - 1);
    } else {
      navigation.goBack();
    }
  }, [currentStep, navigation]);

  const handleSelectRecipient = () => {
    showBottomSheet({
      ref: customerBottomSheetRef,
      title: 'Chọn người nhận',
      prefixAction: <View />,
      suffixAction: (
        <TouchableOpacity
          onPress={() => hideBottomSheet(customerBottomSheetRef)}
          style={{padding: 6, alignItems: 'center'}}>
          <Winicon
            src="outline/layout/xmark"
            size={20}
            color={ColorThemes.light.neutral_text_title_color}
          />
        </TouchableOpacity>
      ),
      children: (
        <CustomerBottomSheet
          ref={customerBottomSheetRef}
          selectedCustomerId={recipientId}
          onSelectCustomer={customer => {
            setRecipientId(customer.Id);
            setRecipientName(customer.Name);
            setRecipientPhone(customer.Mobile || '');
          }}
        />
      ),
    });
  };

  const handleStep1Next = useCallback((totalTranfer?: number) => {
    setCurrentStep(2);
    setTimeout(() => {
      if (pagerRef?.current) pagerRef.current?.setPage(1);
    }, 100);
  }, []);

  const handleOTPComplete = useCallback((otp: string) => {
    setOtpValue(otp);
  }, []);

  const handleResendOTP = useCallback(() => {
    setOtpValue('');
    showSnackbar({
      message: 'Đã gửi lại mã OTP',
      status: ComponentStatus.SUCCSESS,
    });
  }, []);

  const amount = useMemo(() => {
    return parseFloat(transferAmount || '0');
  }, [transferAmount]);

  // phí
  const feeTotal = useMemo(() => {
    return (amount / 100) * FeeTransaction.feeTransfer;
  }, [transferAmount]);

  // tổng chuyển + phí
  const totalTransferWithFee = useMemo(() => {
    return amount + feeTotal;
  }, [transferAmount, feeTotal]);

  // chuyển point nhận được
  const totalReceive = useMemo(() => {
    return amount - (amount / 100) * FeeTransaction.feeReceiver;
  }, [transferAmount]);

  const handleVerifyOTP = async (otp: string) => {
    setIsVerifying(true);
    if (!customer?.Id) return;
    // for tranfer type.
    const amount =
      type === TransactionType.tranfer
        ? totalReceive
        : parseInt(transferAmount, 10);
    if (type === TransactionType.tranfer) {
      const ADMIN_NAME = 'CHAINIVOADM';
      const ADMIN_MOBILE = '';

      await new WalletDA().transferPoints({
        senderId: customer.Id,
        senderName: customer.Name,
        senderMobile: customer.Mobile,
        recipientId: ConfigAPI.adminCHAINIVO,
        recipientName: ADMIN_NAME,
        recipientMobile: ADMIN_MOBILE,
        amount: feeTotal, // phí
        type: TransactionType.tranfer,
        status: TransactionStatus.success,
        descriptionSender: `Chuyển Point nội bộ`,
        descriptionRecipient: `Nhận Point nội bộ giao dịch từ ${customer.Name}`,
      });
    }

    const result = await walletDa.transferCustomPoint({
      senderId: customer?.Id,
      senderName: customer?.Name,
      senderMobile: customer?.Mobile,
      recipientId,
      recipientName,
      recipientMobile: recipientPhone,
      receiverAmount: amount,
      senderAmount: totalTransferWithFee,
      type,
      status: TransactionStatus.success,
      descriptionSender: `Chuyển Point đến ${recipientName}`,
      descriptionRecipient: `Nhận Point từ ${customer?.Name}`,
    });

    if (result?.code === 200) {
      setIsVerifying(false);
      setTransactionData(result.transactionData);
      setCurrentStep(3);
      pagerRef.current?.setPage(2);
      showSnackbar({
        message:
          type === TransactionType.tranfer
            ? 'Chuyển Point thành công!'
            : 'Rút Point thành công!',
        status: ComponentStatus.SUCCSESS,
      });
      dispatch(CustomerActions.getInfor());
    } else {
      setIsVerifying(false);
      showSnackbar({
        message:
          type === TransactionType.tranfer
            ? 'Chuyển Point thất bại!'
            : 'Rút Point thất bại!',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const handleDone = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  return {
    // State
    currentStep,
    transferAmount,
    recipientName,
    recipientPhone,
    recipientId,
    otpValue,
    isVerifying,
    transactionData,
    currentPoints,
    loading,
    customer,
    type,

    // Refs
    pagerRef,
    customerBottomSheetRef,

    // Handlers
    handleBack,
    handleSelectRecipient,
    handleStep1Next,
    handleOTPComplete,
    handleResendOTP,
    handleVerifyOTP,
    handleDone,
    setTransferAmount,
    setRecipientName,
    setRecipientPhone,
    setRecipientId,
  };
};
