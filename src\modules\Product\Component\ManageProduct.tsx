import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {RootScreen} from '../../../router/router';
import {
  Title,
  TransactionStatus,
  TransactionType,
} from '../../../Config/Contanst';
import MenuProduct from './MenuProduct';
import ManageProductDetail from '../list/ListManageProductDetail';
import {ManageProductStyles} from '../styles/ManageProductStyles';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {
  ComponentStatus,
  FDialog,
  showDialog,
  showSnackbar,
} from 'wini-mobile-components';
import RankConfigDa from '../../configRank/da/rankConfigDa';
import {DataController} from 'base/baseController';
import ConfigAPI from 'Config/ConfigAPI';
const ManageItemProduct = ({data}: {data: any}) => {
  const navigation = useNavigation<any>();
  const productInfo = data;
  const [menu, setMenu] = useState<string>('Còn hàng');
  const dialogRef = useRef<any>(null);
  const customerRank = useSelectorCustomerState().rankInfo;
  const [currentRank, setCurrentRank] = useState<any>();
  const [ranks, setRanks] = useState<any>([]);
  const rankDA = new RankConfigDa();

  useEffect(() => {
    if (customerRank) {
      setCurrentRank(customerRank);
    }
  }, [customerRank]);

  useEffect(() => {
    getRanks();
  }, []);

  const getRanks = async () => {
    const res = await rankDA.fetchAll();
    if (res) {
      setRanks(res);
    }
  };

  return (
    <View style={ManageProductStyles.containerScreenManageProduct}>
      <FDialog ref={dialogRef} />
      <MenuProduct setMenu={setMenu} menu={menu} data={productInfo} />
      <ManageProductDetail menu={menu} dataShop={productInfo} />
      <TouchableOpacity
        style={ManageProductStyles.ScreenManageProductButton}
        onPress={async () => {
          // check customer theo số hạng
          // check số hạng có quyền thêm sản phẩm
          let rankSort = ranks.sort(
            (a: any, b: any) => a.ProductLimit - b.ProductLimit,
          );
          const avaiableRank = rankSort?.find(
            (item: any) => item.ProductLimit > 0,
          );
          // nếu rank nhỏ hơn thì không cho tạo sản phẩm
          if (
            currentRank?.ProductLimit == undefined ||
            currentRank?.ProductLimit == 0
          ) {
            showDialog({
              ref: dialogRef,
              status: ComponentStatus.WARNING,
              title: 'Bạn không đủ số hạng để tạo sản phẩm',
              content: `Vui lòng nâng cấp số hạng để tạo sản phẩm. Số hạng từ ${avaiableRank?.Name} trở lên.`,
              onSubmit: async () => {},
            });
            return;
          }
          // nếu như đạt rank thì check số lượng sản phẩm đã tạo có vượt quá số lượng cho phép không
          if (currentRank?.ProductLimit <= productInfo.length) {
            showDialog({
              ref: dialogRef,
              status: ComponentStatus.WARNING,
              title: 'Bạn đã đạt số lượng sản phẩm tối đa',
              content: `Bạn đã đạt số lượng sản phẩm tối đa theo số hạng. Vui lòng xóa sản phẩm để tạo mới hoặc nâng cấp số hạng.`,
              onSubmit: async () => {},
            });
            return;
          }

          navigation.push(RootScreen.CreateNewProduct, {
            title: Title.CreateMyProduct,
          });
        }}>
        <Text style={ManageProductStyles.ScreenManageProductTextButton}>
          Thêm sản phẩm mới
        </Text>
      </TouchableOpacity>
    </View>
  );
};
export default ManageItemProduct;
