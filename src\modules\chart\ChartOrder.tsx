import React, {useEffect, useState} from 'react';
import {View, Text, Dimensions, StyleSheet} from 'react-native';
import {Pie<PERSON>hart} from 'react-native-chart-kit';
import {ScrollView} from 'react-native-gesture-handler';
import {FLoading} from 'wini-mobile-components';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import {useSelectorOrderState} from '../../redux/hook/orderHook ';
import CustomDropdown from '../../components/CustomDropdown';
import {useTranslation} from '../../locales/useTranslation';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

interface PieChartItem {
  name: string;
  value: number;
  color: string;
  legendFontColor: string;
  legendFontSize: number;
}

// Get screen width for responsive chart size
const screenWidth = Dimensions.get('window').width;
const ChartOrder = () => {
  const {t} = useTranslation();
  const OrderInfo = useSelectorOrderState().data;
  const [percent, setPercent] = useState<any>({
    CancelOrder: 0,
    DoneOrder: 0,
    NewOrder: 0,
    ProcessOrder: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedTime, setSelectedTime] = useState<string | number>(
    'last7days',
  );
  const [filteredData, setFilteredData] = useState<PieChartItem[]>([]);

  const timeFilterData = [
    {id: 'last7days', name: t('chart.last7Days')},
    {id: 'last1m', name: t('chart.last1Month')},
    {id: 'last3m', name: t('chart.last3Months')},
    {id: 'last6m', name: t('chart.last6Months')},
    {id: 'last1y', name: t('chart.last1Year')},
  ];
  // Sample data for the pie chart
  let data = [
    {
      name: t('chart.cancelledOrders'),
      value: OrderInfo?.CancelOrder?.number,
      color: '#FF6347', // Tomato
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
    {
      name: t('chart.completedOrders'),
      value: OrderInfo?.DoneOrder?.number,
      color: '#4682B4', // SteelBlue
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
    {
      name: t('chart.newOrders'),
      value: OrderInfo?.NewOrder?.number,
      color: '#FFD700', // Gold
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
    {
      name: t('chart.processingOrders'),
      value: OrderInfo?.ProcessOrder?.number,
      color: '#6A5ACD', // SlateBlue
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
  ];

  useEffect(() => {
    const filterDataByTime = (orders: any[]) => {
      console.log('check-orders', orders);
      if (!Array.isArray(orders)) return 0;

      const now = dayjs();
      let startDate: dayjs.Dayjs;

      switch (selectedTime) {
        case 'last7days':
          startDate = now.subtract(7, 'day');
          break;
        case 'last1m':
          startDate = now.subtract(1, 'month');
          break;
        case 'last3m':
          startDate = now.subtract(3, 'month');
          break;
        case 'last6m':
          startDate = now.subtract(6, 'month');
          break;
        case 'last1y':
          startDate = now.subtract(1, 'year');
          break;
        default:
          return 0;
      }

      return orders.filter(order => {
        console.log('check-startDate ', startDate?.format('YYYY-MM-DD'));
        console.log('check-now ', now?.format('YYYY-MM-DD'));
        if (!order?.DateCreated) {
          return false;
        }
        const orderDate = dayjs(order.DateCreated);
        return (
          orderDate.isSameOrAfter(startDate, 'day') &&
          orderDate.isSameOrBefore(now)
        );
      }).length;
    };

    const newFilteredData = [
      {
        ...data[0],
        value: filterDataByTime(OrderInfo?.CancelOrder?.data) || 0,
      },
      {
        ...data[1],
        value: filterDataByTime(OrderInfo?.DoneOrder?.data) || 0,
      },
      {
        ...data[2],
        value: filterDataByTime(OrderInfo?.NewOrder?.data) || 0,
      },
      {
        ...data[3],
        value: filterDataByTime(OrderInfo?.ProcessOrder?.data) || 0,
      },
    ];
    setFilteredData(newFilteredData);
  }, [selectedTime, OrderInfo]);

  useEffect(() => {
    let cancelNumber = 0;
    let doneNumber = 0;
    let newNumber = 0;
    let processNumber = 0;

    if (filteredData.length > 0) {
      cancelNumber = filteredData[0]?.value || 0;
      doneNumber = filteredData[1]?.value || 0;
      newNumber = filteredData[2]?.value || 0;
      processNumber = filteredData[3]?.value || 0;
    } else {
      cancelNumber = OrderInfo?.CancelOrder?.number || 0;
      doneNumber = OrderInfo?.DoneOrder?.number || 0;
      newNumber = OrderInfo?.NewOrder?.number || 0;
      processNumber = OrderInfo?.ProcessOrder?.number || 0;
    }

    const all = cancelNumber + doneNumber + newNumber + processNumber;

    setPercent({
      CancelOrder: all > 0 ? Math.round((cancelNumber / all) * 100) : 0,
      DoneOrder: all > 0 ? Math.round((doneNumber / all) * 100) : 0,
      NewOrder: all > 0 ? Math.round((newNumber / all) * 100) : 0,
      ProcessOrder: all > 0 ? Math.round((processNumber / all) * 100) : 0,
    });
  }, [OrderInfo, filteredData]);

  return (
    <ScrollView>
      <FLoading visible={isLoading} />
      <View>
        <CustomDropdown
          options={timeFilterData}
          value={selectedTime}
          onValueChange={setSelectedTime}
        />
      </View>
      <View
        style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'white',
        }}>
        <PieChart
          data={filteredData ? filteredData : data}
          width={screenWidth - 40} // Responsive width
          height={180}
          chartConfig={{
            color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`, // Default color for text/labels
          }}
          accessor="value" // Key for the data values
          backgroundColor="transparent"
          paddingLeft="15"
          absolute // Shows absolute values on the chart
        />
      </View>
      <View>
        <Text style={styles.headerText}>{t('chart.details')}</Text>
      </View>
      <ScrollView>
        <View style={styles.containerItem}>
          <View style={styles.content}>
            {/* Left side - Percentage with circle */}
            <View style={styles.leftSection}>
              <View style={styles.circle} />
              <Text style={styles.percentage}>{percent.CancelOrder}%</Text>
            </View>

            {/* Right side - Text and Amount */}
            <View style={styles.rightSection}>
              <Text style={styles.documentText}>
                {t('chart.cancelledOrders')}
              </Text>
              <Text style={styles.amount}>
                {filteredData.length > 0 ? filteredData[0].value : 0}{' '}
                {t('chart.orders')}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.containerItem}>
          <View style={styles.content}>
            {/* Left side - Percentage with circle */}
            <View style={styles.leftSection}>
              <View style={styles.circleTwo} />
              <Text style={styles.percentage}>{percent.DoneOrder}%</Text>
            </View>

            {/* Right side - Text and Amount */}
            <View style={styles.rightSection}>
              <Text style={styles.documentText}>
                {t('chart.completedOrders')}
              </Text>
              <Text style={styles.amount}>
                {filteredData.length > 0 ? filteredData[1].value : 0}{' '}
                {t('chart.orders')}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.containerItem}>
          <View style={styles.content}>
            {/* Left side - Percentage with circle */}
            <View style={styles.leftSection}>
              <View style={styles.circleThree} />
              <Text style={styles.percentage}>{percent.NewOrder}%</Text>
            </View>

            {/* Right side - Text and Amount */}
            <View style={styles.rightSection}>
              <Text style={styles.documentText}>{t('chart.newOrders')}</Text>
              <Text style={styles.amount}>
                {filteredData.length > 0 ? filteredData[2].value : 0}{' '}
                {t('chart.orders')}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.containerItem}>
          <View style={styles.content}>
            {/* Left side - Percentage with circle */}
            <View style={styles.leftSection}>
              <View style={styles.circleFour} />
              <Text style={styles.percentage}>{percent.ProcessOrder}%</Text>
            </View>

            {/* Right side - Text and Amount */}
            <View style={styles.rightSection}>
              <Text style={styles.documentText}>
                {t('chart.processingOrders')}
              </Text>
              <Text style={styles.amount}>
                {filteredData.length > 0 ? filteredData[3].value : 0}{' '}
                {t('chart.orders')}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderWidth: 1,
    height: 87,
  },
  containerItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 15,
    marginLeft: 16,
    marginRight: 16,
    marginBottom: 14,
    marginTop: 13,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5, // For Android shadow
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },

  headerText: {
    color: '#8F90FF', // Purple color
    fontSize: 18,
    fontWeight: '500',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  leftSection: {
    flex: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  circle: {
    width: 27,
    height: 27,
    backgroundColor: '#EF4444', // Red color
    borderRadius: 50,
    marginRight: 5,
  },
  circleTwo: {
    width: 27,
    height: 27,
    backgroundColor: '#370665',
    borderRadius: 50,
    marginRight: 5,
  },
  circleThree: {
    width: 27,
    height: 27,
    backgroundColor: '#35589A',
    borderRadius: 50,
    marginRight: 5,
  },
  circleFour: {
    width: 27,
    height: 27,
    backgroundColor: '#00952A',
    borderRadius: 50,
    marginRight: 5,
  },
  percentage: {
    color: '#2563EB', // Blue color
    fontSize: 20,
    fontWeight: 'bold',
  },
  rightSection: {
    flex: 0.7,
    marginLeft: 20,
  },
  documentText: {
    color: '#2563EB', // Blue color
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  amount: {
    color: '#2563EB', // Blue color
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ChartOrder;
