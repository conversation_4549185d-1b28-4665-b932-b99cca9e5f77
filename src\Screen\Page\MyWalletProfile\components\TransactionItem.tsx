import {ColorThemes} from 'assets/skin/colors';
import {View, Text, StyleSheet} from 'react-native';
import {Ultis} from 'utils/Utils';
import {Winicon} from 'wini-mobile-components';
import {HistoryItem} from './HistorySection';

const TransactionItem = ({item}: {item: HistoryItem}) => {
  const isIncome = item.type === 'income';
  return (
    <View style={styles.item}>
      <View style={[styles.icon, {backgroundColor: '#141316'}]}>
        <Winicon
          src={
            isIncome
              ? 'outline/arrows/arrow-bottom-left'
              : 'fill/arrows/arrow-top-right'
          }
          size={12}
          color={
            isIncome
              ? ColorThemes.light.success_main_color
              : ColorThemes.light.error_main_color
          }
          style={{
            backgroundColor: isIncome
              ? ColorThemes.light.success_background
              : ColorThemes.light.warning_border_color,
            padding: 12,
            borderRadius: 4,
          }}
        />
      </View>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text
            style={[
              styles.amount,
              {
                color: isIncome
                  ? ColorThemes.light.secondary2_main_color
                  : ColorThemes.light.error_main_color,
              },
            ]}>
            {isIncome ? '+' : ''}
            {Ultis.money(item.amount)} POINT
          </Text>
        </View>
        {!!item.description && (
          <Text style={styles.description} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        <Text style={styles.time}>{item.time}</Text>
      </View>
    </View>
  );
};

export default TransactionItem;

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
  },
  viewAll: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
  item: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_background_color,
  },
  icon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {flex: 1},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  amount: {fontSize: 16, fontWeight: '600'},
  description: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
    lineHeight: 20,
  },
  time: {fontSize: 12, color: ColorThemes.light.neutral_text_subtitle_color},
});
