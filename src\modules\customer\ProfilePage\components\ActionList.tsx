import React from 'react';
import {View, StyleSheet} from 'react-native';
import {
  ListTile,
  Winicon,
  showDialog,
  ComponentStatus,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {navigate, RootScreen} from '../../../../router/router';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {useTranslation} from '../../../../locales/useTranslation';

const actionList = [
  {
    id: 0,
    name: 'profile.account',
    icon: 'fill/users/profile',
    route: RootScreen.SettingProfile,
  },
  {
    id: 3,
    name: 'profile.biometricSetting',
    icon: 'fill/technology/face-recognition',
    route: RootScreen.BiometricSetting,
  },
  {
    id: 16,
    name: 'profile.accountVerification',
    icon: 'fill/user interface/verified',
    route: RootScreen.AccountAuth,
  },
  {
    id: 4,
    name: 'profile.twoFactorAuth',
    icon: 'fill/technology/lock-portrait',
    route: RootScreen.TwoFactorAuth,
  },
  {
    id: 6,
    name: 'profile.favoriteProducts',
    icon: 'outline/user interface/favorite',
    route: RootScreen.FavoriteProduct,
  },
  {
    id: 8,
    name: 'profile.shippingInfo',
    icon: 'fill/location/map-marker',
    route: RootScreen.MyAddress,
  },
  {
    id: 10,
    name: 'profile.language',
    show: true,
    action: 'language',
    icon: 'fill/text/text',
    route: null,
  },
  {
    id: 7,
    name: 'profile.faq',
    show: true,
    icon: 'fill/layout/circle-question',
    route: RootScreen.FAQView,
  },
  {
    id: 1,
    name: 'profile.policy',
    show: true,
    icon: 'fill/shopping/list',
    route: RootScreen.PolicyView,
  },
  {
    id: 9,
    name: 'profile.logout',
    show: true,
    action: 'logout',
    icon: 'outline/arrows/logout',
    route: RootScreen.login,
  },
];

const ActionList = ({customer, dialogRef, dispatch}: any) => {
  const {t} = useTranslation();

  const handlePress = (item: any) => {
    if (item.action === 'logout') {
      if (!customer) {
        dispatch(CustomerActions.logout());
        return;
      }
      showDialog({
        ref: dialogRef,
        status: ComponentStatus.WARNING,
        title: t('customer.confirmLogout'),
        onSubmit: async () => {
          dispatch(CustomerActions.logout());
        },
      });
      return;
    }

    if (item.action === 'language') {
      navigate(RootScreen.LanguageSelection);
      return;
    }

    if (item.route) {
      navigate(item.route);
    }
  };

  const itemsToRender = customer
    ? actionList
    : actionList.filter(item => item.show);

  return (
    <View>
      {itemsToRender.map(item => (
        <ListTile
          key={item.id}
          style={styles.listTile}
          listtileStyle={styles.listTileContent}
          onPress={() => handlePress(item)}
          leading={
            <View style={styles.leadingIconContainer}>
              <Winicon
                src={item.icon}
                color={ColorThemes.light.neutral_text_title_color}
                size={20}
              />
            </View>
          }
          title={
            !customer && item.action === 'logout'
              ? t('profile.loginRedirect')
              : t(item.name)
          }
          titleStyle={styles.title}
          trailing={
            <Winicon
              src="outline/arrows/right-arrow"
              color={ColorThemes.light.neutral_text_subtitle_color}
              size={16}
            />
          }
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  listTile: {
    padding: 0,
  },
  listTileContent: {
    paddingRight: 16,
    paddingVertical: 13,
    gap: 8,
    borderBottomColor: ColorThemes.light.primary_background,
    borderBottomWidth: 1,
    marginLeft: 16,
  },
  leadingIconContainer: {
    height: 32,
    width: 32,
    borderRadius: 4,
    padding: 6,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  title: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
  },
});

export default ActionList;
