// const hostMapUrl = 'https://server.wini.vn/api/data/';
const hostMapUrl = 'https://maps.googleapis.com/maps/api/';

export default class ConfigAPI {
  static regexGuid = /^[0-9a-fA-F]{32}$/;

  static getValidLink = (link: string) => {
    if (!link) return '';
    if (link.startsWith('http')) return link;
    if (ConfigAPI.regexGuid.test(link)) return ConfigAPI.urlImg + link;
    else return ConfigAPI.urlImg + link;
  };
  // https://server-deeplink-s7pb.onrender.com/api/open-app?link=chainivo://product/3595d67bbc034e5689c5bbdf8f9f6706?ref=share&campaign=product
  static urlDeeplink =
    'https://server-deeplink-s7pb.onrender.com/api/open-app?link=';
  static url = 'https://apichanivo.wini.vn/api/';
  static urlWeb = 'https://apichanivo.wini.vn/api/'; // Phần này phải có bản web mới có url, hiện đang để tạm
  static Socketurl = 'https://apichanivo.wini.vn';
  static urlBlockchain = 'https://blockchainapi.innotechjsc.com:9443/api/';
  static pid = '1db6d3afe3c442a7a1366dffa0cea2e0';
  static googleApiKey = 'AIzaSyBrjZpmgCpST9GWPt7fCnr_EiQi-uL9SQM';
  static urlImg = 'https://apichanivo.wini.vn/api/file/img/';
  static adminCHAINIVO = '9089446bbf0241a3970e28b89a378234';
  static area = 'vi';
  static username_blc = 'admin';
  static password_blc = 'admin123';
  static GEOCODING_API_URL_BY_GOOGLE = (lat: any, lng: any) => {
    // return `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    return (
      hostMapUrl +
      `geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    );
  };

  static getAddressByGoogleKey = (inputText: string) => {
    return (
      hostMapUrl +
      `place/textsearch/json?&query=${encodeURIComponent(inputText)}&key=${
        ConfigAPI.googleApiKey
      }`
    );
  };

  static provinceUrl = 'https://esgoo.net/api-tinhthanh/1/0.htm';
  static districtUrl = (cityId: string) =>
    `https://esgoo.net/api-tinhthanh/2/${cityId}.htm`;

  static wardUrl = (districtId: string) =>
    `https://esgoo.net/api-tinhthanh/3/${districtId}.htm`;
}
