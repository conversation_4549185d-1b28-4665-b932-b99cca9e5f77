import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {getRankCustomer} from '../../../../redux/actions/customerAction';
import {OrderActions} from '../../../../redux/reducers/OrderReducer';
import {DataController} from '../../../../base/baseController';
import {RootState} from '../../../../redux/store/store';

export const useProfilePage = (select: any) => {
  const dispatch = useDispatch<any>();
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);

  const customer = useSelector((state: RootState) => state.customer.data);
  const shopInfo = useSelector((state: RootState) => state.shop.data);

  const [orderDetail, setOrderDetail] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const orderController = useMemo(() => new DataController('Order'), []);

  const getOrderDetail = useCallback(async () => {
    if (!customer?.Id) return;
    setLoading(true);
    try {
      const response = await orderController.getPatternList({
        query: `@CustomerId: {${customer?.Id}}`,
        pattern: {
          ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
        },
      });
      if (response?.code === 200) {
        setOrderDetail(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch order details:', error);
    } finally {
      setLoading(false);
    }
  }, [customer?.Id, orderController]);

  useEffect(() => {
    if (customer?.Id) {
      dispatch(getRankCustomer({Id: customer.Id}));
    }
  }, [customer?.Id, dispatch]);

  useEffect(() => {
    if (select === 'Cá nhân') {
      getOrderDetail();
    }
  }, [select, getOrderDetail]);

  useEffect(() => {
    if (shopInfo && shopInfo[0]?.Id) {
      dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
    }
  }, [shopInfo, dispatch]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      getOrderDetail();
    });
    return unsubscribe;
  }, [navigation, getOrderDetail]);

  return {
    customer,
    loading,
    dialogRef,
    orderDetail,
    dispatch,
  };
};
