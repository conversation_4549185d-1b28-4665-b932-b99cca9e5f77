import React, {
  ImageBackground,
  Text,
  View,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {AppSvg, FDialog} from 'wini-mobile-components';
import FastImage from '@d11/react-native-fast-image';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';

import iconSvg from '../../svg/icon';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ConfigAPI from '../../Config/ConfigAPI';
import {RootScreen} from '../../router/router';
import {Ultis} from '../../utils/Utils';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {getRankCustomer} from '../../redux/actions/customerAction';
import {RootState} from '../../redux/store/store';
import {useEffect, useRef, useState} from 'react';
import {MissionType} from 'Config/Contanst';
import WalletDA from './walletDa';

// Constants
const VIP_COLORS = ['#FFC043', '#FFD275', '#FFE0A3', '#FFF3DF'];
const NORMAL_COLORS = ['#90C8FB', '#8DC4F7E5', '#B6F5FE'];
const VIP_IMAGE = require('../../assets/bg_circle_orange.png');
const NORMAL_IMAGE = require('../../assets/bg04.png');

// Skeleton Component
const SkeletonBox = ({
  width,
  height,
  style,
}: {
  width: any;
  height: number;
  style?: any;
}) => (
  <SkeletonPlaceholder
    backgroundColor="#f0f0f0"
    highlightColor="#e0e0e0"
    children={undefined}>
    <SkeletonPlaceholder.Item
      width={typeof width === 'string' ? undefined : width}
      height={height}
      borderRadius={4}
      style={style}
    />
  </SkeletonPlaceholder>
);

const PointHome = () => {
  const dispatch = useDispatch<any>();
  const {rankInfo, rankInfoLoading, point} = useSelector(
    (state: RootState) => state.customer,
  );
  const customer = useSelectorCustomerState().data;
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const [isVip, setIsVip] = useState<boolean>(false);

  // Note: isVip is always false, so we can simplify the logic
  const colors = isVip ? VIP_COLORS : NORMAL_COLORS;
  const backgroundImage = isVip ? VIP_IMAGE : NORMAL_IMAGE;
  const textColor = isVip
    ? ColorThemes.light.secondary6_darker_color
    : ColorThemes.light.primary_main_color;

  useEffect(() => {
    if (rankInfo && !rankInfoLoading) {
      const walletda = new WalletDA();
      // #region xử lý nhiệm vụ
      if (customer) walletda.CaculateMisson(customer?.Id, MissionType.Login);
    }
  }, [rankInfo]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', async () => {
      if (customer?.Id) {
        dispatch(getRankCustomer({Id: customer.Id}));
      }
    });
    return unsubscribe;
  }, [navigation, customer?.Id, dispatch]);

  useEffect(() => {
    if (!rankInfo || !rankInfo?.Sort) setIsVip(false);
    else setIsVip(rankInfo?.Sort >= 3);
  }, [rankInfo]);

  const handleGiftExchange = () => {
    if (!customer?.Id) {
      dialogCheckAcc(dialogRef);
      return;
    }
    navigation.push(RootScreen.GiftExchange);
  };

  if (rankInfoLoading) {
    return (
      <View style={styles.container}>
        <FDialog ref={dialogRef} />
        <SkeletonBox width="100%" height={82} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <LinearGradient
        colors={colors}
        start={{x: 0, y: 0}}
        end={{x: 0, y: 1}}
        style={styles.gradient}>
        <ImageBackground
          source={backgroundImage}
          resizeMode="contain"
          style={styles.backgroundImage}
        />

        <View style={styles.content}>
          {/* Points Section */}
          <View style={styles.row}>
            <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
            <Text style={styles.text}>{Ultis.money(point || 0)} điểm</Text>
          </View>

          {/* Rank Section */}
          <View style={[styles.row, {marginTop: 4}]}>
            {rankInfo?.Icon ? (
              <FastImage
                source={{uri: ConfigAPI.urlImg + rankInfo.Icon}}
                style={styles.icon}
                resizeMode="contain"
              />
            ) : (
              <AppSvg SvgSrc={iconSvg.logoCircle} size={20} />
            )}
            <Text style={styles.text}>
              {rankInfo?.Name ? `Hạng ${rankInfo?.Name}` : 'Chưa có hạng'}
            </Text>
          </View>
        </View>

        {/* Gift Exchange Button */}
        <TouchableOpacity style={styles.button} onPress={handleGiftExchange}>
          <LinearGradient
            start={{x: 0, y: 0}}
            end={{x: 0, y: 1}}
            colors={VIP_COLORS}
            style={styles.buttonGradient}>
            <Text style={[styles.buttonText, {color: textColor}]}>Đổi quà</Text>
          </LinearGradient>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 82,
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 10,
    overflow: 'hidden',
  },
  gradient: {
    borderRadius: 10,
    height: '100%',
    flex: 1,
    overflow: 'hidden',
    width: '100%',
  },
  backgroundImage: {
    position: 'absolute',
    bottom: -30,
    left: -20,
    width: 110,
    height: 110,
  },
  content: {
    paddingLeft: 76,
    paddingTop: 16,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  text: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
  },
  icon: {
    width: 20,
    height: 20,
  },
  button: {
    position: 'absolute',
    right: 16,
    top: 22,
  },
  buttonGradient: {
    borderRadius: 8,
    minHeight: 32,
  },
  buttonText: {
    ...TypoSkin.body2,
    fontSize: 14,
    fontWeight: '500',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
});

export default PointHome;
