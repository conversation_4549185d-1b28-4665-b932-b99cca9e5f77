# DeepLink Service

## 🎯 Overview

A comprehensive, type-safe deeplink system for the Chainivo React Native app.

## 🚀 Quick Start

```typescript
import {deepLinkManager} from 'src/services/deeplink';

// The service is automatically initialized in App.tsx
// Handle deeplinks manually if needed
const result = await deepLinkManager.handleDeepLink('chainivo://posts/123');
```

## 📁 File Structure

```
src/services/deeplink/
├── DeepLinkManager.ts      # Main service (singleton)
├── DeepLinkUtils.ts        # Parsing & validation utilities
├── DeepLinkTypes.ts        # Type definitions
├── index.ts                # Exports
├── __tests__/              # Test files
│   ├── DeepLinkManager.test.ts
│   └── DeepLinkUtils.test.ts
└── README.md               # This file
```

## 🔧 Components

### DeepLinkManager
- Singleton service managing all deeplink operations
- Handles URL listening, parsing, and navigation
- Provides event system for monitoring
- Automatic retry and error handling

### DeepLinkUtils
- URL validation and parsing functions
- Deeplink creation utilities
- Type-safe parameter extraction

### DeepLinkTypes
- Comprehensive type definitions
- URL patterns and validation rules
- Configuration interfaces

## 🎨 Usage Examples

### Basic Navigation

```typescript
// Navigate to a post
await deepLinkManager.handleDeepLink('chainivo://posts/123');

// Navigate to a product
await deepLinkManager.handleDeepLink('https://chainivo.com/product/456');
```

### Creating Share Links

```typescript
import {createDeepLink, createUniversalLink} from 'src/services/deeplink';

// Custom scheme (for in-app sharing)
const postLink = createDeepLink('posts', '123', {ref: 'share'});
// Result: "chainivo://posts/123?ref=share"

// Universal link (for external sharing)
const universalLink = createUniversalLink('posts', '123');
// Result: "https://chainivo.com/posts/123"
```

### Event Monitoring

```typescript
// Listen for deeplink events
deepLinkManager.addEventListener('url_received', (event, data) => {
  console.log('URL received:', data.url);
});

deepLinkManager.addEventListener('navigation_success', (event, data) => {
  console.log('Navigated to:', data.screen);
});
```

## 🔍 Supported URL Patterns

### Custom Scheme
- `chainivo://posts/{id}` → PostDetail screen
- `chainivo://news/{id}` → DetailNews screen
- `chainivo://product/{id}` → ProductDetail screen
- `chainivo://events/{id}` → DetailEvent screen
- `chainivo://share/{id}` → Login screen (saves RefCode)
- `chainivo://` → Home screen

### Universal Links
- `https://chainivo.com/posts/{id}`
- `https://chainivo.com/news/{id}`
- `https://chainivo.com/product/{id}`
- `https://chainivo.com/events/{id}`
- `https://chainivo.com/share/{id}`
- `https://chainivo.com/`

## 🧪 Testing

### Unit Tests
```bash
npm test -- src/services/deeplink/__tests__/
```

### Manual Testing
```typescript
// Available in development
global.testDeepLink = (url: string) => {
  deepLinkManager.handleDeepLink(url);
};

// Usage
testDeepLink('chainivo://posts/123');
```

## 🔧 Configuration

### Default Configuration
```typescript
{
  enableLogging: __DEV__,
  fallbackScreen: RootScreen.navigateEComView,
  retryAttempts: 2,
  retryDelay: 500,
}
```

### Custom Configuration
```typescript
deepLinkManager.initialize(navigationRef, {
  enableLogging: true,
  fallbackScreen: RootScreen.navigateEComView,
  retryAttempts: 3,
  retryDelay: 1000,
});
```

## 🚨 Error Handling

The service handles errors gracefully:
- Invalid URLs → Navigate to fallback screen
- Navigation errors → Retry with exponential backoff
- Parsing errors → Log and fallback
- Missing navigation ref → Queue for later processing

## 📊 Performance

- Singleton pattern for efficient memory usage
- Lazy initialization
- Event-driven architecture
- Automatic cleanup on app termination

## 🔒 Security

- URL validation against whitelist
- Parameter sanitization
- Safe navigation with error boundaries
- No execution of arbitrary code

## 🐛 Debugging

Enable detailed logging:
```typescript
deepLinkManager.initialize(navigationRef, {
  enableLogging: true
});
```

Look for console messages:
```
[DeepLinkManager] Handling deeplink: chainivo://posts/123
[DeepLinkManager] Successfully navigated to PostDetail
```

## 📚 API Reference

### DeepLinkManager Methods

```typescript
// Initialize the manager
initialize(navigationRef, config?): void

// Handle a deeplink URL
handleDeepLink(url: string): Promise<DeepLinkNavigationResult>

// Event management
addEventListener(event: DeepLinkEvent, listener: DeepLinkEventListener): void
removeEventListener(event: DeepLinkEvent, listener: DeepLinkEventListener): void

// Cleanup
cleanup(): void
```

### DeepLinkUtils Functions

```typescript
// Validation
validateDeepLink(url: string): DeepLinkValidationResult

// Parsing
parseDeepLink(url: string): DeepLinkParsingResult

// Creation
createDeepLink(type: DeepLinkType, id?: string, queryParams?: Record<string, string>): string
createUniversalLink(type: DeepLinkType, id?: string, queryParams?: Record<string, string>): string

// Validation helpers
hasValidId(parsed: ParsedDeepLink): boolean
```

## 🔄 Migration

If migrating from the old system:

1. Replace `DeepLinkListener` with `DeepLinkProvider`
2. Update imports to use new service
3. Test all scenarios
4. Remove old files

See [Migration Plan](../../../docs/DEEPLINK_MIGRATION_PLAN.md) for details.

## 📞 Support

For issues:
1. Check console logs
2. Verify URL patterns
3. Test with development tools
4. Review configuration

## 📖 Documentation

- [Developer Guide](../../../docs/DEEPLINK_DEVELOPER_GUIDE.md)
- [Testing Guide](../../../docs/DEEPLINK_TESTING_GUIDE.md)
- [Migration Plan](../../../docs/DEEPLINK_MIGRATION_PLAN.md)
