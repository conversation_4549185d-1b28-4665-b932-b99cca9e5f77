// Service để quản lý message notification state
// Sử dụng simple callback pattern tương tự IncomingCallOverlayService

import { ChatMessage, ChatRoom } from '../modules/chat/types/ChatTypes';

interface MessageNotificationData {
  message: ChatMessage;
  room: ChatRoom | null;
  senderName: string;
  messagePreview: string;
}

type EventCallback = (data?: any) => void;

class MessageNotificationService {
  private static instance: MessageNotificationService;
  private isShowing = false;
  private currentNotificationData: MessageNotificationData | null = null;
  private listeners: { [event: string]: EventCallback[] } = {};
  private hideTimeout: NodeJS.Timeout | null = null;

  private constructor() {}

  static getInstance(): MessageNotificationService {
    if (!MessageNotificationService.instance) {
      MessageNotificationService.instance = new MessageNotificationService();
    }
    return MessageNotificationService.instance;
  }

  // Add event listener
  on(event: string, callback: EventCallback): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    console.log(`💬 Added listener for ${event}, total: ${this.listeners[event].length}`);
  }

  // Remove event listener
  off(event: string, callback: EventCallback): void {
    if (!this.listeners[event]) return;

    const index = this.listeners[event].indexOf(callback);
    if (index > -1) {
      this.listeners[event].splice(index, 1);
      console.log(`💬 Removed listener for ${event}, remaining: ${this.listeners[event].length}`);
    }
  }

  // Emit event
  emit(event: string, data?: any): void {
    console.log(`💬 Emitting event ${event} with data:`, data);
    if (!this.listeners[event]) {
      console.log(`💬 No listeners for event ${event}`);
      return;
    }

    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`💬 Error in listener for ${event}:`, error);
      }
    });
  }

  // Get listener count
  listenerCount(event: string): number {
    return this.listeners[event] ? this.listeners[event].length : 0;
  }

  // Hiển thị message notification
  showMessageNotification(message: ChatMessage, room: ChatRoom | null): void {
    const senderName = message.user?.Name || 'Someone';
    const messagePreview = message.Type === 2 ? '📷 Hình ảnh' : 
                          message.Type === 3 ? '📎 File đính kèm' : 
                          message.Content;

    const notificationData: MessageNotificationData = {
      message,
      room,
      senderName,
      messagePreview,
    };

    console.log('💬 Showing message notification from:', senderName);

    // Clear existing timeout
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    // Reset state trước khi show notification mới
    if (this.isShowing) {
      console.log('💬 Notification already showing, hiding first');
      this.hideMessageNotification();

      // Delay một chút để đảm bảo state được reset
      setTimeout(() => {
        this.showMessageNotification(message, room);
      }, 200);
      return;
    }

    this.isShowing = true;
    this.currentNotificationData = notificationData;

    // Emit event để UI components có thể listen và hiển thị
    this.emit('showMessageNotification', notificationData);

    // Auto hide after 5 seconds
    this.hideTimeout = setTimeout(() => {
      this.hideMessageNotification();
    }, 5000);
  }

  // Ẩn message notification
  hideMessageNotification(): void {
    console.log('💬 hideMessageNotification called, current isShowing:', this.isShowing);

    if (!this.isShowing) {
      console.log('💬 Notification not showing, nothing to hide');
      return;
    }

    // Clear timeout
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    this.isShowing = false;
    this.currentNotificationData = null;
    console.log('💬 Hiding message notification, state reset');

    // Emit event để UI components ẩn notification
    this.emit('hideMessageNotification');
  }

  // Tap notification để mở chat room
  tapNotification(): void {
    if (this.currentNotificationData) {
      console.log('💬 Tapping notification for room:', this.currentNotificationData.room?.Id);
      this.emit('tapNotification', this.currentNotificationData);
      this.hideMessageNotification();
    }
  }

  // Kiểm tra xem có đang hiển thị notification không
  isShowingNotification(): boolean {
    return this.isShowing;
  }

  // Lấy thông tin notification hiện tại
  getCurrentNotificationData(): MessageNotificationData | null {
    return this.currentNotificationData;
  }

  // Force reset state (để debug)
  forceReset(): void {
    console.log('💬 Force resetting MessageNotificationService state');
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
    this.isShowing = false;
    this.currentNotificationData = null;
    this.emit('hideMessageNotification');
  }

  // Get current state (để debug)
  getState(): { isShowing: boolean; currentNotificationData: MessageNotificationData | null } {
    return {
      isShowing: this.isShowing,
      currentNotificationData: this.currentNotificationData,
    };
  }
}

export default MessageNotificationService.getInstance();
