import {randomGID, Ultis} from '../../../utils/Utils.js';
import {DataController} from '../../../base/baseController.tsx';
import {TransactionType} from 'Config/Contanst.tsx';

// Constants for order status
const ORDER_STATUS = {
  NEWORDER: 1,
  CANCELLED: 3,
} as const;

// Interfaces for better type safety
interface OrderDetail {
  Id: string;
  ProductId: string;
  Quantity: number;
  Price: number;
  Total: number;
  CategoryId?: string; // Added for convenience
}

interface RewardConfig {
  Id: string;
  CategoryId?: string;
  Percent: number;
  Filial: number;
  DateCreated: string;
}

interface RewardTransaction {
  Id: string;
  Filial: number;
  OrderDetailId: string | null;
  CustomerId: string;
  Value: number;
  Status: number;
  Type: number;
  OrderId: string;
  Name: string;
  DateCreated: number;
  Description: string;
  Code: string;
  ShopCateId?: string;
  ShopRewardId?: string;
  RewardId?: string;
}

/**
 * Process affiliate rewards for an order
 */
export async function fnFilial(
  shopid: string,
  orderid: string,
  customerId: string,
  status: number,
  code: string,
): Promise<void> {
  try {
    // Only process rewards for new confirmed orders
    if (status === ORDER_STATUS.NEWORDER) {
      const existingRewards = await getExistingRewards(orderid);
      // Only create rewards if none exist for this order
      if (!existingRewards?.length) {
        await processNewRewards(shopid, orderid, customerId, code);
      }
    }
  } catch (error) {
    console.error('Error in fnFilial:', error);
    throw error;
  }
}

/**
 * Get existing rewards for an order
 */
async function getExistingRewards(orderid: string): Promise<any[]> {
  const historyReward = new DataController('HistoryReward');
  const result = await historyReward.getListSimple({
    page: 1,
    size: 1000,
    query: `@OrderId:{${orderid}} @Type:[${TransactionType.hoahong}]`,
    returns: ['Id'],
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
  return result?.data || [];
}

/**
 * Process new rewards for a completed order
 */
async function processNewRewards(
  shopid: string,
  orderid: string,
  customerId: string,
  code: string,
): Promise<void> {
  const [
    customerData,
    orderDetails,
    shopCategories,
    shopRewards,
    systemRewards,
    pointToCurrencyConfig,
  ] = await Promise.all([
    getCustomerData(customerId),
    getOrderDetails(orderid),
    getShopCategories(shopid),
    getShopRewards(shopid),
    getSystemRewards(),
    getPointToCurrencyConfig(),
  ]);
  const parentList = await buildParentList(customerData, customerId);
  const conversionRate = getConversionRate(pointToCurrencyConfig);
  const transactions = await calculateRewards({
    shopid,
    orderid,
    customerId,
    code,
    orderDetails,
    shopCategories,
    shopRewards,
    systemRewards,
    parentList,
    conversionRate,
  });
  if (transactions.length > 0) {
    const historyReward = new DataController('HistoryReward');
    await historyReward.add(transactions);
  }
}

// --- Data Fetching Helpers ---

async function getCustomerData(customerId: string): Promise<any> {
  const customer = new DataController('Customer');
  const result = await customer.getById(customerId);
  return result?.data || [];
}

async function getOrderDetails(orderid: string): Promise<OrderDetail[]> {
  const orderDetail = new DataController('OrderDetail');
  const result = await orderDetail.getListSimple({
    page: 1,
    size: 100,
    query: `@OrderId:{${orderid}}`,
    returns: ['Id', 'ProductId', 'Quantity', 'Price', 'Total'],
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
  return result?.data || [];
}

async function getShopCategories(shopid: string): Promise<RewardConfig[]> {
  const shopcate = new DataController('ShopCate');
  const result = await shopcate.getListSimple({
    page: 1,
    size: 100,
    query: `@ShopId:{${shopid}} @Status:[1]`,
    returns: ['Id', 'CategoryId', 'DateCreated', 'Percent', 'Filial'],
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
  return result?.data || [];
}

async function getShopRewards(shopid: string): Promise<RewardConfig[]> {
  const shopReward = new DataController('ShopReward');
  const result = await shopReward.getListSimple({
    page: 1,
    size: 100,
    query: `@ShopId:{${shopid}} @Status:[1]`,
    returns: ['Id', 'CategoryId', 'Percent', 'DateCreated', 'Filial'],
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
  return result?.data || [];
}

async function getSystemRewards(): Promise<RewardConfig[]> {
  const reward = new DataController('Reward');
  const result = await reward.getListSimple({
    page: 1,
    size: 100,
    query: '*',
    returns: ['Id', 'Percent', 'Filial'],
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
  return result?.data || [];
}

async function getPointToCurrencyConfig(): Promise<any[]> {
  const pointToCurrency = new DataController('ConfigPointToCurrency');
  const result = await pointToCurrency.getAll();
  return result?.data || [];
}

// --- Data Processing Helpers ---

/**
 * Build parent list by fetching parent and grandparent from database
 * @param customerData - The customer data
 * @param customerId - The customer ID
 * @returns Array where index 0 is customer, 1 is parent, 2 is grandparent
 */
async function buildParentList(
  customerData: any,
  customerId: string,
): Promise<string[]> {
  const parentList: string[] = [customerId]; // Level 0 is the customer

  // If customer has no parent, return just the customer
  if (!customerData || !customerData.ParentId) {
    return parentList;
  }

  try {
    const customer = new DataController('Customer');

    // Get parent (level 1)
    const parentResult = await customer.getListSimple({
      page: 1,
      size: 1,
      query: `@Id:{${customerData.ParentId}}`,
      returns: ['Id', 'ParentId', 'Name'],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });

    const parentData = parentResult?.data?.[0];
    if (parentData && parentData.Id) {
      parentList.push(parentData.Id); // Level 1 - Parent

      // Check if parent has a parent (grandparent)
      if (parentData.ParentId) {
        // Get grandparent (level 2)
        const grandparentResult = await customer.getListSimple({
          page: 1,
          size: 1,
          query: `@Id:{${parentData.ParentId}}`,
          returns: ['Id', 'Name'],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        const grandparentData = grandparentResult?.data?.[0];
        if (grandparentData && grandparentData.Id) {
          parentList.push(grandparentData.Id); // Level 2 - Grandparent
        }
      }
    }
  } catch (error) {
    console.error('Error building parent list:', error);
    // Return what we have so far if error occurs
  }

  return parentList;
}

function getConversionRate(config: any[]): number {
  return config?.[0]?.Rate || 1;
}

async function getProductsByCategories(
  shopid: string,
  categories: RewardConfig[],
  orderDetails: OrderDetail[],
): Promise<any[]> {
  if (categories.length === 0) return [];

  const uniqueProductIds = [
    ...new Set(orderDetails.map(item => item.ProductId)),
  ];
  const uniqueCategoryIds = [
    ...new Set(categories.map(cat => cat.CategoryId).filter(Boolean)),
  ];

  if (uniqueCategoryIds.length === 0 || uniqueProductIds.length === 0)
    return [];

  const query = `@ShopId:{${shopid}} @Id:{${uniqueProductIds.join(
    ' | ',
  )}} @CategoryId:{${uniqueCategoryIds.join(' | ')}}`;

  const product = new DataController('Product');
  const result = await product.getListSimple({
    page: 1,
    size: 100,
    query,
    returns: ['Id', 'CategoryId'],
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });

  return result?.data || [];
}

// --- Reward Calculation Helpers ---

async function calculateRewards(params: {
  shopid: string;
  orderid: string;
  customerId: string;
  code: string;
  orderDetails: OrderDetail[];
  shopCategories: RewardConfig[];
  shopRewards: RewardConfig[];
  systemRewards: RewardConfig[];
  parentList: string[];
  conversionRate: number;
}): Promise<RewardTransaction[]> {
  const allTransactions: RewardTransaction[] = [];

  // Step 1: Get products that belong to categories with reward rules
  const categoryProducts = await getProductsByCategories(
    params.shopid,
    params.shopCategories,
    params.orderDetails,
  );

  // Step 2: Process category rewards for products that have category rules
  const categoryProductIds = new Set(categoryProducts.map(p => p.Id));
  const categoryOrderDetails = params.orderDetails
    .filter(detail => categoryProductIds.has(detail.ProductId))
    .map(detail => {
      const product = categoryProducts.find(p => p.Id === detail.ProductId);
      return product ? {...detail, CategoryId: product.CategoryId} : detail;
    });

  if (categoryOrderDetails.length > 0) {
    const categoryTransactions = processRewardsForType({
      ...params,
      orderDetails: categoryOrderDetails,
      rewardConfigs: params.shopCategories,
      type: 'category',
    });
    allTransactions.push(...categoryTransactions);
  }

  // Step 3: Process remaining products with shop or system rewards
  const remainingProducts = params.orderDetails.filter(
    detail => !categoryProductIds.has(detail.ProductId),
  );

  if (remainingProducts.length > 0) {
    if (params.shopRewards.length > 0) {
      const shopTransactions = processRewardsForType({
        ...params,
        orderDetails: remainingProducts,
        rewardConfigs: params.shopRewards,
        type: 'shop',
      });
      allTransactions.push(...shopTransactions);
    } else if (params.systemRewards.length > 0) {
      const systemTransactions = processRewardsForType({
        ...params,
        orderDetails: remainingProducts,
        rewardConfigs: params.systemRewards,
        type: 'system',
      });
      allTransactions.push(...systemTransactions);
    }
  }

  return allTransactions;
}

function processRewardsForType(params: {
  orderid: string;
  customerId: string;
  code: string;
  orderDetails: OrderDetail[];
  rewardConfigs: RewardConfig[];
  parentList: string[];
  conversionRate: number;
  type: 'category' | 'shop' | 'system';
}): RewardTransaction[] {
  const transactions: RewardTransaction[] = [];

  for (const detail of params.orderDetails) {
    // Filter applicable configs based on type
    let applicableConfigs: RewardConfig[];

    if (params.type === 'category') {
      // For category rewards, only apply configs that match the product's category
      applicableConfigs = params.rewardConfigs.filter(
        config => config.CategoryId === detail.CategoryId,
      );
    } else {
      // For shop and system rewards, all configs are applicable
      applicableConfigs = params.rewardConfigs;
    }

    for (const config of applicableConfigs) {
      // Check if there's a valid parent at the required filial level
      // and ensure the filial level doesn't exceed available parents
      if (
        config.Filial >= 0 &&
        config.Filial < params.parentList.length &&
        params.parentList[config.Filial]
      ) {
        const rewardValue =
          (detail.Total * config.Percent) / 100 / params.conversionRate;

        const rewardTransaction = createBaseRewardTransaction({
          orderDetail: detail,
          rewardConfig: config,
          parentList: params.parentList,
          rewardValue,
          orderid: params.orderid,
          customerId: params.customerId,
          code: params.code,
          type: params.type,
        });

        const allTransactions = createRewardTransactions(
          rewardTransaction,
          params.customerId,
          params.code,
        );
        transactions.push(...allTransactions);
      }
    }
  }
  return transactions;
}

function createBaseRewardTransaction(params: {
  orderDetail: OrderDetail;
  rewardConfig: RewardConfig;
  parentList: string[];
  rewardValue: number;
  orderid: string;
  customerId: string;
  code: string;
  type: 'category' | 'shop' | 'system';
}): RewardTransaction {
  const {
    orderDetail,
    rewardConfig,
    parentList,
    rewardValue,
    orderid,
    customerId,
    code,
    type,
  } = params;

  const nameMap = {
    category: `Chuyên mục bản cập nhật: ${rewardConfig.DateCreated} của đơn hàng ${customerId}`,
    shop: `Shop bản cập nhật: ${rewardConfig.DateCreated} của đơn hàng ${customerId}`,
    system: `System của đơn hàng ${customerId}`,
  };

  const transaction: RewardTransaction = {
    Id: randomGID(),
    Filial: rewardConfig.Filial * 1,
    OrderDetailId: orderDetail.Id,
    CustomerId: parentList[rewardConfig.Filial],
    Value: rewardValue,
    Status: 1, // Active transaction
    OrderId: orderid,
    Type: TransactionType.hoahong,
    Name: nameMap[type],
    DateCreated: Date.now(),
    Description: `Thưởng hoa hồng từ đơn hàng #${code}`,
    Code: Ultis.randomString(10),
  };

  if (type === 'category') transaction.ShopCateId = rewardConfig.Id;
  if (type === 'shop') transaction.ShopRewardId = rewardConfig.Id;
  if (type === 'system') transaction.RewardId = rewardConfig.Id;

  return transaction;
}

/**
 * Creates a commission transaction and a corresponding deduction transaction.
 */
function createRewardTransactions(
  rewardTransaction: RewardTransaction,
  senderId: string,
  code: string,
): RewardTransaction[] {
  const transactions = [rewardTransaction];

  if (senderId !== rewardTransaction.CustomerId) {
    transactions.push({
      ...rewardTransaction,
      Id: randomGID(),
      Filial: 0,
      CustomerId: senderId,
      Value: -rewardTransaction.Value, // Deduct from the buyer
      Status: 1, // Active transaction
      Type: TransactionType.hoahong,
      Name: `Trừ tiền hoa hồng: ${rewardTransaction.Name}`,
      Description: `Trừ tiền hoa hồng từ đơn hàng #${code}`,
      Code: Ultis.randomString(10),
    });
  }

  return transactions;
}
