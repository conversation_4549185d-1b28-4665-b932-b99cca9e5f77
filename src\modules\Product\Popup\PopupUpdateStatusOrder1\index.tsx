import React from 'react';
import {
  View,
  Modal,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
} from 'react-native';
import {useOrderStatusUpdate, OrderItem} from './hooks/useOrderStatusUpdate';
import OrderStatusHeader from './components/OrderStatusHeader';
import OrderStatusList from './components/OrderStatusList';
import CancelReasonInput from './components/CancelReasonInput';
import ActionButtons from './components/ActionButtons';

interface PopupUpdateStatusOrderProps {
  visible: boolean;
  onClose: () => void;
  item: OrderItem;
  handleUpdateStatusProcessOrder: (
    item: OrderItem,
    type?: string,
  ) => Promise<void>;
}

const PopupUpdateStatusOrder: React.FC<PopupUpdateStatusOrderProps> = ({
  visible,
  onClose,
  item,
  handleUpdateStatusProcessOrder,
}) => {
  const {
    selectedStatus,
    cancelReason,
    setCancelReason,
    statusOptions,
    handleStatusSelect,
    handleUpdateStatus,
    isUpdateDisabled,
  } = useOrderStatusUpdate({
    visible,
    item,
    handleUpdateStatusProcessOrder,
    onClose,
  });
  return (
    <Modal
      visible={visible}
      transparent={true}
      statusBarTranslucent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalOverlay}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
          style={{flex: 1, justifyContent: 'center'}}>
          <ScrollView
            contentContainerStyle={{flexGrow: 1, justifyContent: 'center'}}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}>
            <View style={styles.modalContent}>
              <OrderStatusHeader orderCode={item?.Code} onClose={onClose} />

              <View style={styles.orderInfoContainer} />

              <OrderStatusList
                statusOptions={statusOptions}
                selectedStatus={selectedStatus}
                onStatusSelect={handleStatusSelect}
              />

              <CancelReasonInput
                visible={selectedStatus === 'cancelled'}
                value={cancelReason}
                onChangeText={setCancelReason}
              />

              <ActionButtons
                onUpdateStatus={handleUpdateStatus}
                isDisabled={isUpdateDisabled}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.65)',
    paddingHorizontal: 20,
  },
  modalContent: {
    backgroundColor: '#ffffff',
    padding: 24,
    borderRadius: 20,
    width: '100%',
    maxWidth: 420,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 20},
    shadowOpacity: 0.35,
    shadowRadius: 35,
    elevation: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  orderInfoContainer: {
    alignItems: 'center',
    paddingVertical: 12,
  },
});

export default PopupUpdateStatusOrder;
