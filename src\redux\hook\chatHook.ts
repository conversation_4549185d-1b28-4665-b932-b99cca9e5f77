import { useSelector } from 'react-redux';
import { RootState } from '../store/store';

export const useSelectorChatState = () => {
  return useSelector((state: RootState) => state.chat);
};

export const useChatRooms = () => {
  return useSelector((state: RootState) => state.chat.rooms);
};

export const useCurrentChatRoom = () => {
  return useSelector((state: RootState) => state.chat.currentRoom);
};

export const useChatMessages = (roomId: string) => {
  return useSelector((state: RootState) => state.chat.messages[roomId] || []);
};

export const useUnreadCount = () => {
  return useSelector((state: RootState) => state.chat.unreadCount);
};

export const useChatLoading = () => {
  return useSelector((state: RootState) => state.chat.loading);
};

export const useChatError = () => {
  return useSelector((state: RootState) => state.chat.error);
};

export const useChatConnectionStatus = () => {
  return useSelector((state: RootState) => state.chat.isConnected);
};
