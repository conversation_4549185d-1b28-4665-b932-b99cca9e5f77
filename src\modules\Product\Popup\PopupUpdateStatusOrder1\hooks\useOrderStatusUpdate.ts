import {useState, useEffect} from 'react';

export interface StatusOption {
  id: number;
  name: string;
  type: string;
}

export interface OrderItem {
  Status: number;
  Code: string;
  cancelReason?: string;
  [key: string]: any;
}

export interface UseOrderStatusUpdateProps {
  visible: boolean;
  item: OrderItem;
  handleUpdateStatusProcessOrder: (
    item: OrderItem,
    type?: string,
  ) => Promise<void>;
  onClose: () => void;
}

export const useOrderStatusUpdate = ({
  visible,
  item,
  handleUpdateStatusProcessOrder,
  onClose,
}: UseOrderStatusUpdateProps) => {
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [cancelReason, setCancelReason] = useState('');

  // Reset state when modal is closed
  useEffect(() => {
    if (!visible) {
      setSelectedStatus(null);
      setCancelReason('');
    }
  }, [visible]);

  const statusOptions: StatusOption[] = [
    {id: 1, name: '<PERSON><PERSON><PERSON> nhận đơn hàng', type: 'processing'},
    {id: 3, name: '<PERSON><PERSON><PERSON> thành', type: 'completed'},
    {id: 4, name: '<PERSON><PERSON>y', type: 'cancelled'},
  ];

  const getFilteredStatusOptions = (): StatusOption[] => {
    if (item?.Status === 1) {
      return statusOptions.filter(
        status => status.type === 'processing' || status.type === 'cancelled',
      );
    } else if (item?.Status === 2) {
      return statusOptions.filter(
        status => status.type === 'completed' || status.type === 'cancelled',
      );
    }
    return statusOptions;
  };

  const handleStatusSelect = (statusType: string) => {
    setSelectedStatus(statusType);
    if (statusType !== 'cancelled') {
      setCancelReason('');
    }
  };

  const handleUpdateStatus = async () => {
    if (selectedStatus) {
      if (selectedStatus === 'cancelled' && !cancelReason.trim()) {
        return;
      }

      try {
        const itemWithCancelReason =
          selectedStatus === 'cancelled'
            ? {...item, cancelReason: cancelReason.trim()}
            : item;

        await handleUpdateStatusProcessOrder(
          itemWithCancelReason,
          selectedStatus,
        );
        onClose(); // Close modal on success
      } catch (error) {
        console.error('Error updating order status:', error);
        // Error handling should be done by the parent component
        // that provides handleUpdateStatusProcessOrder
      }
    }
  };

  const isUpdateDisabled =
    !selectedStatus || (selectedStatus === 'cancelled' && !cancelReason.trim());

  return {
    selectedStatus,
    cancelReason,
    setCancelReason,
    statusOptions: getFilteredStatusOptions(),
    handleStatusSelect,
    handleUpdateStatus,
    isUpdateDisabled,
  };
};
