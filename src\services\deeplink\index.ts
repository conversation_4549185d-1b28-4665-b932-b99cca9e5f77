/**
 * Deep Link Service
 * Centralized exports for the deeplink system
 */

export {default as DeepLinkManager} from './DeepLinkManager';
export * from './DeepLinkTypes';
export * from './DeepLinkUtils';

// Re-export commonly used functions
export {
  validateDeepLink,
  parseDeepLink,
  hasValidId,
  createDeepLink,
  createUniversalLink,
} from './DeepLinkUtils';

// Re-export singleton instance
import DeepLinkManager from './DeepLinkManager';
export const deepLinkManager = DeepLinkManager.getInstance();
