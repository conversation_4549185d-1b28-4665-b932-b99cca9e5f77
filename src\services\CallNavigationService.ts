/**
 * CallNavigationService
 * Handles navigation to CallScreen for incoming and outgoing calls
 */

import { navigate, RootScreen } from '../router/router';
import WebRTCService from './webrtc/WebRTCService';
import IncomingCallOverlayService from './IncomingCallOverlayService';
import { CallStatus, CallData } from './webrtc/types/WebRTCTypes';

class CallNavigationService {
  private static instance: CallNavigationService;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): CallNavigationService {
    if (!CallNavigationService.instance) {
      CallNavigationService.instance = new CallNavigationService();
    }
    return CallNavigationService.instance;
  }

  /**
   * Initialize the service
   */
  public initialize(): void {
    if (this.isInitialized) {
      console.log('📞 CallNavigationService already initialized');
      return;
    }

    console.log('📞 Initializing CallNavigationService');

    // Set up WebRTC callbacks
    WebRTCService.setCallbacks({
      onCallStatusChanged: this.handleCallStatusChanged.bind(this),
      onCallEnded: this.handleCallEnded.bind(this),
      onCallFailed: this.handleCallFailed.bind(this),
    });

    // Listen to IncomingCallOverlayService events
    IncomingCallOverlayService.on('acceptCall', this.handleAcceptCallFromOverlay.bind(this));
    IncomingCallOverlayService.on('rejectCall', this.handleRejectCallFromOverlay.bind(this));

    this.isInitialized = true;
    console.log('✅ CallNavigationService initialized');
  }

  /**
   * Handle call status changes
   */
  private handleCallStatusChanged(status: CallStatus, callData: CallData): void {
    console.log('📞 CallNavigationService - Call status changed:', status, callData);

    switch (status) {
      case CallStatus.RINGING:
        if (callData.direction === 'incoming') {
          this.handleIncomingCall(callData);
        }
        break;

      case CallStatus.CALLING:
        // For outgoing calls, CallScreen should already be navigated from ContactsScreen
        break;

      case CallStatus.CONNECTED:
        // Call is connected, make sure we're on CallScreen
        this.ensureCallScreenIsActive(callData);
        break;

      default:
        break;
    }
  }

  /**
   * Handle incoming call
   */
  private handleIncomingCall(callData: CallData): void {
    console.log('📞 Handling incoming call:', callData);

    // Show incoming call overlay notification
    IncomingCallOverlayService.showIncomingCall({
      callerName: callData.caller.name,
      callerAvatar: callData.caller.avatar,
      callerId: callData.caller.id,
    });
  }

  /**
   * Handle accept call from overlay
   */
  private handleAcceptCallFromOverlay(data: any): void {
    console.log('📞 Accept call from overlay:', data);

    // Accept the call via WebRTC
    WebRTCService.acceptCall().then(() => {
      // Navigate to CallScreen
      const currentCall = WebRTCService.getCurrentCall();
      if (currentCall) {
        this.navigateToCallScreen(currentCall, true);
      }
    }).catch((error) => {
      console.error('❌ Error accepting call:', error);
    });
  }

  /**
   * Handle reject call from overlay
   */
  private handleRejectCallFromOverlay(data: any): void {
    console.log('📞 Reject call from overlay:', data);

    // Reject the call via WebRTC
    WebRTCService.rejectCall();
  }

  /**
   * Handle call ended
   */
  private handleCallEnded(reason: string, callData: CallData): void {
    console.log('📞 Call ended:', reason, callData);
    
    // Hide incoming call overlay if showing
    if (IncomingCallOverlayService.isShowingNotification()) {
      IncomingCallOverlayService.hideIncomingCall();
    }
  }

  /**
   * Handle call failed
   */
  private handleCallFailed(error: string, callData: CallData): void {
    console.log('📞 Call failed:', error, callData);
    
    // Hide incoming call overlay if showing
    if (IncomingCallOverlayService.isShowingNotification()) {
      IncomingCallOverlayService.hideIncomingCall();
    }
  }

  /**
   * Navigate to CallScreen
   */
  private navigateToCallScreen(callData: CallData, isIncoming: boolean): void {
    console.log('📞 Navigating to CallScreen:', { callData, isIncoming });

    const contact = isIncoming ? callData.caller : callData.receiver;
    const currentUser = isIncoming ? callData.receiver : callData.caller;

    navigate(RootScreen.CallScreen, {
      contact: {
        Id: contact.id,
        Name: contact.name,
        AvatarUrl: contact.avatar,
      },
      isIncoming,
      currentUser: {
        Id: currentUser.id,
        Name: currentUser.name,
        AvatarUrl: currentUser.avatar,
      },
    });
  }

  /**
   * Ensure CallScreen is active for connected calls
   */
  private ensureCallScreenIsActive(callData: CallData): void {
    // This could be enhanced to check current screen and navigate if needed
    // For now, we assume CallScreen is already active
    console.log('📞 Call connected, CallScreen should be active');
  }

  /**
   * Start outgoing call and navigate to CallScreen
   */
  public async startOutgoingCall(
    targetUser: { id: string; name: string; avatar?: string },
    currentUser: { id: string; name: string; avatar?: string }
  ): Promise<void> {
    try {
      console.log('📞 Starting outgoing call to:', targetUser.name);

      // Start the call via WebRTC
      await WebRTCService.startCall(
        {
          id: targetUser.id,
          name: targetUser.name,
          avatar: targetUser.avatar,
        },
        {
          id: currentUser.id,
          name: currentUser.name,
          avatar: currentUser.avatar,
        }
      );

      // Navigate to CallScreen
      navigate(RootScreen.CallScreen, {
        contact: {
          Id: targetUser.id,
          Name: targetUser.name,
          AvatarUrl: targetUser.avatar,
        },
        isIncoming: false,
        currentUser: {
          Id: currentUser.id,
          Name: currentUser.name,
          AvatarUrl: currentUser.avatar,
        },
      });

      console.log('✅ Outgoing call started and navigated to CallScreen');
    } catch (error) {
      console.error('❌ Error starting outgoing call:', error);
      throw error;
    }
  }

  /**
   * Cleanup
   */
  public cleanup(): void {
    console.log('📞 Cleaning up CallNavigationService');
    
    // Remove listeners
    IncomingCallOverlayService.off('acceptCall', this.handleAcceptCallFromOverlay.bind(this));
    IncomingCallOverlayService.off('rejectCall', this.handleRejectCallFromOverlay.bind(this));
    
    this.isInitialized = false;
  }
}

export default CallNavigationService.getInstance();
