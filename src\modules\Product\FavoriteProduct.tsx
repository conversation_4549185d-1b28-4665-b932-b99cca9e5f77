import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import Header from './FavoriteProductComponent/Header';
import ProductItem from './FavoriteProductComponent/ProductItem';
import SearchBar from '../../components/shop/Search';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
import {ColorThemes} from '../../assets/skin/colors';
import {DataController} from '../../base/baseController';
import {getProduct} from '../../redux/actions/productAction';
import {useDispatch, useSelector} from 'react-redux';
import {RootState} from '../../redux/store/store';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import EmptyPage from '../../Screen/emptyPage';

interface FavoriteProductItem {
  Id: string;
  ProductId: string;
  CustomerId: string;
  DateCreated: number;
  Product?: any;
}

// Skeleton component for favorite products
const FavoriteProductSkeleton = () => (
  <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
    <View style={styles.skeletonItem}>
      {/* Product Image */}
      <View style={styles.skeletonImage} />

      {/* Info and Actions Container */}
      <View style={styles.skeletonInfoActionContainer}>
        {/* Product Info */}
        <View style={styles.skeletonInfoContainer}>
          {/* Product Name */}
          <View style={styles.skeletonTitle} />
          <View style={styles.skeletonTitleSecond} />

          {/* Price */}
          <View style={styles.skeletonPrice} />
        </View>

        {/* Actions Container */}
        <View style={styles.skeletonActionsContainer}>
          <View style={styles.skeletonActionButton} />
          <View style={styles.skeletonActionButton} />
        </View>
      </View>
    </View>
  </SkeletonPlaceholder>
);

// Header Skeleton Component
const HeaderSkeleton = () => (
  <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
    <View style={styles.skeletonHeader}>
      <View style={styles.skeletonHeaderTitle} />
      <View style={styles.skeletonHeaderCount} />
    </View>
  </SkeletonPlaceholder>
);

// Skeleton List Component
const FavoriteProductSkeletonList = () => (
  <View style={styles.container}>
    <HeaderSkeleton />
    {Array.from({length: 6}).map((_, index) => (
      <View key={index}>
        <FavoriteProductSkeleton />
        {index < 5 && <View style={styles.separator} />}
      </View>
    ))}
  </View>
);

const FavouriteProduct = () => {
  // Get customer ID from Redux (only thing we need from Redux)
  const customerId = useSelector((state: RootState) => state.customer.data?.Id);

  // Local states
  const [data, setData] = useState<FavoriteProductItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [query, setQuery] = useState('');
  const [canLoadMore, setCanLoadMore] = useState(true);

  const size = 10;
  const searchTimeoutRef = useRef<any | null>(null);
  const isLoadingRef = useRef(false);
  const lastEndReachedTime = useRef(0);
  const controller = new DataController('ProductFavorite');

  // API functions
  const getFavoriteProducts = async (
    pageNum: number,
    searchText: string = '',
  ): Promise<{data: FavoriteProductItem[]; totalCount: number}> => {
    try {
      const res = await controller.aggregateList({
        page: pageNum,
        size: size,
        searchRaw: `@CustomerId:{${customerId}} ${searchText}`,
        sortby: [{prop: 'DateCreated', direction: 'DESC'}],
      });
      if (res?.code === 200) {
        const favoriteData = res.data;

        if (favoriteData.length === 0) {
          return {data: [], totalCount: res.totalCount};
        }

        const productIds = favoriteData
          .map((item: any) => item.ProductId)
          .filter(Boolean);

        if (productIds.length === 0) {
          return {data: [], totalCount: res.totalCount};
        }

        const listProduct = await getProduct(productIds);

        const validData = favoriteData.filter((item: any) => {
          if (!item.ProductId) return false;

          item.Product = listProduct.find(
            (product: any) => product.Id === item.ProductId,
          );

          if (item.Product) {
            item.Product.IsFavorite = true;
            return true;
          }
          return false;
        });

        return {data: validData, totalCount: res.totalCount};
      }
      return {data: [], totalCount: 0};
    } catch (error) {
      console.error('Error fetching favorite products:', error);
      return {data: [], totalCount: 0};
    }
  };

  // Load initial data
  const loadInitialData = async () => {
    if (!customerId) return;

    setLoading(true);
    try {
      const result = await getFavoriteProducts(1, query);
      setData(result.data);
      setTotalCount(result.totalCount);
      setPage(1);
      setCanLoadMore(result.data.length < result.totalCount);
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load more data
  const loadMoreData = async () => {
    console.log('loadMoreData called - checking conditions...');

    if (isLoadingRef.current || loadingMore || !canLoadMore || !customerId) {
      console.log('loadMoreData blocked:', {
        isLoadingRef: isLoadingRef.current,
        loadingMore,
        canLoadMore,
        customerId: !!customerId,
      });
      return;
    }

    // Kiểm tra thêm điều kiện
    if (data.length >= totalCount && totalCount > 0) {
      console.log('loadMoreData blocked: already have all data');
      setCanLoadMore(false);
      return;
    }

    console.log('loadMoreData executing - page:', page + 1);
    setLoadingMore(true);
    isLoadingRef.current = true;

    try {
      const nextPage = page + 1;
      const result = await getFavoriteProducts(nextPage, query);

      if (result.data.length > 0) {
        setData(prevData => {
          const newData = [...prevData, ...result.data];
          console.log(
            'Data updated:',
            newData.length,
            'Total:',
            result.totalCount,
          );
          return newData;
        });
        setPage(nextPage);
        setCanLoadMore(data.length + result.data.length < result.totalCount);
      } else {
        console.log('No more data available');
        setCanLoadMore(false);
      }
    } catch (error) {
      console.error('Error loading more data:', error);
      setCanLoadMore(false);
    } finally {
      setLoadingMore(false);
      isLoadingRef.current = false;
    }
  };

  // Refresh data
  const refreshData = async () => {
    if (!customerId) return;

    setRefreshing(true);
    lastEndReachedTime.current = 0; // Reset throttle

    try {
      const result = await getFavoriteProducts(1, query);
      setData(result.data);
      setTotalCount(result.totalCount);
      setPage(1);
      setCanLoadMore(result.data.length < result.totalCount);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Search function
  const handleSearch = useCallback(
    (text: string) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(async () => {
        if (!customerId) return;

        setQuery(text);
        setLoading(true);
        lastEndReachedTime.current = 0; // Reset throttle

        try {
          const result = await getFavoriteProducts(1, text);
          setData(result.data);
          setTotalCount(result.totalCount);
          setPage(1);
          setCanLoadMore(result.data.length < result.totalCount);
        } catch (error) {
          console.error('Error searching:', error);
        } finally {
          setLoading(false);
        }
      }, 1000);
    },
    [customerId],
  );

  // Effects
  useEffect(() => {
    loadInitialData();

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [customerId]);

  // Handlers
  const onEndReached = useCallback(() => {
    const now = Date.now();

    // Throttle: chỉ cho phép gọi mỗi 2 giây
    if (now - lastEndReachedTime.current < 2000) {
      console.log('onEndReached throttled');
      return;
    }

    // Kiểm tra điều kiện trước khi load more
    if (
      isLoadingRef.current ||
      loadingMore ||
      loading ||
      !canLoadMore ||
      data.length === 0 ||
      data.length >= totalCount
    ) {
      console.log('onEndReached blocked - conditions not met');
      return;
    }

    lastEndReachedTime.current = now;
    console.log(
      'onEndReached triggered - Data length:',
      data.length,
      'Total:',
      totalCount,
    );
    loadMoreData();
  }, [loadingMore, loading, canLoadMore, data.length, totalCount]);

  const onRefresh = useCallback(() => {
    refreshData();
  }, []);

  return (
    <View style={styles.screen}>
      <InforHeader title={'Sản phẩm yêu thích'} />
      <SearchBar setDataSearch={handleSearch} />
      {loading ? (
        <FavoriteProductSkeletonList />
      ) : (
        <View style={{flex: 1}}>
          <View style={styles.container}>
            <Header title="Sản phẩm yêu thích" productCount={data.length} />
            <FlatList
              data={data}
              renderItem={({item}) => (
                <ProductItem
                  item={item.Product}
                  showFavorite={true}
                  onFavoritePress={async id => {
                    // get delete favorite product and remove item in data
                    const productFavorite = await controller.getListSimple({
                      page: 1,
                      size: 1,
                      query: `@ProductId:{${id}} @CustomerId:{${customerId}}`,
                    });
                    if (productFavorite.code === 200) {
                      await controller
                        .delete([productFavorite.data[0].Id])
                        .then(value => {
                          if (value?.code === 200) {
                            showSnackbar({
                              message: 'Đã bỏ yêu thích',
                              status: ComponentStatus.SUCCSESS,
                            });
                            setData(
                              data.filter(itemData => itemData.Id !== item.Id),
                            );
                          }
                        });
                    }
                  }}
                />
              )}
              keyExtractor={item => item.Id}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={[ColorThemes.light.primary_main_color]}
                  tintColor={ColorThemes.light.primary_main_color}
                />
              }
              onEndReached={onEndReached}
              onEndReachedThreshold={0.3}
              ListFooterComponent={
                loadingMore ? (
                  <View style={styles.loadingFooter}>
                    <ActivityIndicator
                      size="small"
                      color={ColorThemes.light.primary_main_color}
                    />
                  </View>
                ) : null
              }
              ListEmptyComponent={
                <View style={styles.center}>
                  <EmptyPage />
                </View>
              }
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: ColorThemes.light.white,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  separator: {
    height: 16,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingFooter: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  // Skeleton styles
  skeletonItem: {
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    marginVertical: 8,
    paddingHorizontal: 16,
  },
  skeletonImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
  },
  skeletonInfoActionContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 12,
  },
  skeletonInfoContainer: {
    flex: 1,
    justifyContent: 'center',
    gap: 6,
  },
  skeletonTitle: {
    width: '80%',
    height: 16,
    borderRadius: 4,
  },
  skeletonTitleSecond: {
    width: '60%',
    height: 14,
    borderRadius: 4,
  },
  skeletonPrice: {
    width: 80,
    height: 16,
    borderRadius: 4,
  },
  skeletonActionsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  skeletonActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  skeletonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  skeletonHeaderTitle: {
    width: 150,
    height: 20,
    borderRadius: 4,
  },
  skeletonHeaderCount: {
    width: 60,
    height: 16,
    borderRadius: 4,
  },
});

export default FavouriteProduct;
