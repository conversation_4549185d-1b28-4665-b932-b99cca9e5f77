import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';

export enum RootScreen {
  order = 'ORDER',
  TransferCANPoint = 'TransferCANPoint',
  AllHotProductsPage = 'AllHotProductsPage',
  BiometricSetting = 'BiometricSetting',
  CartPage = 'CartPage',
  ChartReport = 'ChartReport',
  CheckoutPage = 'CheckoutPage',
  ChatMain = 'ChatMain',
  ChatList = 'ChatList',
  ChatRoom = 'ChatRoom',
  CreateGroup = 'CreateGroup',
  Contacts = 'Contacts',
  CallHistory = 'CallHistory',
  ConfigAffiliate = 'ConfigAffiliate',
  CreateNewProduct = 'CreateNewProduct',
  CreateReviewOrder = 'CreateReviewOrder',
  CreateReviewOrderDetail = 'CreateReviewOrderDetail',
  DetailNews = 'DetailNews',
  DetailEvent = 'DetailEvent',
  DrawerNavigation = 'DrawerNavigation',
  MyAddress = 'MyAddress',
  EditAddress = 'EditAddress',
  FAQView = 'FAQView',
  ForgotPass = 'ForgotPass',
  HotProductsDemo = 'HotProductsDemo',
  Instructors = 'Instructors',
  Intro = 'Intro',
  LableProduct = 'LableProduct',
  ListProductCreate = 'ListProductCreate',
  ListItemChild = 'ListItemChild',
  login = 'Login',
  ManageProduct = 'ManageProduct',
  MyWallet = 'MyWallet',
  MyWalletProfile = 'MyWalletProfile',
  navigateCommunityParent = 'navigateCommunityParent',
  navigateCommunityView = 'navigateCommunityView',
  navigateEComParent = 'navigateEComParent',
  navigateEComView = 'navigateEComView',
  navigateSakupiParent = 'navigateSakupiParent',
  navigateSakupiView = 'navigateSakupiView',
  NotifCommunity = 'NotifCommunity',
  Notification = 'Notification',
  OrderDetail = 'OrderDetail',
  OrderCustomerDetail = 'OrderCustomerDetail',
  OrderDetailPage = 'OrderDetailPage',
  OriginProduct = 'OriginProduct',
  PolicyView = 'PolicyView',
  ProductDetail = 'ProductDetail',
  ProductListByCategory = 'ProductListByCategory',
  PurchaseHistory = 'PurchaseHistory',
  RegisterShop = 'RegisterShop',
  Review = 'Review',
  SearchIndex = 'SearchIndex',
  SettingProfile = 'SettingProfile',
  Shop = 'Shop',
  splashView = 'Splash',
  TransactionHistory = 'TransactionHistory',
  VnpayPaymentScreen = 'VnpayPaymentScreen',
  TreeAffiliateDetail = 'TreeAffiliateDetail',
  NewsScreen = 'NewsScreen',
  FavoriteProduct = 'FavoriteProduct',
  GiftExchange = 'GiftExchange',
  GiftDetail = 'GiftDetail',
  ProfileRankScreen = 'ProfileRankScreen',
  TwoFactorAuth = 'TwoFactorAuth',
  AccountAuth = 'AccountAuth',
  ShopPromotion = 'ShopPromotion',
  PaymentSetting = 'PaymentSetting',
  ProductIndex = 'ProductIndex',
  WithDrawMoney = 'WithDrawMoney',
  RatingScreen = 'RatingScreen',
  RatingForAllScreen = 'RatingForAllScreen',
  InforShopView = 'InforShopView',
  OrderDetailPageForShop = 'OrderDetailPageForShop',
  ListByHashtagScreen = 'ListByHashtagScreen',
  // community
  PostDetail = 'PostDetail',
  createPost = 'createPost',
  ProfileCommunity = 'ProfileCommunity',
  SearchCommunity = 'SearchCommunity',
  ProductShopScreen = 'ProductShopScreen',
  // call
  CallScreen = 'CallScreen',

  BonusFundPage = 'BonusFundPage',
  LanguageSelection = 'LanguageSelection',
}

export const navigationRef = createNavigationContainerRef();
export function navigate(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.navigate(routeName, params));
    return true;
  }
  return false;
}

export function navigateBack() {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.goBack());
    return true;
  }
  return false;
}
export function navigateBackWithParams(screenName: string, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.navigate({
        name: screenName,
        params: params,
      }),
    );
    return true;
  }
  return false;
}
export function navigateReset(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: routeName, params}],
      }),
    );
    return true;
  }
  return false;
}
