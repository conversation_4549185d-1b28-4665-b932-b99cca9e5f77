import React from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  Pressable,
  ScrollView,
  Dimensions,
} from 'react-native';
import {AppSvg, ListTile, Rating} from 'wini-mobile-components';
import {TypeMenuReview} from '../../../Config/Contanst';
import ReviewInfo from '../../../modules/rating/component/ReviewInfo';
import {TypoSkin} from '../../../assets/skin/typography';
import iconSvg from '../../../svg/icon';
import ConfigAPI from '../../../Config/ConfigAPI';
import {Ultis} from '../../../utils/Utils';
import {ColorThemes} from '../../../assets/skin/colors';
import {navigate, RootScreen} from '../../../router/router';
import FastImage from '@d11/react-native-fast-image';
import RenderHTML from 'react-native-render-html';
import ClickableImage from '../../../components/ClickableImage';

const width = Dimensions.get('window').width;

const ReviewProductIteCard = (
  {item, index}: {item: any; index: number},
  type: string,
) => {
  // Render stars based on rating
  const renderStars = (rating: number) => {
    return (
      <View style={styles.starsContainer}>
        <Rating value={rating} size={20} fillColor="#FFC043" />
      </View>
    );
  };
  return (
    <ListTile
      key={item.id}
      leading={
        <View
          style={[
            styles.avatarContainer,
            item.userAvatar && {
              backgroundColor: ColorThemes.light.neutral_main_background_color,
            },
          ]}>
          {item?.Customer?.AvatarUrl ? (
            <FastImage
              source={{uri: ConfigAPI.urlImg + item?.Customer?.AvatarUrl}}
              style={{width: 40, height: 40, borderRadius: 20}}
            />
          ) : (
            <Text style={styles.avatarText}>
              {item?.Customer?.Name?.charAt(0) ?? ''}
            </Text>
          )}
        </View>
      }
      listtileStyle={{alignItems: 'flex-start'}}
      title={
        <View style={styles.reviewHeader}>
          <Text style={styles.userName}>{item?.Customer?.Name || ''}</Text>
          {renderStars(item?.Value)}
        </View>
      }
      subtitle={
        <View style={styles.reviewContent}>
          <Text style={styles.comment}>{item?.Description}</Text>
          <RenderHTML contentWidth={width} source={{html: item?.Content}} />
          {/* show Img  */}
          {item?.ListImg?.length > 0 && (
            <ScrollView
              horizontal={true}
              showsHorizontalScrollIndicator={false}
              style={{marginVertical: 8, height: 56}}
              contentContainerStyle={{flexGrow: 1, gap: 8}}>
              {item.ListImg?.length > 0 &&
                item.ListImg?.split(',')?.map(
                  (image: string, index: number) => (
                    <ClickableImage
                      key={`item-${index}`}
                      source={{uri: ConfigAPI.urlImg + image}}
                      style={{
                        width: 56,
                        height: 56,
                        borderRadius: 8,
                        borderColor:
                          ColorThemes.light.neutral_main_border_color,
                        borderWidth: 1,
                      }}
                    />
                  ),
                )}
            </ScrollView>
          )}
          {type == TypeMenuReview.Product ? (
            item?.Product && (
              <ListTile
                key={index}
                style={{padding: 0}}
                onPress={() =>
                  navigate(RootScreen.ProductDetail, {
                    id: item?.Product.Id,
                  })
                }
                leading={
                  <FastImage
                    source={{uri: item?.Product?.Img}}
                    style={{
                      width: 56,
                      height: 56,
                      borderRadius: 8,
                      borderColor: ColorThemes.light.neutral_main_border_color,
                      borderWidth: 1,
                    }}
                  />
                }
                title={item?.Product.Name}
                subtitle={
                  <View style={{gap: 4}}>
                    {/* <Text style={styles.size}>
                      {item?.Product?.Description}
                    </Text> */}
                    <Text style={styles.size}>
                      Hoàn tiền: {item?.Refund ? Ultis.money(item?.Refund) : 0}{' '}
                      CANPOINT
                    </Text>
                  </View>
                }
              />
            )
          ) : (
            <View style={styles.orderDetail}>
              <AppSvg SvgSrc={iconSvg.orderReviewAction} size={24} />
              <Text style={styles.orderName}>{item?.Order?.Code}</Text>
            </View>
          )}
          <Text style={styles.date}>{item.date}</Text>
        </View>
      }
      style={styles.listTile}
    />
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginTop: 26,
    marginRight: 20,
    marginLeft: 25,
  },

  review: {
    flexDirection: 'column',
    borderBottomWidth: 0.2,
    borderBottomColor: '#00FFFF',
    width: '100%',
  },

  description: {
    ...TypoSkin.body3,
    marginVertical: 5,
  },
  imagesProduct: {
    marginTop: 10,
    height: 100,
    width: '100%',
  },
  images: {
    flexDirection: 'row',
    marginTop: 10,
  },
  productImage: {
    width: 100,
    height: 100,
    marginRight: 10,
    borderRadius: 5,
  },
  reviewDetail: {
    flexDirection: 'row',
    alignContent: 'center',
    paddingBottom: 13,
    marginTop: 10,
  },
  avatarProduct: {
    width: 80,
    height: 80,
    borderRadius: 20,
    marginRight: 10,
  },
  imageDetail: {
    borderWidth: 5,
    borderRadius: 50,
    width: 50,
    height: 50,
    marginRight: 10,
    borderColor: '#F8F8FF',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  avatarDetail: {
    width: 40,
    height: 40,
    borderRadius: 50,
  },
  tag: {
    ...TypoSkin.title4,
    fontWeight: '700',
    color: '#555',
  },
  size: {
    fontSize: 12,
    color: '#888',
  },
  orderDetail: {
    flexDirection: 'row',
    margin: 10,
  },
  orderName: {
    ...TypoSkin.body2,
    color: 'blue',
    marginLeft: 5,
    width: '100%',
  },
  scrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 20,
  },

  container: {
    flex: 1,
    width: '100%',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  // Search styles
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    paddingHorizontal: 12,
    paddingBottom: 4,
  },
  searchInput: {
    flex: 1,
    color: ColorThemes.light.neutral_text_body_color,
    height: 35,
  },
  filterButton: {
    padding: 4,
  },
  filterContainer: {
    marginTop: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  dateFilterRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  dateButton: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 4,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  dateButtonText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_body_color,
  },
  actionButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  resetButton: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  resetButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_text_body_color,
  },
  searchButton: {
    flex: 1,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.primary_main_color,
    marginLeft: 8,
  },
  searchButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  // Original styles
  loadingContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingBottom: 16,
  },
  listTile: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    padding: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  avatarContainer: {
    marginTop: 8,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.primary_main_color,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  avatarText: {
    ...TypoSkin.title3,
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  reviewHeader: {
    flexDirection: 'column',
    marginBottom: 4,
  },
  userName: {
    ...TypoSkin.subtitle1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 4,
    alignItems: 'center',
  },
  reviewContent: {
    marginTop: 4,
  },
  comment: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_body_color,
    marginBottom: 8,
  },
  date: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  headerContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_divider_color,
  },
  headerTitle: {
    ...TypoSkin.subtitle1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  reviewCount: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  footerLoader: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ReviewProductIteCard;
