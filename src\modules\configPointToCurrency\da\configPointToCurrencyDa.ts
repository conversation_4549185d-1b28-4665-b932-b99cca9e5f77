import {DataController} from '../../../base/baseController';
import {ConfigPointToCurrencyType} from '../../../redux/types/configPointToCurrencyType';

class ConfigPointToCurrencyDa {
  private controller: DataController;

  constructor() {
    this.controller = new DataController('ConfigPointToCurrency');
  }

  /**
   * Lấy ConfigPointToCurrency theo area
   * @param area - Khu vực (ví dụ: "vi", "en")
   * @returns ConfigPointToCurrencyType hoặc null nếu không tìm thấy
   */
  async getConfigByArea(
    area: string,
  ): Promise<ConfigPointToCurrencyType | null> {
    try {
      const res = await this.controller.getListSimple({
        query: `@Area: (${area})`,
        page: 1,
        size: 1,
        returns: ['Id', 'Name', 'Sort', 'Area', 'Rate', 'DateCreated'],
      });
      if (res?.code === 200 && res.data && res.data.length > 0) {
        return res.data[0] as ConfigPointToCurrencyType;
      }
      return null;
    } catch (error) {
      console.error('Error getting config point to currency by area:', error);
      return null;
    }
  }

  /**
   * Lấy tất cả ConfigPointToCurrency
   * @returns Danh sách ConfigPointToCurrencyType
   */
  async getAllConfigs(): Promise<ConfigPointToCurrencyType[]> {
    try {
      const res = await this.controller.getAll();

      if (res?.code === 200) {
        return res.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error getting all config point to currency:', error);
      return [];
    }
  }
}

export default ConfigPointToCurrencyDa;
