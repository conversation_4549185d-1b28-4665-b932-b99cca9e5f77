import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';

interface ActionButtonsProps {
  onUpdateStatus: () => void;
  isDisabled: boolean;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onUpdateStatus,
  isDisabled,
}) => {
  return (
    <View style={styles.actionButtonsContainer}>
      <View style={styles.additionalButtons}>
        <TouchableOpacity
          style={[styles.additionalButton, isDisabled && styles.disabledButton]}
          onPress={onUpdateStatus}
          disabled={isDisabled}
          accessibilityLabel="Cập nhật trạng thái đơn hàng"
          accessibilityRole="button"
          accessibilityState={{disabled: isDisabled}}>
          <Text
            style={[
              styles.additionalButtonText,
              isDisabled && styles.disabledButtonText,
            ]}>
            Cập nhật trạng thái
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  actionButtonsContainer: {
    marginTop: 8,
  },
  additionalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  additionalButton: {
    backgroundColor: '#ffc107',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 14,
    alignItems: 'center',
    flex: 1,
    shadowColor: '#ffc107',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 193, 7, 0.3)',
  },
  additionalButtonText: {
    ...TypoSkin.title3,
    color: '#b71800',
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 20,
  },
  disabledButton: {
    backgroundColor: '#f5f5f5',
    borderColor: '#e0e0e0',
    shadowOpacity: 0,
    elevation: 0,
  },
  disabledButtonText: {
    color: '#999999',
    fontWeight: '500',
  },
});

export default ActionButtons;
