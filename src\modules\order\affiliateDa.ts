import {DataController} from '../../base/baseController';
import {randomGID, Ultis} from '../../utils/Utils.js';
import {fnFilial} from 'modules/order/functions/fnFilial';

// Constants for reward transaction types
const TRANSACTION_TYPE = {
  COMMISSION: 1,
  TRANSFER: 2,
  COMMISSION_DEDUCTION: 3,
} as const;

// Interface for better type safety
interface RewardTransaction {
  Id: string;
  Filial: number;
  OrderDetailId: string | null;
  CustomerId: string;
  Value: number;
  Status: number;
  Type: number;
  OrderId: string;
  Name: string;
  DateCreated: number;
  Description: string;
  Code: string;
  ShopCateId?: string;
  ShopRewardId?: string;
  RewardId?: string;
}

export default class AffiliateDa {
  private _historyReward: DataController;
  constructor() {
    this._historyReward = new DataController('HistoryReward');
  }
  /**
   * Process affiliate rewards for an order
   * Delegates to the fnFilial function in functions/fnFilial.ts
   */
  async createPendingReward(
    shopid: string,
    orderid: string,
    customerId: string,
    status: number,
    code: string,
  ): Promise<void> {
    return fnFilial(shopid, orderid, customerId, status, code);
  }

  /**
   * Creates two transactions: one for the sender (deduction) and one for the receiver (addition).
   */
  async createTransferTransactions(
    senderId: string,
    receiverId: string,
    amount: number,
    orderId: string,
    code: string,
    description?: string,
  ): Promise<RewardTransaction[]> {
    try {
      const currentTime = Date.now();
      const baseTransaction = {
        Id: randomGID(),
        Filial: 0,
        OrderDetailId: null,
        Status: 1, // Active transaction
        Type: TRANSACTION_TYPE.TRANSFER,
        OrderId: orderId,
        DateCreated: currentTime,
        Code: Ultis.randomString(10),
      };

      const senderTransaction: RewardTransaction = {
        ...baseTransaction,
        CustomerId: senderId,
        Value: -amount,
        Name: `Chuyển tiền đến ${receiverId}`,
        Description: description || `Chuyển tiền từ đơn hàng #${code}`,
      };

      const receiverTransaction: RewardTransaction = {
        ...baseTransaction,
        Id: randomGID(),
        CustomerId: receiverId,
        Value: amount,
        Name: `Nhận tiền từ ${senderId}`,
        Description: description || `Nhận tiền từ đơn hàng #${code}`,
      };

      return [senderTransaction, receiverTransaction];
    } catch (error) {
      console.error('---err createTransferTransactions', error);
      throw error;
    }
  }

  /**
   * Update all reward transactions for an order to status 2 (completed/success)
   * @param orderId - The order ID to update rewards for
   */
  async updateDoneTransactionReward(orderId: string): Promise<boolean> {
    try {
      // Get all reward transactions for this order
      const result = await this._historyReward.getListSimple({
        page: 1,
        size: 1000,
        query: `@OrderId:{${orderId}}`,
        returns: ['Id', 'Status'],
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });

      if (result?.code === 200 && result.data && result.data.length > 0) {
        // Prepare updates: change all rewards to status 2
        const updates = result.data.map((reward: any) => ({
          Id: reward.Id,
          Status: 2, // Update to completed/success status
        }));

        // Update all rewards
        const updateResult = await this._historyReward.edit(updates);

        if (updateResult?.code === 200) {
          console.log(
            `Successfully updated ${updates.length} reward transactions for order ${orderId} to status 2`,
          );
          return true;
        } else {
          console.error('Failed to update reward transactions:', updateResult);
          return false;
        }
      } else {
        console.log(`No reward transactions found for order ${orderId}`);
        return true; // No transactions to update is considered success
      }
    } catch (error) {
      console.error('Error updating reward transactions:', error);
      return false;
    }
  }

  async updateRejectTransactionReward(orderId: string): Promise<boolean> {
    try {
      // Get all reward transactions for this order
      const result = await this._historyReward.getListSimple({
        page: 1,
        size: 1000,
        query: `@OrderId:{${orderId}}`,
        returns: ['Id', 'Status'],
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });

      if (result?.code === 200 && result.data && result.data.length > 0) {
        // Prepare updates: change all rewards to status 2
        const updates = result.data.map((reward: any) => ({
          Id: reward.Id,
          Status: 3, // Update to completed/success status
        }));

        // Update all rewards
        const updateResult = await this._historyReward.edit(updates);

        if (updateResult?.code === 200) {
          console.log(
            `Successfully updated ${updates.length} reward transactions for order ${orderId} to status 2`,
          );
          return true;
        } else {
          console.error('Failed to update reward transactions:', updateResult);
          return false;
        }
      } else {
        console.log(`No reward transactions found for order ${orderId}`);
        return true; // No transactions to update is considered success
      }
    } catch (error) {
      console.error('Error updating reward transactions:', error);
      return false;
    }
  }
}
