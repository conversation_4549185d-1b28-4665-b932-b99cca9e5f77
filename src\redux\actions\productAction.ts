import {createAsyncThunk} from '@reduxjs/toolkit';
import {DataController} from '../../base/baseController';
import {Product} from '../models/product';
import {getImage} from './rootAction';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {RootState} from '../store/store';
import {randomGID} from '../../utils/Utils';

export const productAction = {
  find: async (
    config: {
      page?: number;
      size?: number;
      sortby?: any;
      searchRaw?: string;
    },
    customerId?: string | undefined,
  ) => {
    const controller = new DataController('Product');
    const res = await controller.aggregateList(config);
    if (res?.code === 200) {
      let newData = await getImage({items: res.data});
      newData = await getRatingProduct({items: newData});
      if (customerId) {
        newData = await checkFavorite(newData, customerId);
      }
      return newData;
    }
    return [];
  },
  update: async (products: Product[]) => {
    const controller = new DataController('Product');
    try {
      const res = await controller.edit(products);
      if (res?.code === 200) {
        return res.data;
      }
    } catch (error: any) {
      showSnackbar({message: error.message, status: ComponentStatus.ERROR});
    }
  },
};

async function getProduct(listProductId: string[]): Promise<Product[]> {
  try {
    if (!listProductId || listProductId.length === 0) {
      console.warn('getProduct: Empty or invalid product ID list');
      return [];
    }

    const controller = new DataController('Product');
    const res = await controller.getListSimple({
      page: 1,
      size: listProductId.length,
      query: `@Id:{${listProductId.join('|')}}`,
    });

    if (res?.code === 200) {
      const data = await getImage({items: res.data || []});
      console.log(
        `getProduct: Fetched ${data.length} products for ${listProductId.length} IDs`,
      );
      return data;
    } else {
      console.error('getProduct: API returned error code:', res?.code);
      return [];
    }
  } catch (error) {
    console.error('getProduct: Error fetching products:', error);
    return [];
  }
}

const checkFavorite = async (items: Product[], customerId: string) => {
  const controller = new DataController('ProductFavorite');

  const res = await controller.getListSimple({
    page: 1,
    size: 0,
    query: `@CustomerId:{${customerId}} @ProductId:{${items
      .map(item => item.Id)
      .join('|')}}`,
  });
  if (res?.code === 200) {
    items.forEach(item => {
      item.IsFavorite = res.data.some((p: any) => p.ProductId === item.Id);
    });

    return items;
  }
  return items;
};

const fetchProducts = createAsyncThunk<
  {data: Product[]; totalCount: number; isLoadMore?: boolean},
  {
    page?: number;
    size?: number;
    query?: string;
    searchRaw?: string;
    sortby?: any;
    isLoadMore?: boolean;
  },
  {state: RootState}
>('product/fetchProducts', async (config, thunkAPI: any) => {
  const controller = new DataController('Product');
  try {
    const params: any = {
      page: config?.page ?? 1,
      size: config?.size ?? 1000,
    };
    if (config?.query) params.query = config.query;
    if (config?.sortby) params.sortby = config.sortby;
    if (config?.searchRaw) params.searchRaw = config.searchRaw;
    const res = await controller.aggregateList(params);
    if (res?.code === 200) {
      const customerId = thunkAPI.getState().customer.data?.Id;
      let data = res.data;
      data = await getImage({items: data});
      data = await getRatingProduct({items: data});
      if (customerId) {
        data = await checkFavorite(data, customerId);
      }
      return {
        data,
        totalCount: res.totalCount,
        isLoadMore: config?.isLoadMore || false,
      };
    }
  } catch (err: any) {
    const errorMessage = err.message || 'An unknown error occurred';
    showSnackbar({message: errorMessage, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(errorMessage);
  }
});

const updateFavoriteProduct = createAsyncThunk<
  Product,
  Product,
  {state: RootState}
>('product/updateProduct', async (product, thunkAPI: any) => {
  const favoriteProductController = new DataController('ProductFavorite');
  const customerId = thunkAPI.getState().customer.data?.Id;

  try {
    if (product.IsFavorite) {
      const res = await favoriteProductController.add([
        {
          Id: randomGID(),
          ProductId: product.Id,
          CustomerId: customerId,
          Name: product.Name,
          DateCreated: Date.now(),
        },
      ]);
    } else {
      const productFavorite = await favoriteProductController.getListSimple({
        page: 1,
        size: 1,
        query: `@ProductId:{${product.Id}} @CustomerId:{${customerId}}`,
      });
      if (productFavorite.code === 200) {
        await favoriteProductController.delete([productFavorite.data[0].Id]);
      }
    }

    return product;
  } catch (error: any) {
    showSnackbar({message: error.message, status: ComponentStatus.ERROR});
    return thunkAPI.rejectWithValue(error.message);
  }
});

export const getRatingProduct = async ({items}: {items: Array<any>}) => {
  const ratingController = new DataController('Rating');
  if (!items || items.length === 0) {
    return items;
  }
  let lstId = items.map((item: any) => item.Id);
  lstId = [...new Set(lstId)];
  // const ratingResult = await ratingController.getListSimple({
  //   query: `@ProductId: {${lstId.join(' | ')}}`,
  // });
  // ratingResult by group
  const ratingResult = await ratingController.group({
    reducers:
      'LOAD * GROUPBY 1 @ProductId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
    searchRaw: `@ProductId: {${lstId.join(' | ')}}`,
  });
  if (ratingResult.code === 200) {
    items.map((item: any) => {
      const rating = ratingResult.data.filter(
        (a: any) => a.ProductId === item.Id,
      );
      item.rating =
        rating.length > 0 ? rating[0]?.TotalRate / rating[0]?.CountRate : 0;
      return item;
    });
  }
  return items;
};

export {getProduct, fetchProducts, updateFavoriteProduct};
