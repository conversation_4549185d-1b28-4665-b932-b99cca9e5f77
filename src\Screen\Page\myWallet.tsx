import React, {useState, useRef, useEffect} from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ImageBackground,
  Modal,
  Dimensions,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import {
  App<PERSON>utton,
  ComponentStatus,
  FLoading,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import QRCode from 'react-native-qrcode-svg';
import ViewShot from 'react-native-view-shot';
import {ColorThemes} from '../../assets/skin/colors';
import {CameraRollHelper} from '../../utils/CameraRollHelper';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ConfigAPI from '../../Config/ConfigAPI';
import {InforHeader} from '../Layout/headers/inforHeader';
import FastImage from '@d11/react-native-fast-image';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';
import {shareQRCode} from '../../features/share';
import {Ultis} from '../../utils/Utils';

const {width, height} = Dimensions.get('window');

const MyWallet = () => {
  const customer = useSelectorCustomerState().data;
  const [showQRModal, setShowQRModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const viewShotRef = useRef<ViewShot>(null);
  const dispatch = useDispatch<any>();

  useEffect(() => {
    if (!customer?.RefCode) {
      setLoading(true);
      // Nếu chưa có RefCode thì tạo mới
      const newCus = {
        ...customer,
        RefCode: Ultis.randomString(10),
      };
      dispatch(CustomerActions.createRefcode(newCus));
      dispatch(CustomerActions.getInfor());
    }
    setLoading(false);
  }, [customer]);

  const copyToClipboard = () => {
    Clipboard.setString(customer?.RefCode ?? '');
    showSnackbar({
      status: ComponentStatus.SUCCSESS,
      message: 'Đã sao chép mã QR',
    });
  };

  const downloadQRCode = async () => {
    try {
      if (viewShotRef.current && viewShotRef.current.capture) {
        // Hiển thị loading
        showSnackbar({
          status: ComponentStatus.INFOR,
          message: 'Đang tạo và lưu mã QR...',
        });

        const uri = await viewShotRef.current.capture();

        // Sử dụng CameraRollHelper để lưu ảnh vào thư viện ảnh
        const result = await CameraRollHelper.saveImage(uri, 'Chainivo');

        console.log('Saved to camera roll:', result);

        Alert.alert('Thành công!', 'QR Code đã được lưu vào thư viện ảnh', [
          {
            text: 'OK',
            onPress: () => {
              setShowQRModal(false);
            },
          },
        ]);
      }
    } catch (error) {
      console.error('Error downloading QR code:', error);

      // Nếu lỗi do không có quyền, hiển thị dialog yêu cầu quyền
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('quyền')) {
        CameraRollHelper.showPermissionDialog();
      } else {
        Alert.alert(
          'Lỗi',
          `Không thể lưu QR Code: ${errorMessage || 'Lỗi không xác định'}`,
          [{text: 'OK'}],
        );
      }
    }
  };

  const url = `${ConfigAPI.urlDeeplink}chainivo://share/${customer?.RefCode}?ref=qrcode`;

  return (
    <ScrollView style={styles.container}>
      {loading && (
        <FLoading
          visible={loading}
          avt={require('../../assets/appstore.png')}
        />
      )}
      <InforHeader title={'Ví QR của tôi'} showBack={false} />
      {/* QR Code Section */}
      <View style={styles.qrSection}>
        <View style={styles.qrContainer}>
          <ImageBackground
            source={require('../../assets/bg-qr.png')}
            style={styles.qrBackground}>
            <View style={styles.qrCodeWrapper}>
              {customer?.RefCode ? (
                <QRCode
                  value={url ?? ''}
                  size={110}
                  backgroundColor="white"
                  color="black"
                />
              ) : null}
            </View>
          </ImageBackground>
        </View>
      </View>
      <View style={styles.linkSection}>
        <Text style={styles.linkTitle}>Link mời bạn mới</Text>
        <View style={styles.linkContainer}>
          <Text style={styles.linkText}>{url ?? ''}</Text>
          <TouchableOpacity style={styles.copyButton} onPress={copyToClipboard}>
            <Winicon
              src="fill/layout/copy-2"
              size={20}
              color={ColorThemes.light.primary_main_color}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Action Buttons */}
      {customer?.RefCode ? (
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity
            style={styles.exportButton}
            onPress={() => shareQRCode(customer?.RefCode ?? '')}>
            <Text style={styles.exportButtonText}>Chia sẻ tới bạn bè</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.exportButton}
            onPress={() => setShowQRModal(true)}>
            <Text style={styles.exportButtonText}>Tải xuống mã QR</Text>
          </TouchableOpacity>
        </View>
      ) : null}

      {/* QR Export Modal */}
      <Modal
        visible={showQRModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowQRModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.downloadButton}
              onPress={downloadQRCode}>
              <Winicon
                src="fill/user interface/download"
                size={20}
                color="#333"
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowQRModal(false)}>
              <Winicon src="fill/layout/circle-xmark" size={20} color="#333" />
            </TouchableOpacity>
          </View>

          {/* ViewShot bao bọn toàn bộ nội dung bao gồm cả ImageBackground */}
          <ViewShot
            ref={viewShotRef}
            options={{format: 'png', quality: 0.9}}
            style={styles.viewShotContainer}>
            <ImageBackground
              source={require('../../assets/scan-me.png')}
              style={styles.modalContainer}>
              <View style={styles.qrModalContent}>
                {/* Logo */}
                <View style={styles.logoContainer}>
                  <View style={styles.logoCircle}>
                    <Image
                      source={require('../../assets/splash.png')}
                      style={{width: 270, height: 163}}
                    />
                  </View>
                </View>

                {/* QR Code */}
                <View style={styles.modalQRContainer}>
                  <ImageBackground
                    source={require('../../assets/bg-qr.png')}
                    style={styles.modalQRBackground}>
                    <View style={styles.qrCodeWrapper}>
                      {customer?.RefCode ? (
                        <QRCode
                          value={url ?? ''}
                          size={140}
                          backgroundColor="white"
                          color="black"
                        />
                      ) : null}
                    </View>
                  </ImageBackground>
                </View>

                {/* User Info */}
                <View style={styles.userInfoContainer}>
                  {customer?.AvatarUrl?.includes('https') ? (
                    <FastImage
                      source={{uri: customer.AvatarUrl}}
                      style={styles.userAvatar}
                    />
                  ) : customer?.AvatarUrl ? (
                    <FastImage
                      source={{uri: ConfigAPI.getValidLink(customer.AvatarUrl)}}
                      style={styles.userAvatar}
                    />
                  ) : (
                    <View
                      style={{
                        ...styles.userAvatar,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: ColorThemes.light.primary_main_color,
                      }}>
                      <Text
                        style={{
                          fontSize: 24,
                          color: '#fff',
                          fontWeight: 'bold',
                        }}>
                        {customer?.Name
                          ? customer?.Name.charAt(0).toUpperCase()
                          : ''}
                      </Text>
                    </View>
                  )}
                  <Text style={styles.userNameText}>
                    {customer?.Name || 'Người dùng'}
                  </Text>
                </View>

                {/* Description */}
                <View style={styles.descriptionContainer}>
                  <Text style={styles.descriptionText}>
                    Hãy tham gia cùng tôi và tận hưởng{'\n'}
                    đặc quyền thành viên của Chainivo
                  </Text>
                </View>
              </View>
            </ImageBackground>
          </ViewShot>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  qrSection: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  qrContainer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    overflow: 'hidden',
  },
  qrBackground: {
    width: 200,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrCodeWrapper: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 36,
  },
  linkSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  linkTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
  },
  linkText: {
    flex: 1,
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
  },
  copyButton: {
    padding: 5,
  },
  actionButtonsContainer: {
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 30,
  },
  exportButton: {
    backgroundColor: '#90C8FB',
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: 'center',
  },
  exportButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#343434',
  },
  transferButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingVertical: 15,
    borderRadius: 25,
    alignItems: 'center',
  },
  transferButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  historySection: {
    paddingHorizontal: 20,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginBottom: 15,
    width: '100%',
  },
  headerColumn: {
    flex: 1,
  },
  headerPointsColumn: {
    width: 50,
    alignItems: 'center',
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  userList: {
    gap: 0,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingVertical: 12,
    marginBottom: 10,
  },
  userColumn: {
    flex: 1,
  },
  pointsColumn: {
    width: 50,
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  badgeText: {
    fontSize: 12,
    color: '#FF4444',
    fontWeight: '500',
  },
  timeLabel: {
    fontSize: 12,
    color: '#666',
  },
  phoneNumber: {
    fontSize: 12,
    color: '#999',
  },
  points: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4169E1',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewShotContainer: {
    width: width * 0.95,
    height: 650,
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: 'transparent',
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    top: 60,
    left: 0,
    right: 10,
    zIndex: 10,
    height: 50,
  },
  downloadButton: {
    padding: 6,
    borderRadius: 20,
    width: 32,
    height: 32,
    backgroundColor: '#F5F5F5',
    position: 'absolute',
    top: 10,
    right: 50,
    zIndex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    padding: 6,
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrModalContent: {
    alignItems: 'center',
    position: 'relative',
  },
  logoContainer: {
    alignItems: 'center',
    zIndex: 1,
  },
  logoCircle: {
    justifyContent: 'center',
    alignItems: 'center',
    // marginBottom: 20,
    borderRadius: 10,
    overflow: 'hidden',
  },
  modalQRContainer: {
    alignItems: 'center',
    // marginBottom: 30,
    zIndex: 1,
  },
  modalQRBackground: {
    width: 250,
    height: 250,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalQRWrapper: {
    backgroundColor: 'white',
    // padding: 20,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  userInfoContainer: {
    alignItems: 'center',
    zIndex: 1,
    flexDirection: 'row',
    gap: 10,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 25,
    marginBottom: 10,
  },
  userNameText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  descriptionContainer: {
    alignItems: 'center',
    zIndex: 1,
    marginBottom: 20,
  },
  descriptionText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default MyWallet;
