import React from 'react';
import {View, StyleSheet} from 'react-native';
import StepIndicator from '../../../../../components/StepIndicator';

interface TransferStepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

const TransferStepIndicator: React.FC<TransferStepIndicatorProps> = ({
  currentStep,
  totalSteps,
}) => {
  return (
    <View style={styles.container}>
      <StepIndicator currentStep={currentStep} totalSteps={totalSteps} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});

export default TransferStepIndicator;
