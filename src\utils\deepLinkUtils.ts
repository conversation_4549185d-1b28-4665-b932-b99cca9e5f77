/**
 * @deprecated Use the new DeepLink service instead
 * This file is kept for backward compatibility
 */

import {RootScreen} from '../router/router';
import {DeepLinkUrl} from '../Screen/Layout/navigation/ecomNavigator';
import {
  parseDeepLink as newParseDeepLink,
  validateDeepLink,
  createDeepLink,
  createUniversalLink,
} from '../services/deeplink';
import type {ParsedDeepLink as NewParsedDeepLink} from '../services/deeplink';

// Legacy interface for backward compatibility
export interface ParsedDeepLink {
  screen: string;
  params: Record<string, string>;
  queryParams: string;
}

/**
 * @deprecated Use parseDeepLink from services/deeplink instead
 */
export const parseDeepLink = (url: DeepLinkUrl): ParsedDeepLink | null => {
  console.warn('[DEPRECATED] Use parseDeepLink from services/deeplink instead');

  const result = newParseDeepLink(url);
  if (!result.success || !result.data) {
    return null;
  }

  const data = result.data;

  // Convert new format to legacy format
  return {
    screen: mapScreenToLegacyFormat(data.screen),
    params: data.params as Record<string, string>,
    queryParams:
      Object.keys(data.queryParams).length > 0
        ? new URLSearchParams(data.queryParams).toString()
        : '',
  };
};

/**
 * @deprecated Use validateDeepLink from services/deeplink instead
 */
export const isValidDeepLink = (url: DeepLinkUrl): boolean => {
  console.warn(
    '[DEPRECATED] Use validateDeepLink from services/deeplink instead',
  );
  const result = validateDeepLink(url);
  return result.isValid;
};

/**
 * Map new screen format to legacy format
 */
const mapScreenToLegacyFormat = (screen: RootScreen): string => {
  switch (screen) {
    case RootScreen.PostDetail:
      return 'Posts';
    case RootScreen.DetailNews:
      return 'News';
    case RootScreen.ProductDetail:
      return 'Product';
    case RootScreen.DetailEvent:
      return 'Events';
    case RootScreen.login:
      return 'Share';
    default:
      return RootScreen.navigateEComView;
  }
};

const capitalizeFirst = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// Advanced parsing functions - kept for backward compatibility
export const extractProductInfo = (parsed: ParsedDeepLink) => {
  if (parsed.screen !== 'Product') return null;

  return {
    productId: parsed.params.id,
  };
};

export const extractPostInfo = (parsed: ParsedDeepLink) => {
  if (parsed.screen !== 'Posts') return null;

  return {
    postId: parsed.params.id,
  };
};

export const extractNewsInfo = (parsed: ParsedDeepLink) => {
  if (parsed.screen !== 'News') return null;

  return {
    newsId: parsed.params.id,
  };
};

export const extractEventsInfo = (parsed: ParsedDeepLink) => {
  if (parsed.screen !== 'Events') return null;

  return {
    eventId: parsed.params.id,
  };
};

export const extractLoginInfo = (parsed: ParsedDeepLink) => {
  if (parsed.screen !== 'Share') return null;

  return {
    qrId: parsed.params.id,
  };
};

// Usage Examples:

/*
1. Chia sẻ bài viết:
   URL: "chainivo://posts/123"
   Result: {
     screen: "Posts",
     params: { id: "123" },
     queryParams: {}
   }

2. Chia sẻ tin tức:
   URL: "chainivo://news/456"
   Result: {
     screen: "News",
     params: { id: "456" },
     queryParams: {}
   }

3. Chia sẻ sản phẩm:
   URL: "chainivo://product/789?ref=share&campaign=social"
   Result: {
     screen: "Product",
     params: { id: "789" },
     queryParams: { ref: "share", campaign: "social" }
   }

4. Chia sẻ QR code:
   URL: "chainivo://login/qr123?ref=qrcode&utm_source=app"
   Result: {
     screen: "Login",
     params: { id: "qr123" },
     queryParams: { ref: "qrcode", utm_source: "app" }
   }
*/
