import React, {useEffect} from 'react';
import {StatusBar} from 'react-native';

import {Provider} from 'react-redux';
import store from './src/redux/store/store';
import {NavigationContainer} from '@react-navigation/native';
import {navigationRef} from './src/router/router';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {LanguageProvider} from './src/locales/languageContext';
import {
  initNotificationPermission,
  registerListenerWithFCM,
  clearBadgeCount,
} from './src/features/notifications/fcm/fcm_helper';
import {PaperProvider} from 'react-native-paper';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {FSnackbar} from 'wini-mobile-components';
import {initBiometrics} from './src/features/local-authen/local-authen';
import DrawerCategories from './src/modules/category/DrawerCategories';
import {CartActions} from './src/redux/reducers/CartReducer';
import MessageNotificationProvider from './src/components/chat/MessageNotificationProvider';
import NotificationBadgeListener from './src/components/NotificationBadgeListener';
import DeepLinkProvider from './src/components/DeepLinkProvider';
import {TagProvider} from './src/modules/community/context/TagContext';
import LinkingConfiguration from './src/Screen/Layout/navigation/LinkingConfiguration';
import {EComStackNavigator} from './src/Screen/Layout/navigation/ecomNavigator';

function App(): React.JSX.Element {
  useEffect(() => {
    clearBadgeCount().then(() => console.log('Badge count removed'));
  }, []);

  /** setup firebase cloud message */
  useEffect(() => {
    initNotificationPermission();
    initBiometrics();
  }, []);

  useEffect(() => {
    const unsubscribe = registerListenerWithFCM();
    return unsubscribe;
  }, []);

  // Khởi tạo giỏ hàng với dữ liệu mẫu và tải giỏ hàng từ AsyncStorage khi ứng dụng khởi động
  useEffect(() => {
    const initCart = async () => {
      // Khởi tạo giỏ hàng với dữ liệu mẫu nếu chưa có
      // await initializeCart();
      // await clearCartStorage();
      // Tải giỏ hàng từ AsyncStorage
      store.dispatch(CartActions.loadCartFromStorage());
    };

    initCart();
  }, []);

  return (
    <Provider store={store} stabilityCheck="always">
      <LanguageProvider>
        <TagProvider>
          <PaperProvider>
            <GestureHandlerRootView>
              <StatusBar barStyle={'dark-content'} backgroundColor={'white'} />
              <SafeAreaProvider>
                <NotificationBadgeListener>
                  <MessageNotificationProvider>
                    <NavigationContainer
                      ref={navigationRef}
                      linking={LinkingConfiguration}>
                      <DeepLinkProvider navigationRef={navigationRef}>
                        <EComStackNavigator />
                        <FSnackbar />
                        <DrawerCategories />
                      </DeepLinkProvider>
                    </NavigationContainer>
                  </MessageNotificationProvider>
                </NotificationBadgeListener>
              </SafeAreaProvider>
            </GestureHandlerRootView>
          </PaperProvider>
        </TagProvider>
      </LanguageProvider>
    </Provider>
  );
}
export default App;
