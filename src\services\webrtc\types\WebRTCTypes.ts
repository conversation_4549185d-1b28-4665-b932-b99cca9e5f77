/**
 * WebRTC Types and Interfaces
 * Core types for WebRTC call functionality
 */

import { RTCPeerConnection, RTCSessionDescription, RTCIceCandidate, MediaStream } from 'react-native-webrtc';

// Call Status Enum
export enum CallStatus {
  IDLE = 'idle',
  CALLING = 'calling',
  RINGING = 'ringing', 
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ENDED = 'ended',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

// Call Direction
export enum CallDirection {
  OUTGOING = 'outgoing',
  INCOMING = 'incoming'
}

// User Info Interface
export interface UserInfo {
  id: string;
  name: string;
  avatar?: string;
}

// Call Data Interface
export interface CallData {
  callHistoryId: string;
  caller: UserInfo;
  receiver: UserInfo;
  direction: CallDirection;
  status: CallStatus;
  startTime?: number;
  acceptTime?: number;
  endTime?: number;
  duration?: number; // in milliseconds
}

// WebRTC Connection State
export interface WebRTCConnectionState {
  peerConnection: RTCPeerConnection | null;
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;
  isConnected: boolean;
  connectionState: string;
  iceConnectionState: string;
}

// Socket Events Data Interfaces
export interface CallUserData {
  targetUserId: string;
  from: string;
  fromName: string;
  fromAvatar?: string;
  callHistoryId: string;
}

export interface AcceptCallData {
  targetUserId: string; // caller ID
  from: string;         // receiver ID
  callHistoryId: string;
}

export interface RejectCallData {
  from: string;         // caller ID
  callHistoryId: string;
  rejectedBy?: string;  // receiver ID
}

export interface EndCallData {
  targetUserId: string;
  from: string;
  callHistoryId: string;
}

export interface OfferData {
  to: string;
  from: string;
  offer: RTCSessionDescription;
  callHistoryId: string;
}

export interface AnswerData {
  to: string;
  from: string;
  answer: RTCSessionDescription;
  callHistoryId: string;
}

export interface CandidateData {
  targetUserId: string;
  from: string;
  candidate: RTCIceCandidate;
  callHistoryId: string;
}

// Incoming Call Event Data
export interface IncomingCallData {
  from: string;
  fromName: string;
  fromAvatar?: string;
  callHistoryId: string;
  socketId: string;
}

// Call Event Callbacks
export interface CallEventCallbacks {
  onCallStatusChanged?: (status: CallStatus, callData: CallData) => void;
  onRemoteStreamReceived?: (stream: MediaStream) => void;
  onCallEnded?: (reason: string, callData: CallData) => void;
  onCallFailed?: (error: string, callData: CallData) => void;
  onConnectionStateChanged?: (state: string) => void;
}

// WebRTC Configuration
export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  enableAudio: boolean;
  enableVideo: boolean;
  audioConstraints?: MediaTrackConstraints;
  videoConstraints?: MediaTrackConstraints;
}

// Default WebRTC Configuration
export const DEFAULT_WEBRTC_CONFIG: WebRTCConfig = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
  ],
  enableAudio: true,
  enableVideo: false, // Audio only for now
  audioConstraints: {
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
  },
};

// Call History Database Interface
export interface CallHistoryRecord {
  Id: string;
  DateCreated: number;
  Name: string;        // Caller/Receiver name
  Receiver: string;    // Receiver ID
  IsAccept: boolean;   // Was call accepted
  Time: number;        // Call duration in seconds
  CallType?: 'incoming' | 'outgoing' | 'missed';
}

// Error Types
export enum WebRTCError {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  MEDIA_ERROR = 'MEDIA_ERROR',
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  SIGNALING_ERROR = 'SIGNALING_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  USER_OFFLINE = 'USER_OFFLINE',
  CALL_REJECTED = 'CALL_REJECTED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface WebRTCErrorInfo {
  type: WebRTCError;
  message: string;
  originalError?: any;
}

// Call Timer Interface
export interface CallTimer {
  startTime: number;
  duration: number; // in seconds
  isRunning: boolean;
}

// Audio Device Types
export enum AudioDevice {
  SPEAKER = 'speaker',
  EARPIECE = 'earpiece',
  BLUETOOTH = 'bluetooth',
  WIRED_HEADSET = 'wired_headset'
}

// Call Controls State
export interface CallControlsState {
  isMuted: boolean;
  isSpeakerOn: boolean;
  currentAudioDevice: AudioDevice;
  availableAudioDevices: AudioDevice[];
}

export default {
  CallStatus,
  CallDirection,
  WebRTCError,
  AudioDevice,
  DEFAULT_WEBRTC_CONFIG
};
