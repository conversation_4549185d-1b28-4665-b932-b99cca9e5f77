import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Image,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {useFocusEffect} from '@react-navigation/native';
import {ColorThemes} from '../../../assets/skin/colors';
import {
  useChatRooms,
  useChatLoading,
  useChatConnectionStatus,
} from '../../../redux/hook/chatHook';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {fetchChatRoomsForUser} from '../../../redux/reducers/ChatReducer';
import {ChatRoom} from '../types/ChatTypes';
import {navigate, RootScreen} from '../../../router/router';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import FastImage from '@d11/react-native-fast-image';
import {Ultis} from '../../../utils/Utils';
import ConfigAPI from '../../../Config/ConfigAPI';
import SocketService from '../services/SocketService';

interface ChatListScreenProps {
  searchQuery?: string;
}

const ChatListScreen: React.FC<ChatListScreenProps> = ({searchQuery = ''}) => {
  const dispatch = useDispatch<any>();
  const chatRooms = useChatRooms();
  const loading = useChatLoading();
  const isConnected = useChatConnectionStatus();
  const customer = useSelectorCustomerState().data;
  const [refreshing, setRefreshing] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);
  const [filteredChatRooms, setFilteredChatRooms] = useState<ChatRoom[]>([]);

  // Filter chat rooms based on search query
  useEffect(() => {
    if (!searchQuery || searchQuery.trim() === '') {
      setFilteredChatRooms(chatRooms);
    } else {
      const filtered = chatRooms.filter(
        room =>
          room.Name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          room.LastMessage?.toLowerCase().includes(searchQuery.toLowerCase()),
      );
      setFilteredChatRooms(filtered);
    }
  }, [searchQuery, chatRooms]);

  // Setup socket listeners for online users
  useEffect(() => {
    if (isConnected) {
      console.log('🔄 [ChatListScreen] Setting up online users listeners...');

      // Lắng nghe danh sách người dùng online
      SocketService.onOnlineUsers((users: any[]) => {
        console.log('📋 [ChatListScreen] Received online users:', users);
        const userIds = users.map(
          user => user.CustomerId || user.customerId || user.id || user,
        );
        setOnlineUsers(userIds);
      });

      // Lắng nghe sự kiện user online
      SocketService.onUserOnline((customerId: string) => {
        console.log('🟢 [ChatListScreen] User came online:', customerId);
        setOnlineUsers(prev => {
          if (!prev.includes(customerId)) {
            return [...prev, customerId];
          }
          return prev;
        });
      });

      // Lắng nghe sự kiện user offline
      SocketService.onUserOffline((customerId: string) => {
        console.log('🔴 [ChatListScreen] User went offline:', customerId);
        setOnlineUsers(prev => prev.filter(id => id !== customerId));
      });
    }

    return () => {
      // Cleanup listeners khi component unmount hoặc connection thay đổi
      console.log('🧹 [ChatListScreen] Cleaning up online users listeners...');
    };
  }, [isConnected]);

  useFocusEffect(
    useCallback(() => {
      if (customer) {
        // Chỉ tải dữ liệu chat rooms nếu chưa có data
        // Socket đã được khởi tạo ở app level
        if (chatRooms.length === 0) {
          console.log(
            '🔄 [ChatListScreen] Screen focused, loading chat rooms...',
          );
          loadChatRooms();
        } else {
          console.log(
            '✅ [ChatListScreen] Screen focused, already has chat rooms data',
          );
        }
      }
    }, [customer, chatRooms.length]),
  );

  const loadChatRooms = async () => {
    try {
      // Tránh multiple loading calls
      if (isInitializing) {
        console.log(
          '🔄 [ChatListScreen] Chat rooms loading already in progress...',
        );
        return;
      }

      setIsInitializing(true);
      console.log('🔄 [ChatListScreen] Loading chat rooms...');

      // Tải danh sách chat rooms cho user hiện tại
      // Socket đã được khởi tạo ở app level, không cần khởi tạo lại
      const userId = customer.Id;
      if (userId) {
        console.log('📋 [ChatListScreen] Fetching chat rooms...');
        dispatch(fetchChatRoomsForUser(userId, 1));
      }
    } catch (error) {
      console.error('❌ [ChatListScreen] Error loading chat rooms:', error);
    } finally {
      setIsInitializing(false);
    }
  };
  const getRandomAvatarColor = (name: string) => {
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
      '#F7DC6F',
      '#BB8FCE',
      '#85C1E9',
    ];

    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // Kiểm tra xem có user nào trong room đang online không (trừ user hiện tại)
  const isRoomOnline = (room: ChatRoom) => {
    if (!room.Members || !customer?.Id) return false;

    const memberIds = room.Members.split(',').map(id => id.trim());
    const otherMembers = memberIds.filter(id => id !== customer.Id);

    return otherMembers.some(memberId => onlineUsers.includes(memberId));
  };
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      const userId = customer?.Id || customer?.id;
      if (userId) {
        await dispatch(fetchChatRoomsForUser(userId, 1));
      }
    } catch (error) {
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể tải danh sách chat',
      });
    } finally {
      setRefreshing(false);
    }
  }, [dispatch, customer]);

  const navigateToChatRoom = (room: ChatRoom) => {
    navigate(RootScreen.ChatRoom, {room});
  };

  const navigateToCreateGroup = () => {
    navigate(RootScreen.CreateGroup);
  };
  const renderChatItem = ({item}: {item: ChatRoom}) => (
    <TouchableOpacity
      style={{
        ...styles.chatItem,
        ...(item?.unreadCount > 0
          ? {backgroundColor: ColorThemes.light.primary_background}
          : {}),
      }}
      onPress={() => navigateToChatRoom(item)}
      activeOpacity={0.7}>
      <View style={styles.avatarContainer}>
        {item.Avatar ? (
          <FastImage
            key={item.Avatar}
            source={{uri: ConfigAPI.getValidLink(item.Avatar)}}
            style={styles.avatar}
          />
        ) : (
          <View
            style={[
              styles.avatar,
              styles.defaultAvatar,
              {backgroundColor: getRandomAvatarColor(item.Name || 'User')},
            ]}>
            <Text style={styles.avatarText}>
              {item.Name?.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}

        {item.IsGroup && (
          <View style={styles.groupIndicator}>
            <Text style={styles.groupIndicatorText}>G</Text>
          </View>
        )}

        {/* Online decorator - chỉ hiển thị cho chat 1-1 và khi có user online */}
        {!item.IsGroup && isRoomOnline(item) && (
          <View style={styles.onlineDecorator}></View>
        )}
      </View>

      <View style={styles.chatContent}>
        <View style={styles.chatHeader}>
          <View
            style={{
              flexDirection: 'row',
              flex: 1,
              alignItems: 'center',
              gap: 4,
            }}>
            <Text style={styles.chatName} numberOfLines={1}>
              {item.Name}
            </Text>
          </View>

          {item.UpdatedAt && (
            <Text style={styles.timeText}>
              {
                // kiểm tra item.UpdatedAt == date hiện tại thì hiển thị hôm nay
                new Date(item.UpdatedAt).toDateString() ===
                new Date().toDateString()
                  ? // Hiển thị giờ:phút
                    new Date(item.UpdatedAt).toLocaleTimeString('vi-VN', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : Ultis.formatDateTime(item.UpdatedAt, true)
              }
            </Text>
          )}
        </View>
        <View style={styles.chatFooter}>
          <Text
            style={{
              ...styles.lastMessage,
              fontWeight: item.unreadCount > 0 ? 'bold' : 'normal',
            }}
            numberOfLines={1}>
            {item.LastMessage || ''}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>Chưa có cuộc trò chuyện nào</Text>
      <TouchableOpacity
        style={styles.createButton}
        onPress={navigateToCreateGroup}>
        <Text style={styles.createButtonText}>Tạo nhóm chat</Text>
      </TouchableOpacity>
    </View>
  );
  return (
    <View style={styles.container}>
      <FlatList
        data={filteredChatRooms}
        renderItem={renderChatItem}
        keyExtractor={item => item.Id || item.id || ''}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={
          filteredChatRooms.length === 0 ? styles.emptyList : undefined
        }
        ListFooterComponent={() => {
          return <View style={{height: 100}}></View>;
        }}
      />
      {!isConnected && (
        <View style={styles.connectionStatus}>
          <Text style={styles.connectionText}>Đang kết nối...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },

  chatItem: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 0.5,
    borderBottomColor: '#E8E8E8',
  },
  avatarContainer: {
    position: 'relative',
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#E8E8E8',
    // marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.primary_color,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 25,
  },
  avatarText: {
    color: 'red',
    fontSize: 18,
    fontWeight: 'bold',
  },
  groupIndicator: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.secondary_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  groupIndicatorText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_color,
  },
  timeText: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_secondary_color,
  },
  chatFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_secondary_color,
    flex: 1,
  },
  unreadDecorator: {
    backgroundColor: ColorThemes.light.primary_main_color,
    opacity: 0.6,
    borderRadius: 7,
    width: 8,
    height: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyText: {
    fontSize: 16,
    color: ColorThemes.light.neutral_text_secondary_color,
    textAlign: 'center',
    marginBottom: 16,
  },
  createButton: {
    backgroundColor: ColorThemes.light.primary_color,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  connectionStatus: {
    position: 'absolute',
    top: 60,
    left: 16,
    right: 16,
    backgroundColor: ColorThemes.light.warning_color,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  connectionText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
  decorator: {
    position: 'absolute',
    right: -5,
    bottom: 0,
    backgroundColor: 'rgba(55, 165, 99, 0.86)',
    borderRadius: 7,
    width: 15,
    height: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
  onlineDecorator: {
    position: 'absolute',
    right: -5,
    bottom: 0,
    backgroundColor: '#4CAF50', // Màu xanh lá cho online
    borderRadius: 7,
    width: 15,
    height: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: 'white',
  },
});

export default ChatListScreen;
