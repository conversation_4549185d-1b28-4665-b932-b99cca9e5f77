# Hệ thống Đa ngôn ngữ (Multi-language System)

Hệ thống đa ngôn ngữ cho ứng dụng React Native sử dụng `react-i18next` và `react-native-localize`.

## C<PERSON>u hình hiện tại

- **Ngôn ngữ mặc định**: Tiếng Việt (vi)
- **Ngôn ngữ được hỗ trợ**:
  - Tiếng Việt (vi)
  - Tiếng Anh (en)
  - Tiế<PERSON> (ja)

## Cấu trúc thư mục

```
src/locales/
├── i18n.tsx              # Cấu hình i18next
├── languageContext.tsx   # Context và Provider cho ngôn ngữ
├── useTranslation.ts     # Custom hooks và utilities
├── vi.json              # File ngôn ngữ tiếng Việt
├── en.json              # File ngôn ngữ tiếng Anh
├── ja.json              # File ngôn ngữ tiếng Nhật
└── README.md            # Tài liệu hướng dẫn
```

## Cách sử dụng

### 1. Sử dụng hook useTranslation

```tsx
import {useTranslation} from '../locales/useTranslation';

const MyComponent = () => {
  const {t, language, changeLanguage} = useTranslation();

  return (
    <View>
      <Text>{t('welcome')}</Text>
      <Text>{t('common.loading')}</Text>
      <Button title={t('auth.login')} onPress={() => {}} />
    </View>
  );
};
```

### 2. Thay đổi ngôn ngữ

```tsx
const {changeLanguage} = useTranslation();

// Chuyển sang tiếng Anh
await changeLanguage('en');

// Chuyển sang tiếng Việt
await changeLanguage('vi');

// Chuyển sang tiếng Nhật
await changeLanguage('ja');
```

### 3. Sử dụng LanguageSwitcher component

```tsx
import LanguageSwitcher from '../components/LanguageSwitcher';

const SettingsScreen = () => {
  return (
    <View>
      <LanguageSwitcher />
    </View>
  );
};
```

### 4. Sử dụng utilities

```tsx
import {LanguageUtils, SUPPORTED_LANGUAGES} from '../locales/useTranslation';

// Kiểm tra ngôn ngữ hiện tại
if (LanguageUtils.isVietnamese(language)) {
  // Logic cho tiếng Việt
}

// Lấy tên hiển thị của ngôn ngữ
const displayName = LanguageUtils.getLanguageDisplayName('vi'); // "Tiếng Việt"

// Lấy danh sách ngôn ngữ được hỗ trợ
const languages = LanguageUtils.getSupportedLanguages();
```

## Thêm ngôn ngữ mới

1. Tạo file JSON mới trong thư mục `src/locales/` (ví dụ: `ko.json` cho tiếng Hàn)
2. Thêm import trong `i18n.tsx`:
   ```tsx
   import ko from './ko.json';
   ```
3. Thêm vào resources:
   ```tsx
   resources: {
     en: { translation: en },
     vi: { translation: vi },
     ja: { translation: ja },
     ko: { translation: ko }  // Thêm dòng này
   }
   ```
4. Cập nhật `LanguageUtils.getSupportedLanguages()` và constants

## Cấu trúc file ngôn ngữ

File JSON được tổ chức theo nhóm chức năng:

```json
{
  "common": {
    "back": "Quay lại",
    "save": "Lưu",
    "cancel": "Hủy"
  },
  "auth": {
    "login": "Đăng nhập",
    "register": "Đăng ký"
  }
}
```

## Interpolation (Nội suy)

Sử dụng biến trong translation:

```json
{
  "course": {
    "duration": "Thời lượng: {{hours}} giờ",
    "totalLessons": "{{count}} bài giảng"
  }
}
```

```tsx
// Sử dụng
t('course.duration', {hours: 5}); // "Thời lượng: 5 giờ"
t('course.totalLessons', {count: 10}); // "10 bài giảng"
```

## Lưu ý quan trọng

- Ngôn ngữ được lưu trong AsyncStorage và tự động khôi phục khi khởi động app
- Ngôn ngữ mặc định là tiếng Việt nếu không có ngôn ngữ nào được lưu
- Hệ thống tự động phát hiện ngôn ngữ thiết bị nhưng ưu tiên ngôn ngữ đã lưu
- Tất cả translation keys nên được đặt tên rõ ràng và có cấu trúc
