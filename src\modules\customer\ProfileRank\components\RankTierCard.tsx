import React, {useState, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';
import FastImage from '@d11/react-native-fast-image';
import RenderHTML from 'react-native-render-html';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {
  closePopup,
  ComponentStatus,
  FLoading,
  showPopup,
  showSnackbar,
} from 'wini-mobile-components';
import {RankInfo} from '../../../../redux/types/rankTypes';
import BasePopupConfirm from '../../../../components/Popup/BasePopupConfirm';
import CustomerRankDa from '../../../customerRank/Da/customerRankDa';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';
import {CustomerActions} from 'redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';

const {width} = Dimensions.get('window');

interface RankTierCardProps {
  tier: RankInfo;
  popupRef: React.RefObject<any>;
  currentRankInfo?: RankInfo | null;
  isVip?: boolean;
}

// Helper function to extract benefits from Description
const extractBenefits = (description: string): string[] => {
  if (!description) return [];

  // Split by comma and filter out empty strings
  const benefits = description
    .split(',')
    .map(benefit => benefit.trim())
    .filter(benefit => benefit.length > 0);

  return benefits;
};

export const RankTierCard: React.FC<RankTierCardProps> = ({
  tier,
  popupRef,
  currentRankInfo,
  isVip,
}) => {
  const customer = useSelectorCustomerState().data;
  const dispatch = useDispatch();
  const [showConfirm, setShowConfirm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showBenefits, setShowBenefits] = useState(false);
  const customerRankDa = new CustomerRankDa();

  // Extract benefits for display
  const benefits = useMemo(
    () => extractBenefits((tier as any)?.Description || ''),
    [tier],
  );

  // Check if upgrade button should be hidden
  const shouldHideUpgradeButton = useMemo(() => {
    if (!currentRankInfo || !tier) return false;

    // Use Sort field for comparison, fallback to Score if Sort is not available
    const currentSort = currentRankInfo.Sort ?? currentRankInfo.Score;
    const tierSort = tier.Sort ?? tier.Score;

    return currentSort >= tierSort;
  }, [currentRankInfo, tier]);

  const handleConfirm = async () => {
    try {
      setLoading(true);
      setShowConfirm(false);
      closePopup(popupRef);
      if (!customer) return;
      await customerRankDa.buyRank(customer, tier);
      CustomerActions.getRankInfo(dispatch);
      showSnackbar({
        message: 'Nâng hạng thành công',
        status: ComponentStatus.SUCCSESS,
      });
    } catch (error: any) {
      const msg = error?.message || 'Có lỗi không xác định';
      closePopup(popupRef);
      showSnackbar({message: msg, status: ComponentStatus.ERROR});
    } finally {
      setShowConfirm(false);
      setLoading(false);
    }
  };
  const handleUpgrade = () => {
    setShowConfirm(true);
  };

  const handleViewDetails = () => {
    showPopup({
      ref: popupRef,
      enableDismiss: true,
      children: (
        <View style={styles.popupContainer}>
          <View style={styles.handleIndicator} />
          <ScrollView
            style={styles.popupContent}
            contentContainerStyle={{paddingBottom: 120}}>
            <Text style={styles.popupTitle}>Mô tả chi tiết</Text>
            <View style={styles.popupDescription}>
              <RenderHTML
                contentWidth={width}
                source={{html: tier?.Condition}}
              />
            </View>
          </ScrollView>
          <View style={styles.popupFooter}>
            <TouchableOpacity
              style={styles.buyButton}
              onPress={() => setShowConfirm(true)}>
              <Text style={styles.actionButtonText}>Mua hạng</Text>
            </TouchableOpacity>
          </View>
        </View>
      ),
    });
  };

  const toggleBenefits = () => {
    setShowBenefits(!showBenefits);
  };

  return (
    <>
      <FLoading visible={loading} />
      <View style={styles.rankTierCard}>
        {/* Top row - Rank info and Upgrade button */}
        <View style={styles.topRow}>
          {/* Left side - Rank info */}
          <View style={styles.leftSection}>
            <View style={styles.rankTierIcon}>
              <FastImage
                key={tier.Icon}
                source={{
                  uri: ConfigAPI.urlImg + tier.Icon,
                }}
                style={styles.iconImage}
              />
            </View>
            <View style={styles.rankInfo}>
              <Text style={styles.rankTierLabel}>Hạng {tier.Name}</Text>
              <Text
                style={[
                  styles.rankTierPoints,
                  {
                    color: isVip
                      ? '#FFC043'
                      : ColorThemes.light.infor_main_color,
                  },
                ]}>
                {Ultis.money(tier.Score)} Point
              </Text>
            </View>
          </View>

          {/* Right side - Upgrade button */}
          {!shouldHideUpgradeButton && (
            <TouchableOpacity
              style={[
                styles.upgradeButton,
                {
                  backgroundColor: isVip
                    ? '#FFC043'
                    : ColorThemes.light.primary_main_color,
                },
              ]}
              onPress={handleUpgrade}>
              <Text style={styles.upgradeButtonText}>Nâng cấp</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Bottom row - Benefits toggle button */}
        <View style={styles.bottomRow}>
          <TouchableOpacity
            style={styles.benefitsToggle}
            onPress={toggleBenefits}>
            <Text
              style={[
                styles.benefitsToggleText,
                {color: isVip ? '#FFC043' : ColorThemes.light.infor_main_color},
              ]}>
              {showBenefits ? 'Ẩn bớt' : 'Xem quyền lợi'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Benefits list - shown when expanded */}
        {showBenefits && (
          <View style={styles.benefitsList}>
            {benefits.length > 0 ? (
              benefits.map((benefit, index) => (
                <View key={index} style={styles.benefitItem}>
                  <View style={styles.checkIcon}>
                    <Text style={styles.checkMark}>✓</Text>
                  </View>
                  <Text style={styles.benefitText}>{benefit}</Text>
                </View>
              ))
            ) : (
              <TouchableOpacity onPress={handleViewDetails}>
                <Text style={styles.viewDetailsText}>
                  Xem chi tiết quyền lợi
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
      <BasePopupConfirm
        visible={showConfirm}
        title="Xác nhận mua hạng"
        message={`Bạn có chắc chắn muốn mua hạng ${tier.Name}?`}
        onCancel={() => setShowConfirm(false)}
        onConfirm={handleConfirm}
      />
    </>
  );
};

const styles = StyleSheet.create({
  rankTierCard: {
    width: '100%',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 12,
    backgroundColor: ColorThemes.light.white,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rankTierIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  iconImage: {
    width: 30,
    height: 30,
  },
  rankInfo: {
    flex: 1,
  },
  rankTierLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  rankTierPoints: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.infor_main_color,
    fontWeight: 'bold',
  },
  upgradeButton: {
    backgroundColor: ColorThemes.light.infor_main_color,
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
  },
  upgradeButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.white,
    fontWeight: '600',
  },
  bottomRow: {
    marginTop: 12,
    alignItems: 'flex-end',
  },
  benefitsToggle: {
    alignSelf: 'flex-end',
  },
  benefitsToggleText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.infor_main_color,
    fontWeight: '500',
    fontSize: 14,
  },
  benefitsList: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_main_border_color,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  checkIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.success_main_color,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  checkMark: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 10,
    fontWeight: 'bold',
  },
  benefitText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    flex: 1,
    fontSize: 12,
  },
  noBenefitsText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontStyle: 'italic',
    fontSize: 12,
  },
  viewDetailsText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.infor_main_color,
    fontWeight: '500',
    fontSize: 12,
  },
  popupContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    height: Dimensions.get('window').height * 0.6,
    backgroundColor: ColorThemes.light.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingTop: 10,
  },
  handleIndicator: {
    width: 40,
    height: 4,
    backgroundColor: ColorThemes.light.neutral_subtitle_color,
    alignSelf: 'center',
    borderRadius: 2,
    marginBottom: 8,
  },
  popupContent: {
    flex: 1,
  },
  popupTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
    textAlign: 'center',
  },
  popupDescription: {
    flex: 1,
    marginTop: 8,
  },
  popupFooter: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    padding: 16,
    paddingBottom: 20,
    backgroundColor: ColorThemes.light.white,
  },
  buyButton: {
    backgroundColor: ColorThemes.light.infor_main_color,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.white,
    fontWeight: '700',
  },
});
