import React from 'react';
import {View, Text, TextInput, StyleSheet} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';

interface CancelReasonInputProps {
  visible: boolean;
  value: string;
  onChangeText: (text: string) => void;
}

const CancelReasonInput: React.FC<CancelReasonInputProps> = ({
  visible,
  value,
  onChangeText,
}) => {
  if (!visible) return null;

  return (
    <View style={styles.cancelReasonContainer}>
      <Text style={styles.cancelReasonLabel}>Lý do hủy đơn hàng *</Text>
      <TextInput
        style={styles.cancelReasonInput}
        placeholder="Nhập lý do hủy đơn hàng..."
        value={value}
        onChangeText={onChangeText}
        multiline={true}
        numberOfLines={3}
        textAlignVertical="top"
        accessibilityLabel="Nhập lý do hủy đơn hàng"
        accessibilityHint="Trường bắt buộ<PERSON> khi hủy đơn hàng"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  cancelReasonContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cancelReasonLabel: {
    ...TypoSkin.title3,
    color: '#333333',
    fontWeight: '600',
    fontSize: 14,
    marginBottom: 8,
  },
  cancelReasonInput: {
    ...TypoSkin.title4,
    color: '#333333',
    padding: 12,
    borderWidth: 1,
    borderColor: '#ced4da',
    borderRadius: 8,
    backgroundColor: '#ffffff',
    minHeight: 80,
    textAlignVertical: 'top',
  },
});

export default CancelReasonInput;
