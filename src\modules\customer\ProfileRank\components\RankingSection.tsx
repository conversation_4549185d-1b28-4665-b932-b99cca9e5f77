import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, FlatList} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {RankTierCard} from './RankTierCard';
import {RankInfo} from '../../../../redux/types/rankTypes';

interface RankingSectionProps {
  ranksData: RankInfo[];
  popupRef: React.RefObject<any>;
  currentRankInfo?: RankInfo | null;
}

export const RankingSection: React.FC<RankingSectionProps> = ({
  ranksData,
  popupRef,
  currentRankInfo,
}) => {
  const [isVip, setIsVip] = useState<boolean>(false);

  useEffect(() => {
    if (!currentRankInfo || !currentRankInfo?.Sort) setIsVip(false);
    else setIsVip(currentRankInfo?.Sort >= 3);
  }, [currentRankInfo]);

  const renderRankItem = ({item}: {item: RankInfo}) => (
    <RankTierCard
      tier={item}
      isVip={isVip}
      popupRef={popupRef}
      currentRankInfo={currentRankInfo}
    />
  );

  return (
    <View
      style={[
        styles.rankingSection,
        {
          backgroundColor: isVip
            ? '#FFF8E1'
            : ColorThemes.light.primary_background,
        },
      ]}>
      <Text style={styles.sectionTitle}>
        Danh sách hạng ({ranksData?.length} cấp độ)
      </Text>
      <FlatList
        data={ranksData}
        renderItem={renderRankItem}
        keyExtractor={item => item.Id}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  rankingSection: {
    marginTop: 16,
    borderRadius: 16,
    padding: 12,
  },
  sectionTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 16,
  },
});
