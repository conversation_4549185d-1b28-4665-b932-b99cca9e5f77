import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {DataController} from '../../../base/baseController';
import {MissionType, StatusOrder, Title} from '../../../Config/Contanst';
import {OrderActions} from '../../../redux/reducers/OrderReducer';
import {RootScreen} from '../../../router/router';
import {Ultis} from '../../../utils/Utils';
import store from '../../../redux/store/store';
import {OrderDA} from '../orderDA';
import {ProductDA} from '../../Product/productDA';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import ChatAPI from '../../chat/services/ChatAPI';
import {addChatRoom} from '../../../redux/reducers/ChatReducer';
import {navigate} from '../../../router/router';
import {useDispatch} from 'react-redux';
import WalletDA from 'modules/wallet/walletDa';
import AffiliateDa from '../affiliateDa';
const orderDetailController = new DataController('OrderDetail');
const productController = new DataController('Product');
const shopController = new DataController('Order');

export const handleUpdateStatusOrder = async (
  item: any,
  type?: string,
  dispatch?: any,
  navigation?: any,
  shopInfo?: any,
) => {
  try {
    let order = item;
    const orderDA = new OrderDA();
    const productDA = new ProductDA();
    const affiliateDa = new AffiliateDa();
    if (type == 'processing') {
      // Lấy chi tiết đơn hàng
      const orderDetailResponse = await orderDA.getOrderDetail(order.Id);
      if (
        orderDetailResponse?.code === 200 &&
        orderDetailResponse?.data?.length > 0
      ) {
        const orderDetails = orderDetailResponse.data;

        for (const detail of orderDetails) {
          const productId = detail?.ProductId;
          const orderQuantity = detail?.Quantity || 1;

          if (productId) {
            const productResponse = await productDA.getProductById(productId);
            if (
              productResponse?.code === 200 &&
              productResponse?.data?.length > 0
            ) {
              const product = productResponse.data[0];
              const currentStock = product?.InStock || 0;

              if (currentStock <= 0) {
                showSnackbar({
                  message: `Sản phẩm "${product?.Name}" đã hết hàng. Không thể xử lý đơn hàng này.`,
                  status: ComponentStatus.ERROR,
                });
                // setLoading(false); // Stop loading here to allow user to retry or cancel
                return; // Stop processing this order
              }
              if (orderQuantity > currentStock) {
                showSnackbar({
                  message: `Sản phẩm "${product?.Name}" chỉ còn ${currentStock} sản phẩm trong kho. Đơn hàng yêu cầu ${orderQuantity} sản phẩm.`,
                  status: ComponentStatus.ERROR,
                });
                // setLoading(false); // Stop loading here to allow user to retry or cancel
                return; // Stop processing this order
              }
              // Cập nhật số lượng tồn kho của sản phẩm
              const newStock = currentStock - orderQuantity;
              const updatedProduct = {
                ...product,
                InStock: newStock,
                Status: newStock === 0 ? 2 : 1,
              };
              const productUpdateResponse = await productDA.updateProduct(
                updatedProduct,
              );
              if (productUpdateResponse?.code !== 200) {
                showSnackbar({
                  message:
                    'Có lỗi xảy ra khi cập nhật số lượng tồn kho sản phẩm',
                  status: ComponentStatus.ERROR,
                });
                // setLoading(false);
                return;
              }
            } else {
              return;
            }
          }
        }
        // Cập nhật trạng thái đơn hàng (chỉ khi tất cả sản phẩm được xử lý thành công)
        const res = await orderDA.updateOrder([
          {
            Id: order.Id,
            CustomerId: order.CustomerId,
            ShopId: order.ShopId,
            Code: order.Code,
            Status: StatusOrder.proccess,
            DateProcess: new Date().getTime(),
          },
        ]);

        if (res?.code == 200) {
          await affiliateDa.createPendingReward(
            order.ShopId,
            order.Id,
            order.CustomerId,
            StatusOrder.new,
            order.Code,
          );
          showSnackbar({
            message:
              'Cập nhật trạng thái đơn hàng sang trạng thái đang xử lý thành công',
            status: ComponentStatus.SUCCSESS,
          });
          if (dispatch && shopInfo && shopInfo.length > 0 && shopInfo[0]?.Id) {
            dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
          }
        }
        // Chuyển trang sau khi cập nhật thành công - luôn chuyển về trạng thái 3 (hoàn thành)
        navigate(RootScreen.OrderDetail, {
          type: Title.Processing,
          status: 2,
        });
        return res;
      } else {
        return;
      }
    }

    if (type == 'completed') {
      await orderDA.completeTransferRewardFund(order.Id);
      await affiliateDa.updateDoneTransactionReward(order.Id);
      // Cập nhật trạng thái đơn hàng
      const resCompleted = await orderDA.updateOrder([
        {
          ...order,
          orderDetails: undefined,
          Status: StatusOrder.success,
          DateCompleted: new Date().getTime(),
        },
      ]);
      if (resCompleted?.code == 200) {
        // #region update mission customer khi tổng đơn hàng trong tháng đạt yêu cầu
        //lấy ngày bắt đẩu và kết thúc của tháng
        const {start, end} = Ultis.getMonthRange();
        const [orderRes, missionCustomer] = await Promise.all([
          new DataController('Order').group({
            reducers:
              'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS Total',
            searchRaw: `@CustomerId: {${order.CustomerId}} @DateCreated: [${start} ${end}] @Status: [${StatusOrder.success}]`,
          }),
          new DataController('Mission').getListSimple({
            page: 1,
            size: 1,
            query: `@MissonType: [${MissionType.month}] @Type: [2]`, // nhiệm vụ tháng
          }),
        ]);
        if (orderRes?.code === 200 && missionCustomer.code === 200) {
          const total = orderRes.data.reduce(
            (total: number, item: any) => total + parseFloat(item.Total),
            0,
          );
          if (total >= missionCustomer.data[0]?.TotalPriceOrder) {
            const walletDA = new WalletDA();
            await walletDA.CaculateMisson(order.CustomerId, MissionType.month);
          }
        }
        // #endregion
        //#region cập nhật order detail khi đơn hàng hoàn thành
        const orderDetailResponse = await orderDetailController.getListSimple({
          page: 1,
          size: 1000,
          query: `@OrderId: {${order?.Id}}`,
        });

        if (
          orderDetailResponse?.code === 200 &&
          orderDetailResponse?.data?.length > 0
        ) {
          const orderDetails = orderDetailResponse.data;
          // Cập nhật trạng thái của các order detail
          const updatedOrderDetails = orderDetails.map((detail: any) => ({
            Id: detail.Id,
            Status: 3,
          }));

          const orderDetailUpdateResponse = await orderDA.updateOrderDetail(
            updatedOrderDetails,
          );

          if (orderDetailUpdateResponse?.code !== 200) {
            console.error(
              'Error updating order detail status:',
              orderDetailUpdateResponse,
            );
            showSnackbar({
              message:
                'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật trạng thái chi tiết đơn hàng',
              status: ComponentStatus.WARNING,
            });
          }

          // Cập nhật số lượng sản phẩm đã bán cho từng sản phẩm trong đơn hàng
          for (const detail of orderDetails) {
            const productId = detail?.ProductId;
            const orderQuantity = detail?.Quantity || 1;

            if (productId) {
              try {
                // Lấy thông tin sản phẩm hiện tại
                const productResponse = await productController.getListSimple({
                  query: `@Id:{${productId}}`,
                });

                if (
                  productResponse?.code === 200 &&
                  productResponse?.data?.length > 0
                ) {
                  const product = productResponse.data[0];
                  const currentSold = product?.Sold || 0;

                  // Cập nhật số lượng đã bán
                  const newSold = currentSold + orderQuantity;
                  const updatedProduct = {
                    ...product,
                    Sold: newSold,
                  };

                  const productUpdateResponse = await productController.edit([
                    updatedProduct,
                  ]);

                  if (productUpdateResponse?.code !== 200) {
                    console.error(
                      'Error updating product sold count:',
                      productUpdateResponse,
                    );
                    showSnackbar({
                      message:
                        'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật số lượng đã bán',
                      status: ComponentStatus.WARNING,
                    });
                  } else {
                    if (
                      dispatch &&
                      shopInfo &&
                      shopInfo.length > 0 &&
                      shopInfo[0]?.Id
                    ) {
                      dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
                    }

                    // Chuyển trang sau khi cập nhật thành công - luôn chuyển về trạng thái 3 (hoàn thành)
                    if (navigation) {
                      navigation.navigate(RootScreen.OrderDetail, {
                        type: Title.Done,
                        status: 3,
                      });
                    }
                  }
                }
              } catch (error) {
                console.error('Error updating product sold count:', error);
                showSnackbar({
                  message:
                    'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi cập nhật số lượng đã bán',
                  status: ComponentStatus.WARNING,
                });
              }
            }
          }
        }
        //#endregion

        showSnackbar({
          message: 'Cập nhật trạng thái đơn hàng sang trạng thái đã hoàn thành',
          status: ComponentStatus.SUCCSESS,
        });
      } else {
        showSnackbar({
          message: 'Cập nhật trạng thái đơn hàng thất bại',
          status: ComponentStatus.ERROR,
        });
      }
    }

    if (type == 'cancelled') {
      // Cập nhật trạng thái đơn hàng
      const resCancelled = await shopController.edit([
        {
          Id: order.Id,
          CustomerId: order.CustomerId,
          ShopId: order.ShopId,
          Code: order.Code,
          Status: StatusOrder.cancel,
          DateUpdate: new Date().getTime(),
          CancelReason: order.cancelReason || '',
        },
      ]);
      if (resCancelled?.code == 200) {
        await affiliateDa.updateRejectTransactionReward(order.Id);
        // Chỉ khôi phục số lượng tồn kho nếu đơn hàng trước đó có status = 2 (đang xử lý)
        // Nếu status = 1 (mới) thì không cần khôi phục vì chưa trừ tồn kho
        if (order.Status === 2) {
          console.log('Đơn hàng có status = 2, tiến hành khôi phục tồn kho...');
          const orderDetailResponse = await orderDA.getOrderDetail(order.Id);
          if (
            orderDetailResponse?.code === 200 &&
            orderDetailResponse?.data?.length > 0
          ) {
            const orderDetails = orderDetailResponse.data;

            for (const detail of orderDetails) {
              const productId = detail?.ProductId;
              const orderQuantity = detail?.Quantity || 1;

              if (productId) {
                try {
                  // Lấy thông tin sản phẩm hiện tại
                  const productResponse = await productDA.getProductById(
                    productId,
                  );
                  if (
                    productResponse?.code === 200 &&
                    productResponse?.data?.length > 0
                  ) {
                    const product = productResponse.data[0];
                    const currentStock = product?.InStock || 0;

                    // Khôi phục số lượng tồn kho bằng cách cộng thêm số lượng đã đặt hàng
                    const newStock = currentStock + orderQuantity;
                    const updatedProduct = {
                      ...product,
                      InStock: newStock,
                      Status: newStock === 0 ? 2 : 1,
                    };

                    const productUpdateResponse = await productDA.updateProduct(
                      [updatedProduct],
                    );

                    if (productUpdateResponse?.code !== 200) {
                      console.error(
                        'Error updating product stock when cancelling order:',
                        productUpdateResponse,
                      );
                      showSnackbar({
                        message:
                          'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi khôi phục số lượng tồn kho sản phẩm',
                        status: ComponentStatus.WARNING,
                      });
                    } else {
                      console.log(
                        `Đã khôi phục ${orderQuantity} sản phẩm cho sản phẩm ${product?.Name}`,
                      );
                    }
                  } else {
                    showSnackbar({
                      message: `Không tìm thấy thông tin sản phẩm với ID: ${productId} khi hủy đơn hàng.`,
                      status: ComponentStatus.WARNING,
                    });
                  }
                } catch (error) {
                  console.error(
                    'Error updating product stock when cancelling order:',
                    error,
                  );
                  showSnackbar({
                    message:
                      'Cập nhật trạng thái đơn hàng thành công nhưng có lỗi khi khôi phục số lượng tồn kho sản phẩm',
                    status: ComponentStatus.WARNING,
                  });
                }
              }
            }
          } else {
          }
        } else {
          console.log(
            `Đơn hàng có status = ${order.Status}, không cần khôi phục tồn kho`,
          );
        }

        showSnackbar({
          message: 'Cập nhật trạng thái đơn hàng sang trạng thái hủy',
          status: ComponentStatus.SUCCSESS,
        });
        if (dispatch && shopInfo && shopInfo.length > 0 && shopInfo[0]?.Id) {
          dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
        }

        // Chuyển trang sau khi cập nhật thành công - luôn chuyển về trạng thái 3 (hoàn thành)
        if (navigation) {
          navigation.navigate(RootScreen.OrderDetail, {
            type: Title.Cancel,
            status: 4,
          });
        }
        return resCancelled;
      }
    }
  } catch (error) {
    console.error('Error updating order status:', error);
    showSnackbar({
      message: 'Có lỗi xảy ra khi cập nhật trạng thái đơn hàng',
      status: ComponentStatus.ERROR,
    });
  } finally {
    // setLoading(false);
  }
};

export const handleChatPress = async (contact: any) => {
  const dispatch = useDispatch();
  const currentUser = useSelectorCustomerState().data;
  const orderDA = new OrderDA();
  if (!currentUser) return;
  try {
    if (!currentUser?.Id && !currentUser?.Id) {
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể xác định người dùng hiện tại',
      });
      return;
    }

    const currentUserId = currentUser.Id || currentUser.Id!;
    const targetUserId = contact.Id;

    // Tìm hoặc tạo ChatRoom
    const chatRoom = await ChatAPI.findOrCreatePrivateRoom(
      currentUserId,
      contact,
    );
    const listRoom = store.getState().chat.rooms;
    if (!listRoom.find((item: any) => item.id === chatRoom.id)) {
      dispatch(addChatRoom(chatRoom));
    }
    // Navigate đến ChatRoomScreen
    navigate(RootScreen.ChatRoom, {room: chatRoom});
  } catch (error) {
    console.error('Error creating chat:', error);
    showSnackbar({
      status: ComponentStatus.ERROR,
      message: 'Không thể tạo cuộc trò chuyện',
    });
  }
};

export const handleChatPressWithShop = async (shopId: string) => {
  const dispatch = useDispatch();
  const currentUser = useSelectorCustomerState().data;
  const orderDA = new OrderDA();
  try {
    if (!currentUser?.Id && !currentUser?.Id) {
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể xác định người dùng hiện tại',
      });
      return;
    }

    let getShopInfo = await orderDA.getShopInfo(shopId);
    if (!getShopInfo || getShopInfo?.code !== 200) {
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không tìm thấy thông tin cửa hàng',
      });
      return;
    }

    const currentUserId = currentUser.Id || currentUser.Id!;
    // Tìm hoặc tạo ChatRoom
    const chatRoom = await ChatAPI.findOrCreatePrivateRoom(
      currentUserId,
      getShopInfo[0],
    );
    const listRoom = store.getState().chat.rooms;
    if (!listRoom.find((item: any) => item.id === chatRoom.id)) {
      dispatch(addChatRoom(chatRoom));
    }
    // Navigate đến ChatRoomScreen
    navigate(RootScreen.ChatRoom, {room: chatRoom});
  } catch (error) {
    console.error('Error creating chat:', error);
    showSnackbar({
      status: ComponentStatus.ERROR,
      message: 'Không thể tạo cuộc trò chuyện',
    });
  }
};
