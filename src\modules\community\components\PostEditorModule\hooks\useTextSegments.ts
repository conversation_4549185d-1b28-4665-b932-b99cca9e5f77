import {useState, useCallback} from 'react';
import {TextSegment, MentionModalState, UserMention} from '../types';
import {
  htmlToSegments,
  mergeAdjacentSegments,
  getFullText,
} from '../utils/textUtils';
import {
  isVietnameseChar,
  canReplaceLatinChar,
} from '../utils/vietnameseUtils';
import {CustomerDA} from '../../../../customer/da';

import ConfigAPI from '../../../../../Config/ConfigAPI';

export const useTextSegments = (
  initialText: string = '',
  initialHtml: string = '',
) => {
  // Khởi tạo segments từ initialHtml nếu có, nếu không thì từ initialText
  const [segments, setSegments] = useState<TextSegment[]>(() => {
    if (initialHtml) {
      return htmlToSegments(initialHtml);
    }
    return [{text: initialText}];
  });

  // Loại bỏ format state - không cần thiết nữa

  const [selection, setSelection] = useState<{start: number; end: number}>({
    start: 0,
    end: 0,
  });

  // Thêm state để theo dõi trạng thái nhập liệu
  const [lastInputChar, setLastInputChar] = useState<string>('');
  const [isComposingVietnamese, setIsComposingVietnamese] =
    useState<boolean>(false);

  // State cho mention modal
  const [mentionModal, setMentionModal] = useState<MentionModalState>({
    visible: false,
    searchText: '',
    users: [],
    loading: false,
    position: {x: 0, y: 0},
  });

  // State để theo dõi vị trí @ hiện tại
  const [currentMentionStart, setCurrentMentionStart] = useState<number>(-1);

  // State để lưu thông tin editor layout
  const [editorInfo, setEditorInfo] = useState({y: 0});

  // Function để cập nhật thông tin editor
  const updateEditorInfo = useCallback((editorY: number) => {
    setEditorInfo({y: editorY});
  }, []);

  // Function để cập nhật vị trí modal dựa trên vị trí con trỏ
  const updateModalPosition = useCallback((editorY: number, cursorPosition: number, text: string) => {
    // Tính số dòng từ đầu text đến vị trí con trỏ
    const textBeforeCursor = text.substring(0, cursorPosition);
    const lineCount = (textBeforeCursor.match(/\n/g) || []).length;

    // Ước tính chiều cao mỗi dòng (có thể điều chỉnh theo font size)
    const lineHeight = 24; // Điều chỉnh theo style của TextInput

    // Tính vị trí Y của con trỏ
    const cursorY = editorY + (lineCount * lineHeight) + 170;
    setMentionModal(prev => ({
      ...prev,
      position: {
        x: 20, // margin từ trái
        y: cursorY, // vị trí ngay bên dưới con trỏ
      },
    }));
  }, []);

  // Kiểm tra mention (@) và hiển thị modal
  const checkForMention = useCallback(async (text: string, cursorPosition: number) => {
    try {
      // Kiểm tra xem có @ + ít nhất 1 ký tự trước cursor không
      let hasValidMention = false;
      let atPosition = -1;
      let searchText = '';

      // Tìm @ gần nhất trước cursor
      for (let i = cursorPosition - 1; i >= 0; i--) {
        if (text[i] === '@') {
          // Kiểm tra xem @ này có phải là bắt đầu của một từ không
          if (i === 0 || text[i - 1] === ' ' || text[i - 1] === '\n') {
            // Lấy text sau @ đến cursor
            const textAfterAt = text.substring(i + 1, cursorPosition);

            // Kiểm tra xem có ít nhất 1 ký tự sau @ không (không có space/newline)
            if (textAfterAt.length >= 1 &&
                /^[a-zA-Z\u00C0-\u024F\u1E00-\u1EFF]+$/.test(textAfterAt) &&
                !textAfterAt.includes(' ') && !textAfterAt.includes('\n')) {
              hasValidMention = true;
              atPosition = i;
              searchText = textAfterAt; // Text sau @
              break;
            }
          }
        }
        // Nếu gặp space hoặc newline, dừng tìm kiếm
        if (text[i] === ' ' || text[i] === '\n') {
          break;
        }
      }

      if (hasValidMention && atPosition !== -1) {
        // Có @ + ít nhất 1 ký tự được phát hiện, hiển thị modal

        // Luôn cập nhật modal khi có mention hợp lệ
        setCurrentMentionStart(atPosition);

        // Cập nhật vị trí modal dựa trên vị trí con trỏ hiện tại
        if (editorInfo.y > 0) {
          updateModalPosition(editorInfo.y, cursorPosition, text);
        }

        // Chỉ gọi API nếu searchText thay đổi
        if (mentionModal.searchText !== searchText) {
          setMentionModal(prev => ({
            ...prev,
            visible: true,
            searchText: searchText,
            loading: true,
          }));

          // Gọi API getAllCustomer với text đã nhập
          try {
            const customerDA = new CustomerDA();
            const result = await customerDA.getAllCustomer(1, 20, searchText);
            if (result && result.code === 200) {
              const mappedUsers: UserMention[] = result.data.map((user: any) => ({
                Id: user.Id,
                Name: user.Name,
                Email: user.Email,
                AvatarUrl: ConfigAPI.getValidLink(user.AvatarUrl),
              }));
              setMentionModal(prev => ({
                ...prev,
                loading: false,
                users: mappedUsers,
                searchText: searchText,
              }));
            } else {
              setMentionModal(prev => ({
                ...prev,
                loading: false,
                users: [],
                searchText: searchText,
              }));
            }
          } catch (error) {
            console.error('Error fetching users:', error);
            setMentionModal(prev => ({
              ...prev,
              loading: false,
              users: [],
              searchText: searchText,
            }));
          }
        } else {
          // Nếu searchText không thay đổi, chỉ đảm bảo modal hiển thị
          setMentionModal(prev => ({
            ...prev,
            visible: true,
          }));
        }
      } else {
        // Không có @ + 1 chữ cái hợp lệ, ẩn modal
        if (mentionModal.visible) {
          setMentionModal(prev => ({
            ...prev,
            visible: false,
            searchText: '',
          }));
          setCurrentMentionStart(-1);
        }
      }
    } catch (error) {
      console.error('Error in checkForMention:', error);
      // Reset state nếu có lỗi
      setMentionModal(prev => ({
        ...prev,
        visible: false,
        searchText: '',
        loading: false,
      }));
      setCurrentMentionStart(-1);
    }
  }, [currentMentionStart, mentionModal.visible, mentionModal.searchText, editorInfo.y, updateModalPosition]);

  // Function để xử lý tìm kiếm trong modal
  const handleModalSearch = useCallback(async (searchText: string) => {
    if (!mentionModal.visible) return;

    setMentionModal(prev => ({
      ...prev,
      searchText: searchText,
      loading: true,
    }));

    try {
      const customerDA = new CustomerDA();
      const result = await customerDA.getAllCustomer(1, 20, searchText);
      if (result && result.code === 200) {
        const mappedUsers: UserMention[] = result.data.map((user: any) => ({
          Id: user.Id,
          Name: user.Name,
          Email: user.Email,
          AvatarUrl: ConfigAPI.getValidLink(user.AvatarUrl),
        }));
        setMentionModal(prev => ({
          ...prev,
          loading: false,
          users: mappedUsers,
        }));
      } else {
        setMentionModal(prev => ({
          ...prev,
          loading: false,
          users: [],
        }));
      }
    } catch (error) {
      console.error('Error fetching users in modal:', error);
      setMentionModal(prev => ({
        ...prev,
        loading: false,
        users: [],
      }));
    }
  }, [mentionModal.visible]);

  // Function để xử lý khi chọn user từ modal
  const selectUserForMention = useCallback((user: UserMention) => {
    if (currentMentionStart === -1) return;

    const fullText = getFullText(segments);

    // Tìm vị trí kết thúc của mention hiện tại (từ @ đến cursor)
    let mentionEnd = currentMentionStart + 1;
    while (mentionEnd < fullText.length && fullText[mentionEnd] !== ' ' && fullText[mentionEnd] !== '\n') {
      mentionEnd++;
    }

    // Tạo text mới với mention được thay thế (không có @, chỉ có tên)
    const beforeMention = fullText.substring(0, currentMentionStart);
    const afterMention = fullText.substring(mentionEnd);
    const mentionText = user.Name; // Chỉ tên người dùng, không có @

    // Tạo segments mới
    const newSegments: TextSegment[] = [];

    // Thêm text trước mention nếu có
    if (beforeMention) {
      newSegments.push({
        text: beforeMention,
      });
    }

    // Thêm mention segment (chỉ tên, không có @)
    newSegments.push({
      text: mentionText,
      isMention: true,
      mentionUserId: user.Id,
    });

    // Thêm khoảng cách sau mention
    newSegments.push({
      text: ' ',
    });

    // Thêm text sau mention nếu có
    if (afterMention) {
      newSegments.push({
        text: afterMention,
      });
    }

    // add thêm 1 newSegments vào segments và giữ nguyên các metions trước đó nếu có
    setSegments(newSegments);

    // Tính toán vị trí con trỏ mới (sau mention + khoảng cách)
    const newCursorPosition = beforeMention.length + mentionText.length + 1; // +1 cho khoảng cách

    // Đặt selection ở vị trí sau mention và khoảng cách
    setTimeout(() => {
      setSelection({
        start: newCursorPosition,
        end: newCursorPosition,
      });
    }, 0);

    // Đóng modal
    setMentionModal(prev => ({
      ...prev,
      visible: false,
    }));
    setCurrentMentionStart(-1);
  }, [currentMentionStart, segments]);

  // Utility functions cho hashtag và mention
  const isHashtagChar = (char: string): boolean => {
    return /^[a-zA-Z0-9_\u00C0-\u024F\u1E00-\u1EFF]$/.test(char);
  };



  // Hàm xử lý sự kiện selection change
  const handleSelectionChange = useCallback((event: any) => {
    const newSelection = event.nativeEvent.selection;
    if (newSelection) {
      setSelection(newSelection);

      // Chỉ kiểm tra mention khi cursor di chuyển và không có modal đang hiển thị
      if (!mentionModal.loading) {
        const fullText = getFullText(segments);
        if (fullText && newSelection.start !== undefined) {
          // Debounce để tránh gọi quá nhiều lần
          setTimeout(() => {
            checkForMention(fullText, newSelection.start).catch(console.error);
          }, 100);
        }
      }
    }
  }, [segments, checkForMention, mentionModal.loading]);

  // Loại bỏ các hàm format text - không cần thiết nữa

  const onChangeText = useCallback(
    (newText: string) => {
      const fullText = getFullText(segments);

      // Nếu không có thay đổi, không cần xử lý
      if (newText === fullText) return;

      // Xử lý hashtag trong tất cả segments
      const processHashtagInAllSegments = (inputSegments: TextSegment[]): TextSegment[] => {
        if (inputSegments.length === 0) return inputSegments;

        const result: TextSegment[] = [];

        for (let segIndex = 0; segIndex < inputSegments.length; segIndex++) {
          const segment = inputSegments[segIndex];

          // Nếu segment là mention hoặc không chứa #, giữ nguyên
          if (segment.isMention || !segment.text.includes('#')) {
            result.push(segment);
            continue;
          }

          // Xử lý hashtag trong segment hiện tại
          const text = segment.text;
          const newSegments: TextSegment[] = [];
          let currentIndex = 0;

          while (currentIndex < text.length) {
            const hashIndex = text.indexOf('#', currentIndex);

            if (hashIndex === -1) {
              // Không có hashtag nào nữa
              if (currentIndex < text.length) {
                newSegments.push({
                  ...segment,
                  text: text.substring(currentIndex),
                  isHashtag: false,
                  isMention: false,
                });
              }
              break;
            }

            // Thêm text trước hashtag
            if (hashIndex > currentIndex) {
              newSegments.push({
                ...segment,
                text: text.substring(currentIndex, hashIndex),
                isHashtag: false,
                isMention: false,
              });
            }

            // Xử lý hashtag
            let hashEnd = hashIndex + 1;
            while (hashEnd < text.length && isHashtagChar(text[hashEnd])) {
              hashEnd++;
            }

            if (hashEnd > hashIndex + 1) {
              // Có hashtag hợp lệ
              newSegments.push({
                ...segment,
                text: text.substring(hashIndex, hashEnd),
                isHashtag: true,
                isMention: false,
              });
              currentIndex = hashEnd;
            } else {
              // Chỉ có dấu # đơn lẻ
              newSegments.push({
                ...segment,
                text: '#',
                isHashtag: false,
                isMention: false,
              });
              currentIndex = hashIndex + 1;
            }
          }

          // Thêm các segment đã xử lý vào kết quả
          result.push(...newSegments);
        }

        return result;
      };



      // Cải thiện logic xử lý tiếng Việt - đơn giản hóa để tránh can thiệp quá nhiều
      // Chỉ xử lý các trường hợp đặc biệt thực sự cần thiết

      // Trường hợp 1: Text rỗng hoặc chỉ có 1 ký tự - xử lý đơn giản
      if (newText.length <= 1) {
        setSegments([
          {
            text: newText,
            isMention: false,
            isHashtag: false,
          },
        ]);
        setLastInputChar(newText);
        setIsComposingVietnamese(newText.length === 1 && isVietnameseChar(newText));
        return;
      }

      setSegments(prev => {
        // Tạo bản sao mới của mảng segments
        const newSegs = [...prev];

        // Kiểm tra mảng rỗng và khởi tạo nếu cần
        if (newSegs.length === 0) {
          return [
            {
              text: newText,
              bold: false,
              italic: false,
              underline: false,
            },
          ];
        }

        // Trường hợp 1: Xóa văn bản
        if (newText.length < fullText.length) {
          // Tìm vị trí bắt đầu khác biệt
          let diffIndex = 0;
          while (
            diffIndex < newText.length &&
            newText[diffIndex] === fullText[diffIndex]
          ) {
            diffIndex++;
          }

          // Số ký tự bị xóa
          const deletedCharCount = fullText.length - newText.length;

          // Tìm segment chứa vị trí cần xóa
          let charCount = 0;
          let targetSegmentIndex = -1;
          let positionInSegment = -1;

          for (let i = 0; i < newSegs.length; i++) {
            const segmentLength = newSegs[i].text.length;
            if (
              charCount <= diffIndex &&
              diffIndex < charCount + segmentLength
            ) {
              targetSegmentIndex = i;
              positionInSegment = diffIndex - charCount;
              break;
            }
            charCount += segmentLength;
          }

          // Nếu không tìm thấy segment (hiếm khi xảy ra)
          if (targetSegmentIndex === -1) {
            console.warn('Cannot find segment to delete from');
            return newSegs;
          }

          const segment = newSegs[targetSegmentIndex];

          // Xử lý xóa mention đặc biệt - xóa từng ký tự của tên người dùng
          if (segment.isMention) {
            // Đang xóa trong mention segment (chỉ có tên người dùng, không có @)
            const beforeText = segment.text.substring(0, positionInSegment);
            const afterText = segment.text.substring(positionInSegment + deletedCharCount);
            const remainingText = beforeText + afterText;

            if (remainingText.length > 0) {
              // Vẫn còn một phần tên người dùng
              newSegs[targetSegmentIndex] = {
                ...segment,
                text: remainingText,
              };
            } else {
              // Xóa toàn bộ mention
              newSegs.splice(targetSegmentIndex, 1);
            }

            // Xóa các segment trống
            const filteredSegs = newSegs.filter(seg => seg.text.length > 0);

            // Đảm bảo luôn có ít nhất một segment
            if (filteredSegs.length === 0) {
              return [
                {
                  text: '',
                  isMention: false,
                  isHashtag: false,
                },
              ];
            }

            return filteredSegs;
          }

          // Xóa văn bản từ vị trí đã xác định (logic cũ)
          const beforeText = segment.text.substring(0, positionInSegment);
          const afterText = segment.text.substring(
            positionInSegment + deletedCharCount,
          );

          // Cập nhật segment hiện tại
          newSegs[targetSegmentIndex] = {
            ...segment,
            text: beforeText + afterText,
          };

          // Xóa các segment trống
          const filteredSegs = newSegs.filter(seg => seg.text.length > 0);

          // Đảm bảo luôn có ít nhất một segment
          if (filteredSegs.length === 0) {
            return [
              {
                text: '',
                isMention: false,
                isHashtag: false,
              },
            ];
          }

          return filteredSegs;
        }

        // Trường hợp 2: Thêm hoặc thay đổi văn bản

        // Kiểm tra xem có phải đang thêm vào đầu dòng không
        // Trường hợp đặc biệt: khi newText bắt đầu bằng fullText (thêm vào đầu)
        if (newText.endsWith(fullText) && newText.length > fullText.length) {
          // Thêm vào đầu dòng
          const addedText = newText.substring(0, newText.length - fullText.length);

          // Thêm segment mới vào đầu
          newSegs.unshift({
            text: addedText,
            isMention: false,
            isHashtag: false,
          });

          return mergeAdjacentSegments(newSegs);
        }

        // Tìm điểm khác biệt đầu tiên giữa hai chuỗi
        let diffIndex = 0;
        const minLength = Math.min(fullText.length, newText.length);

        while (
          diffIndex < minLength &&
          fullText[diffIndex] === newText[diffIndex]
        ) {
          diffIndex++;
        }

        // Xác định phần văn bản được thêm vào
        // Trường hợp đơn giản: chỉ thêm văn bản (không thay thế)
        const addedText = newText.substring(diffIndex, diffIndex + (newText.length - fullText.length));



        // Tìm segment chứa điểm khác biệt
        let charCount = 0;
        let targetSegmentIndex = -1;
        let segmentStartIndex = 0;

        for (let i = 0; i < newSegs.length; i++) {
          const segmentLength = newSegs[i].text.length;

          // Kiểm tra xem diffIndex có nằm trong segment này không
          if (charCount <= diffIndex && diffIndex <= charCount + segmentLength) {
            targetSegmentIndex = i;
            segmentStartIndex = charCount;
            break;
          }
          charCount += segmentLength;
        }

        // Nếu không tìm thấy segment (điểm thay đổi ở cuối văn bản)
        if (targetSegmentIndex === -1) {
          // Thêm segment mới với định dạng đơn giản
          newSegs.push({
            text: addedText,
            isMention: false,
            isHashtag: false,
          });
          return mergeAdjacentSegments(newSegs);
        }

        // Xác định vị trí trong segment
        const positionInSegment = diffIndex - segmentStartIndex;

        const currentSegment = newSegs[targetSegmentIndex];

        // Cải thiện logic xử lý composing cho tiếng Việt
        // Đơn giản hóa để tránh can thiệp quá nhiều vào quá trình nhập liệu

        const changeLength = newText.length - fullText.length;
        const isAtEndOfSegment = positionInSegment === currentSegment.text.length;

        // Chỉ xử lý composing khi:
        // 1. Đang thêm/thay đổi ở cuối segment
        // 2. Thay đổi nhỏ (1-2 ký tự)
        // 3. Có dấu hiệu của việc gõ tiếng Việt
        const shouldHandleComposing =
          isAtEndOfSegment &&
          Math.abs(changeLength) <= 2 &&
          addedText.length <= 2 &&
          (isVietnameseChar(addedText) ||
           (addedText.length === 1 && currentSegment.text.length > 0 &&
            canReplaceLatinChar(currentSegment.text[currentSegment.text.length - 1], addedText)));

        if (shouldHandleComposing && positionInSegment > 0) {
          // Xử lý thay thế ký tự cho tiếng Việt
          if (
            changeLength === 0 && // Thay thế, không thêm mới
            addedText.length === 1 &&
            currentSegment.text.length >= 1 &&
            isVietnameseChar(addedText)
          ) {
            // Thay thế ký tự cuối cùng
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: currentSegment.text.slice(0, -1) + addedText,
            };
          } else {
            // Thêm text bình thường
            const beforeText = currentSegment.text.substring(0, positionInSegment);
            const afterText = currentSegment.text.substring(positionInSegment);
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: beforeText + addedText + afterText,
            };
          }
        } else {
          // Trường hợp thêm mới: Xử lý đơn giản hơn
          if (positionInSegment === 0) {
            // Thêm vào đầu segment
            newSegs.splice(targetSegmentIndex, 0, {
              text: addedText,
              isMention: false,
              isHashtag: false,
            });
          } else if (positionInSegment === currentSegment.text.length) {
            // Thêm vào cuối segment - đơn giản nhất
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: currentSegment.text + addedText,
            };
          } else {
            // Thêm vào giữa segment
            const beforeText = currentSegment.text.substring(0, positionInSegment);
            const afterText = currentSegment.text.substring(positionInSegment);

            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: beforeText + addedText + afterText,
            };
          }
        }

        // Hợp nhất segments thông thường
        const mergedSegs = mergeAdjacentSegments(newSegs, true); // true để bảo vệ mention

        // Xử lý hashtag ngay khi có dấu # trong text
        if (newText.includes('#')) {
          // Xử lý hashtag cho tất cả segments
          const processedSegs = processHashtagInAllSegments(mergedSegs);
          return processedSegs;
        }

        return mergedSegs;
      });

      // Kiểm tra mention sau khi xử lý text
      // Sử dụng vị trí cursor thực tế hoặc cuối text nếu không có selection
      const cursorPosition = selection.start !== undefined ? selection.start : newText.length;
      checkForMention(newText, cursorPosition).catch(console.error);

      // Cập nhật trạng thái
      if (newText.length > 0) {
        setLastInputChar(newText.charAt(newText.length - 1));
      } else {
        setLastInputChar('');
      }
      setIsComposingVietnamese(false);
    },
    [segments, selection, checkForMention],
  );

  return {
    segments,
    setSegments,
    selection,
    setSelection,
    handleSelectionChange,
    onChangeText,
    lastInputChar,
    isComposingVietnamese,
    mentionModal,
    setMentionModal,
    currentMentionStart,
    setCurrentMentionStart,
    selectUserForMention,
    updateModalPosition,
    updateEditorInfo,
    handleModalSearch,
  };
};
