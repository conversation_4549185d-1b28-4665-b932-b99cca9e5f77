import React, {useEffect, useRef} from 'react';
import {AppState, AppStateStatus, Linking, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  DeepLinkUrl,
  RootStackParamList,
} from '../Screen/Layout/navigation/ecomNavigator';
import {RootScreen} from '../router/router';
import {
  extractProductInfo,
  extractPostInfo,
  extractNewsInfo,
  extractLoginInfo,
  isValidDeepLink,
  parseDeepLink,
  extractEventsInfo,
} from '../utils/deepLinkUtils';
import {saveDataToAsyncStorage} from '../utils/AsyncStorage';

interface DeepLinkListenerProps {
  children?: React.ReactNode;
}

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

/**
 * Component that listens for deep links at the app level
 * This component should be placed high in the component tree (e.g., in App.tsx)
 * Similar to NotificationBadgeListener pattern
 */
const DeepLinkListener: React.FC<DeepLinkListenerProps> = ({children}) => {
  const navigation = useNavigation<NavigationProp>();
  const pendingUrl = useRef<string | null>(null);
  const navigationReady = useRef<boolean>(false);
  const initialUrlProcessed = useRef<boolean>(false);
  const listenerSetup = useRef<boolean>(false);

  // Check if navigation is available
  const isNavigationAvailable = () => {
    try {
      return (
        navigation &&
        typeof navigation.navigate === 'function' &&
        navigationReady.current
      );
    } catch (error) {
      console.warn('[DeepLinkListener] Navigation not available:', error);
      return false;
    }
  };

  useEffect(() => {
    console.log('🔗 [DeepLinkListener] Initialized');

    // Check if navigation is ready
    const unsubscribe = navigation.addListener('state', () => {
      navigationReady.current = true;
      console.log('🧭 [DeepLinkListener] Navigation is ready');

      // Process pending URL if exists
      if (pendingUrl.current) {
        const url = pendingUrl.current;
        pendingUrl.current = null;
        console.log('📝 [DeepLinkListener] Processing pending URL:', url);
        handleDeepLink(url);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [navigation]);

  useEffect(() => {
    // Handle initial URL when app opens from deep link
    const getInitialURL = async (): Promise<void> => {
      try {
        // For iOS, try multiple times as getInitialURL can be unreliable
        let initialUrl: string | null = null;
        let attempts = 0;
        const maxAttempts = Platform.OS === 'ios' ? 3 : 1;

        while (!initialUrl && attempts < maxAttempts) {
          attempts++;
          console.log(
            `🔍 [DeepLinkListener] Attempt ${attempts}/${maxAttempts} to get initial URL`,
          );

          try {
            initialUrl = await Linking.getInitialURL();
            if (initialUrl) {
              console.log(
                `✅ [DeepLinkListener] Got initial URL on attempt ${attempts}:`,
                initialUrl,
              );
              break;
            }
          } catch (attemptError) {
            console.warn(
              `❌ [DeepLinkListener] Attempt ${attempts} failed:`,
              attemptError,
            );
          }

          // Wait before next attempt (iOS only)
          if (Platform.OS === 'ios' && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(() => resolve(true), 200));
          }
        }

        if (initialUrl && !initialUrlProcessed.current) {
          initialUrlProcessed.current = true;
          console.log(
            '✅ [DeepLinkListener] App opened with initial URL:',
            initialUrl,
          );

          // iOS specific handling with longer delay
          const delay = Platform.OS === 'ios' ? 1000 : 300;
          setTimeout(() => {
            if (navigationReady.current) {
              handleDeepLink(initialUrl);
            } else {
              pendingUrl.current = initialUrl;
              console.log(
                '📝 [DeepLinkListener] Navigation not ready, storing URL for later',
              );
            }
          }, delay);
        } else if (!initialUrl) {
          console.log(
            '❌ [DeepLinkListener] No initial URL found after all attempts',
          );
        }
      } catch (error) {
        console.error(
          '❌ [DeepLinkListener] Error getting initial URL:',
          error,
        );
      }
    };

    // Setup URL listener
    const setupUrlListener = () => {
      if (listenerSetup.current) {
        console.log('⚠️ [DeepLinkListener] Listener already setup, skipping');
        return null;
      }

      console.log('🔗 [DeepLinkListener] Setting up URL listener...');

      const handleUrlChange = (event: {url: string}) => {
        console.log('🎯 [DeepLinkListener] URL received:', event.url);

        if (isNavigationAvailable()) {
          handleDeepLink(event.url);
        } else {
          pendingUrl.current = event.url;
          console.log(
            '📝 [DeepLinkListener] Navigation not ready, storing URL',
          );
        }
      };

      try {
        // Try modern addEventListener first
        const subscription = Linking.addEventListener('url', handleUrlChange);
        listenerSetup.current = true;
        console.log('✅ [DeepLinkListener] URL listener setup successful');
        return subscription;
      } catch (error) {
        console.error(
          '❌ [DeepLinkListener] URL listener setup failed:',
          error,
        );
        return null;
      }
    };

    // Setup listener and get initial URL
    const subscription = setupUrlListener();

    // Delay initial URL check to ensure everything is ready
    const initTimer = setTimeout(
      () => {
        getInitialURL();
      },
      Platform.OS === 'ios' ? 500 : 100,
    );

    return () => {
      clearTimeout(initTimer);
      subscription?.remove();
      listenerSetup.current = false;
    };
  }, []);

  useEffect(() => {
    // Listen for app state changes to handle URLs when app becomes active
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('📱 [DeepLinkListener] App state changed to:', nextAppState);

      if (nextAppState === 'active') {
        console.log(
          '🔄 [DeepLinkListener] App became active, checking for URLs',
        );

        // Small delay to ensure app is fully active
        setTimeout(async () => {
          try {
            const currentUrl = await Linking.getInitialURL();
            if (currentUrl && !initialUrlProcessed.current) {
              console.log(
                '🔍 [DeepLinkListener] Found URL on app active:',
                currentUrl,
              );
              debugger;
              handleDeepLink(currentUrl);
            }
          } catch (error) {
            console.warn(
              '❌ [DeepLinkListener] Error checking URL on app active:',
              error,
            );
          }
        }, 200);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription?.remove();
    };
  }, []);

  const handleDeepLink = (url: DeepLinkUrl): void => {
    console.log('\n=== [DeepLinkListener] PROCESSING DEEP LINK ===');
    console.log('Raw URL:', url);

    try {
      // Step 1: Validate URL
      if (!isValidDeepLink(url)) {
        console.warn('[DeepLinkListener] Invalid deep link format');
        safeNavigate(RootScreen.navigateEComView);
        return;
      }

      // Step 2: Parse URL
      const parsed = parseDeepLink(url);
      if (!parsed) {
        console.warn('[DeepLinkListener] Unable to parse deep link');
        safeNavigate(RootScreen.navigateEComView);
        return;
      }

      console.log(
        '[DeepLinkListener] Parsed Deep Link:',
        JSON.stringify(parsed, null, 2),
      );

      // Step 3: Navigate based on parsed data
      navigateToScreen(parsed);
    } catch (error) {
      console.error('[DeepLinkListener] Deep link error:', error);
      safeNavigate(RootScreen.navigateEComView);
    }

    console.log('=== [DeepLinkListener] END PROCESSING ===\n');
  };

  const safeNavigate = (screenName: string, params?: any): void => {
    if (!isNavigationAvailable()) {
      console.warn(
        '[DeepLinkListener] Navigation not available, storing URL for later',
      );
      pendingUrl.current = `${screenName}${
        params ? `?${JSON.stringify(params)}` : ''
      }`;
      return;
    }

    try {
      if (params) {
        navigation.navigate(screenName as any, params);
      } else {
        navigation.navigate(screenName as any);
      }
      console.log(`✅ [DeepLinkListener] Navigated to ${screenName}`);
    } catch (error) {
      console.error('[DeepLinkListener] Navigation error:', error);
      // Fallback navigation with delay
      setTimeout(() => {
        try {
          navigation.navigate(RootScreen.navigateEComView as any);
        } catch (fallbackError) {
          console.error(
            '[DeepLinkListener] Fallback navigation failed:',
            fallbackError,
          );
        }
      }, 500);
    }
  };

  const navigateToScreen = (parsed: any): void => {
    switch (parsed.screen?.toLowerCase()) {
      case 'product':
        handleProductNavigation(parsed);
        break;
      case 'posts':
        handlePostNavigation(parsed);
        break;
      case 'news':
        handleNewsNavigation(parsed);
        break;
      case 'events':
        handleEventsNavigation(parsed);
        break;
      case 'share':
        handleLoginNavigation(parsed);
        break;
      default:
        console.warn('[DeepLinkListener] Unknown screen:', parsed.screen);
        safeNavigate(RootScreen.navigateEComView);
    }
  };

  const handleProductNavigation = (parsed: any): void => {
    const productInfo = extractProductInfo(parsed);
    if (!productInfo?.productId) {
      console.warn('[DeepLinkListener] No product ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('[DeepLinkListener] Product Info:', productInfo);
    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {
          name: RootScreen.ProductDetail as any,
          params: {id: productInfo.productId},
        },
      ],
    });
  };

  const handlePostNavigation = (parsed: any): void => {
    const postInfo = extractPostInfo(parsed);
    if (!postInfo?.postId) {
      console.warn('[DeepLinkListener] No post ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('[DeepLinkListener] Post Info:', postInfo);
    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {name: RootScreen.PostDetail as any, params: {id: postInfo.postId}},
      ],
    });
  };

  const handleNewsNavigation = (parsed: any): void => {
    const newsInfo = extractNewsInfo(parsed);
    if (!newsInfo?.newsId) {
      console.warn('[DeepLinkListener] No news ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('[DeepLinkListener] News Info:', newsInfo);
    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {name: RootScreen.DetailNews as any, params: {id: newsInfo.newsId}},
      ],
    });
  };

  const handleEventsNavigation = (parsed: any): void => {
    const eventInfo = extractEventsInfo(parsed);
    if (!eventInfo?.eventId) {
      console.warn('[DeepLinkListener] No event ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('[DeepLinkListener] Event Info:', eventInfo);
    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {name: RootScreen.DetailEvent as any, params: {id: eventInfo.eventId}},
      ],
    });
  };

  const handleLoginNavigation = async (parsed: any): Promise<void> => {
    const loginInfo = extractLoginInfo(parsed);
    console.log('🔗 [DeepLinkListener] Share Link Info:', loginInfo);

    try {
      const refCode = loginInfo?.qrId || '';
      if (refCode) {
        await saveDataToAsyncStorage('RefCodeShared', refCode);
        console.log('✅ [DeepLinkListener] RefCodeShared saved:', refCode);
      } else {
        console.warn(
          '⚠️ [DeepLinkListener] No reference code found in share link',
        );
      }

      safeNavigate(RootScreen.navigateEComView);
    } catch (error) {
      console.error(
        '❌ [DeepLinkListener] Error saving share link info:',
        error,
      );
      safeNavigate(RootScreen.navigateEComView);
    }
  };

  // This component doesn't render anything visible, just like NotificationBadgeListener
  return <>{children}</>;
};

export default DeepLinkListener;
