import React, { Component, ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AppButton } from 'wini-mobile-components';
import { ColorThemes } from '../../../../assets/skin/colors';
import { TypoSkin } from '../../../../assets/skin/typography';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <View style={styles.container}>
          <Text style={styles.title}>Có lỗi xảy ra</Text>
          <Text style={styles.message}>
            Đã xảy ra lỗi không mong muốn. Vui lòng thử lại.
          </Text>
          <AppButton
            title="Thử lại"
            onPress={this.handleRetry}
            backgroundColor={ColorThemes.light.primary_main_color}
            containerStyle={styles.retryButton}
            textStyle={styles.retryButtonText}
            borderColor="transparent"
          />
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  title: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  retryButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.white,
    fontWeight: '600',
  },
});

export default ErrorBoundary;
