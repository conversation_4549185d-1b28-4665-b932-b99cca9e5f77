import {DataController} from '../../../base/baseController';

class RankConfigDa {
  private controller: DataController;
  constructor() {
    this.controller = new DataController('ConfigRank');
  }

  async fetchAll() {
    const res = await this.controller.getAll();
    if (res.code !== 200) return [];
    return res.data?.sort((a: any, b: any) => a.Sort - b.Sort);
  }
}

export default RankConfigDa;
