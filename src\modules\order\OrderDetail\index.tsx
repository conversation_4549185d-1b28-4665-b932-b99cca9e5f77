/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {useSelectorOrderState} from '../../../redux/hook/orderHook ';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';
import ListCard from '../list/ListCard';
import {useOrderSearch, useOrderData, useOrderType} from './hooks';
import {SearchSection, OrderInfoSection, LoadingIndicator} from './components';

const OrderDetail = () => {
  const orderInfo = useSelectorOrderState().data;

  // Custom hooks
  const {typeCard, setTypeCard} = useOrderType();
  const {numberCard} = useOrderData({orderInfo, typeCard});
  const {
    dataSearch,
    setDataSearch,
    dataSearchResult,
    numberCardSearch,
    isLoading,
  } = useOrderSearch({orderInfo, routeType: typeCard});

  return (
    <View style={styles.container}>
      {/* Header */}
      <InforHeader title={typeCard} />

      <View style={{flex: 1, marginHorizontal: 12}}>
        {/* Search Section */}
        <SearchSection setDataSearch={setDataSearch} />

        {/* Order Info Section */}
        <OrderInfoSection
          typeCard={typeCard}
          isLoading={isLoading}
          dataSearch={dataSearch}
          numberCardSearch={numberCardSearch}
          numberCard={numberCard}
        />

        {/* Content */}
        {isLoading ? (
          <LoadingIndicator />
        ) : (
          <ListCard
            type={typeCard}
            setTypeCard={setTypeCard}
            dataSearchResult={dataSearchResult}
            dataSearch={dataSearch}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});

export default OrderDetail;
