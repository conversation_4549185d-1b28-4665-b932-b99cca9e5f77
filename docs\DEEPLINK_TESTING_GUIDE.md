# Deep Link Testing Guide

## 🎯 Overview

This guide provides comprehensive instructions for testing the new DeepLink system in the Chainivo app.

## 🏗️ New Architecture

### Components
- **DeepLinkManager**: Centralized service handling all deeplink logic
- **DeepLinkProvider**: React component wrapper for initialization
- **DeepLinkUtils**: Parsing and validation utilities
- **LinkingConfiguration**: React Navigation configuration

### Supported URL Patterns

#### Custom Scheme
```
chainivo://posts/123
chainivo://news/456
chainivo://product/789
chainivo://share/ABC123
chainivo://events/101
chainivo://
```

#### Universal Links
```
https://chainivo.com/posts/123
https://chainivo.com/news/456
https://chainivo.com/product/789
https://chainivo.com/share/ABC123
https://chainivo.com/events/101
https://chainivo.com/
```

## 📱 Testing Methods

### 1. iOS Simulator Testing

#### Method A: Safari Browser
1. Open Safari in iOS Simulator
2. Type the deeplink URL in address bar
3. Tap "Open" when prompted

#### Method B: Notes App
1. Open Notes app
2. Type the deeplink URL
3. Tap the link

#### Method C: ADB Command (if using device)
```bash
xcrun simctl openurl booted "chainivo://posts/123"
```

### 2. Android Testing

#### Method A: ADB Command
```bash
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "chainivo://posts/123" \
  com.wini_core_mobile
```

#### Method B: Browser
1. Open Chrome/Browser
2. Type the deeplink URL
3. Tap "Open with Chainivo"

### 3. Manual Testing via Settings Screen

The app includes a built-in testing interface:

1. Navigate to Settings
2. Look for "Deep Link Test" section
3. Enter URL and tap "Test"

## 🧪 Test Cases

### 1. App Cold Start (App Not Running)

**Test URLs:**
```
chainivo://posts/123
https://chainivo.com/news/456
chainivo://product/789
```

**Expected Behavior:**
- App launches
- Navigates to correct screen with ID
- Shows loading states appropriately

### 2. App Warm Start (App in Background)

**Test URLs:**
```
chainivo://share/ABC123
https://chainivo.com/events/101
```

**Expected Behavior:**
- App comes to foreground
- Navigates to correct screen
- Previous navigation stack is reset

### 3. App Active (App in Foreground)

**Test URLs:**
```
chainivo://posts/999
https://chainivo.com/product/888
```

**Expected Behavior:**
- Immediate navigation
- Smooth transition
- No app restart

### 4. Invalid URLs

**Test URLs:**
```
chainivo://invalid/123
https://example.com/posts/123
invalid://url
chainivo://posts/
```

**Expected Behavior:**
- Navigate to Home screen
- Show appropriate error logs (in dev mode)
- No app crashes

### 5. URLs with Query Parameters

**Test URLs:**
```
chainivo://posts/123?ref=share&campaign=social
https://chainivo.com/news/456?utm_source=app&utm_campaign=test
```

**Expected Behavior:**
- Navigate to correct screen
- Query parameters are parsed and available
- Analytics tracking works (if implemented)

### 6. Share Link (QR Code) Testing

**Test URLs:**
```
chainivo://share/TESTREF123
https://chainivo.com/share/QRCODE456
```

**Expected Behavior:**
- Navigate to Login/Home screen
- RefCodeShared is saved to AsyncStorage
- Can verify in app settings or debug logs

## 🔍 Debugging

### Console Logs

Look for these log patterns:
```
[DeepLinkManager] Handling deeplink: chainivo://posts/123
[DeepLinkManager] Successfully navigated to PostDetail
[DeepLinkProvider] URL received: chainivo://posts/123
```

### Error Logs

Common error patterns:
```
[DeepLinkManager] Invalid URL: URL does not match any supported scheme
[DeepLinkManager] Navigation error: Navigation ref not available
[DeepLinkManager] Parsing error: URL parsing error
```

### AsyncStorage Verification

For share links, verify RefCodeShared is saved:
```javascript
// In React Native Debugger or dev tools
import AsyncStorage from '@react-native-async-storage/async-storage';
AsyncStorage.getItem('RefCodeShared').then(console.log);
```

## ⚠️ Common Issues & Solutions

### Issue: "App doesn't open from browser"
**Solution:** 
- Check URL scheme registration in Info.plist (iOS) or AndroidManifest.xml (Android)
- Verify app is installed and scheme is not conflicting

### Issue: "Navigation doesn't work"
**Solution:**
- Check if navigation ref is properly initialized
- Verify screen names match RootScreen enum
- Check for navigation timing issues

### Issue: "Share link doesn't save RefCode"
**Solution:**
- Check AsyncStorage permissions
- Verify URL parsing for share links
- Check error logs for AsyncStorage failures

### Issue: "Universal links don't work"
**Solution:**
- Verify associated domains configuration
- Check if domain verification is complete
- Test with custom scheme first

## 📊 Performance Testing

### Metrics to Monitor
- App launch time with deeplinks
- Navigation response time
- Memory usage during deeplink handling
- Battery impact (for background processing)

### Tools
- React Native Performance Monitor
- Flipper Network Inspector
- Xcode Instruments (iOS)
- Android Studio Profiler

## 🚀 Production Testing

### Pre-release Checklist
- [ ] All test cases pass
- [ ] No console errors in production build
- [ ] Universal links work on real devices
- [ ] Share functionality works correctly
- [ ] Analytics tracking works (if implemented)
- [ ] Performance is acceptable

### Post-release Monitoring
- Monitor crash reports for deeplink-related issues
- Track deeplink usage analytics
- Monitor user feedback for navigation issues

## 📝 Reporting Issues

When reporting deeplink issues, include:
1. Device type and OS version
2. App version
3. Exact URL that failed
4. Steps to reproduce
5. Console logs (if available)
6. Expected vs actual behavior

## 🔧 Development Tips

### Adding New Deeplink Types
1. Update `DEEPLINK_PATTERNS` in `DeepLinkTypes.ts`
2. Add new case in `createParsedDeepLink` function
3. Update `RootStackParamList` type
4. Add screen to navigation stack
5. Write tests for new pattern

### Debugging Tips
- Use `__DEV__` flag for detailed logging
- Test on both iOS and Android
- Use React Native Debugger for AsyncStorage inspection
- Test with both custom scheme and universal links
