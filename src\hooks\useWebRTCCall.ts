/**
 * useWebRTCCall Hook
 * React hook for managing WebRTC call functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { MediaStream } from 'react-native-webrtc';
import WebRTCService from '../services/webrtc/WebRTCService';
import {
  CallStatus,
  CallData,
  UserInfo,
  CallTimer,
  CallControlsState,
  WebRTCError,
} from '../services/webrtc/types/WebRTCTypes';

interface UseWebRTCCallReturn {
  // Call state
  callStatus: CallStatus;
  currentCall: CallData | null;
  callTimer: CallTimer;
  callControls: CallControlsState;
  remoteStream: MediaStream | null;
  
  // Call actions
  startCall: (targetUser: UserInfo, currentUser: UserInfo) => Promise<void>;
  acceptCall: () => Promise<void>;
  rejectCall: () => void;
  endCall: () => void;
  
  // Call controls
  toggleMute: () => boolean;
  toggleSpeaker: () => boolean;
  
  // Error state
  error: string | null;
  clearError: () => void;
}

/**
 * Hook for WebRTC call functionality
 */
export const useWebRTCCall = (): UseWebRTCCallReturn => {
  // State
  const [callStatus, setCallStatus] = useState<CallStatus>(CallStatus.IDLE);
  const [currentCall, setCurrentCall] = useState<CallData | null>(null);
  const [callTimer, setCallTimer] = useState<CallTimer>({ startTime: 0, duration: 0, isRunning: false });
  const [callControls, setCallControls] = useState<CallControlsState>({
    isMuted: false,
    isSpeakerOn: false,
    currentAudioDevice: 'earpiece' as any,
    availableAudioDevices: [],
  });
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Update state from service
  const updateState = useCallback(() => {
    setCallStatus(WebRTCService.getCallStatus());
    setCurrentCall(WebRTCService.getCurrentCall());
    setCallTimer(WebRTCService.getCallTimer());
    setCallControls(WebRTCService.getCallControls());
  }, []);

  // Initialize WebRTC service callbacks
  useEffect(() => {
    console.log('🎣 Initializing useWebRTCCall hook');

    // Set up callbacks
    WebRTCService.setCallbacks({
      onCallStatusChanged: (status: CallStatus, callData: CallData) => {
        console.log('📞 Call status changed:', status);
        setCallStatus(status);
        setCurrentCall(callData);
        setError(null); // Clear error on status change
      },
      
      onRemoteStreamReceived: (stream: MediaStream) => {
        console.log('📡 Remote stream received');
        setRemoteStream(stream);
      },
      
      onCallEnded: (reason: string, callData: CallData) => {
        console.log('📞 Call ended:', reason);
        setRemoteStream(null);
        setError(null);
        updateState();
      },
      
      onCallFailed: (errorMessage: string, callData: CallData) => {
        console.log('❌ Call failed:', errorMessage);
        setError(errorMessage);
        setRemoteStream(null);
        updateState();
      },
      
      onConnectionStateChanged: (state: string) => {
        console.log('🔗 Connection state changed:', state);
        // Update state when connection changes
        updateState();
      },
    });

    // Initial state update
    updateState();

    // Timer update interval
    const timerInterval = setInterval(() => {
      const timer = WebRTCService.getCallTimer();
      if (timer.isRunning) {
        setCallTimer(timer);
      }
    }, 1000);

    // Cleanup
    return () => {
      clearInterval(timerInterval);
      console.log('🎣 Cleaning up useWebRTCCall hook');
    };
  }, [updateState]);

  // Call actions
  const startCall = useCallback(async (targetUser: UserInfo, currentUser: UserInfo) => {
    try {
      setError(null);
      console.log(`📞 Starting call to ${targetUser.name}`);
      await WebRTCService.startCall(targetUser, currentUser);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start call';
      console.error('❌ Error starting call:', errorMessage);
      setError(errorMessage);
    }
  }, []);

  const acceptCall = useCallback(async () => {
    try {
      setError(null);
      console.log('📞 Accepting call');
      await WebRTCService.acceptCall();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to accept call';
      console.error('❌ Error accepting call:', errorMessage);
      setError(errorMessage);
    }
  }, []);

  const rejectCall = useCallback(() => {
    try {
      setError(null);
      console.log('📞 Rejecting call');
      WebRTCService.rejectCall();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reject call';
      console.error('❌ Error rejecting call:', errorMessage);
      setError(errorMessage);
    }
  }, []);

  const endCall = useCallback(() => {
    try {
      setError(null);
      console.log('📞 Ending call');
      WebRTCService.endCall();
      setRemoteStream(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to end call';
      console.error('❌ Error ending call:', errorMessage);
      setError(errorMessage);
    }
  }, []);

  // Call controls
  const toggleMute = useCallback(() => {
    try {
      const result = WebRTCService.toggleMute();
      updateState();
      return result;
    } catch (err) {
      console.error('❌ Error toggling mute:', err);
      return false;
    }
  }, [updateState]);

  const toggleSpeaker = useCallback(() => {
    try {
      const result = WebRTCService.toggleSpeaker();
      updateState();
      return result;
    } catch (err) {
      console.error('❌ Error toggling speaker:', err);
      return false;
    }
  }, [updateState]);

  // Error handling
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    callStatus,
    currentCall,
    callTimer,
    callControls,
    remoteStream,
    
    // Actions
    startCall,
    acceptCall,
    rejectCall,
    endCall,
    
    // Controls
    toggleMute,
    toggleSpeaker,
    
    // Error handling
    error,
    clearError,
  };
};

/**
 * Hook for call status only (lightweight)
 */
export const useCallStatus = (): CallStatus => {
  const [callStatus, setCallStatus] = useState<CallStatus>(CallStatus.IDLE);

  useEffect(() => {
    // Set up minimal callback for status only
    WebRTCService.setCallbacks({
      onCallStatusChanged: (status: CallStatus) => {
        setCallStatus(status);
      },
    });

    // Initial state
    setCallStatus(WebRTCService.getCallStatus());
  }, []);

  return callStatus;
};

/**
 * Hook for call timer only
 */
export const useCallTimer = (): CallTimer => {
  const [callTimer, setCallTimer] = useState<CallTimer>({ startTime: 0, duration: 0, isRunning: false });

  useEffect(() => {
    const interval = setInterval(() => {
      const timer = WebRTCService.getCallTimer();
      setCallTimer(timer);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return callTimer;
};

export default useWebRTCCall;
