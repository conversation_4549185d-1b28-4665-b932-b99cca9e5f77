import React from 'react';
import {StyleSheet, View} from 'react-native';
import {FDialog} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {useProfilePage} from './hooks/useProfilePage';
import ProgressSection from './components/ProgressSection';
import OrderSummary from './components/OrderSummary';
import ActionList from './components/ActionList';

export default function ProfilePage({select}: any) {
  const {customer, loading, dialogRef, orderDetail, dispatch} =
    useProfilePage(select);

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      {customer || loading ? <ProgressSection loading={loading} /> : null}
      {customer && <OrderSummary orderDetail={orderDetail} />}
      <ActionList
        customer={customer}
        dialogRef={dialogRef}
        dispatch={dispatch}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});
