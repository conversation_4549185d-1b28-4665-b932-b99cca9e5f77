import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, StyleSheet, Alert } from 'react-native';
import { useTranslation, LanguageUtils } from '../locales/useTranslation';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';
import { closePopup, ListTile, Winicon } from 'wini-mobile-components';

interface LanguageSwitcherProps {
  ref: any;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ ref }) => {

  const {t, language, changeLanguage} = useTranslation();
  const [isChanging, setIsChanging] = useState(false);

  const handleLanguageChange = async (newLanguage: string) => {
    if (newLanguage === language || isChanging) return;
    setIsChanging(true);
    try {
      await changeLanguage(newLanguage);

      // Hi<PERSON>n thị thông báo thành công
      setTimeout(() => {
        Alert.alert(
          t('common.success'),
          `${t('profile.language')}: ${LanguageUtils.getLanguageDisplayName(
            newLanguage,
          )}`,
          [
            {
              text: t('common.ok'),
              onPress: () => closePopup(ref),
            },
          ],
        );
      }, 300);
    } catch (error) {
      Alert.alert(t('common.error'), 'Không thể thay đổi ngôn ngữ');
    } finally {
      setIsChanging(false);
    }
  };

  const supportedLanguages = LanguageUtils.getSupportedLanguages();

  const renderLanguageItem = (lang: any) => (
    <ListTile
      key={lang.code}
      onPress={() => handleLanguageChange(lang.code)}
      style={{
        borderColor: ColorThemes.light.neutral_main_border_color,
        borderWidth: 0,
        borderBottomColor: ColorThemes.light.neutral_main_border_color,
        borderBottomWidth: 1,
        padding: 0,
        paddingHorizontal: 8,
        paddingVertical: 8,
      }}
      title={
        <View style={styles.languageInfo}>
          <Text
            style={[
              styles.languageName,
              language === lang.code && styles.selectedLanguageName,
            ]}>
            {lang.nativeName}
          </Text>
          <Text style={styles.languageCode}>{lang.name}</Text>
        </View>
      }
      trailing={
        language === lang.code && (
          <Winicon
            src="fill/layout/circle-check"
            color={ColorThemes.light.primary_main_color}
            size={20}
          />
        )
      }
    />
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('common.selectLanguage')}</Text>
      <View>
      {supportedLanguages.map(renderLanguageItem)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width:'100%',
    padding: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  title: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 24,
    textAlign: 'center',
  },
  languageButton: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  selectedLanguage: {
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 8,
  },
  languageText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.neutral_text_body_color,
    textAlign: 'center',
  },
  selectedLanguageText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: 'bold',
  },

  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginVertical: 4,
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedLanguageItem: {
    backgroundColor: ColorThemes.light.primary_light,
    borderColor: ColorThemes.light.primary_main,
  },
  languageInfo: {},
  languageName: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 2,
  },
  selectedLanguageName: {
    color: ColorThemes.light.primary_main,
    fontWeight: 'bold',
  },
  languageCode: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default LanguageSwitcher;

