import {createNativeStackNavigator} from '@react-navigation/native-stack';
import SettingProfile from '../../../modules/customer/setting/setting';
import {navigateReset, RootScreen} from '../../../router/router';
import {useDispatch} from 'react-redux';
import {useEffect} from 'react';
import LoginScreen from '../../../modules/customer/login';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {SplashScreen} from '../Splash';
import BiometricSetting from '../../../modules/customer/setting/biometricSetting';
import NotificationIndex from '../../../modules/notification/view';
import VnpayPaymentScreen from '../../../utils/vnpayWebview';
import ForgotPass from '../../../modules/customer/form/forgot-pass';
import FAQView from '../../../modules/customer/listview/FAQView';
import IntroPage from '../../Page/Intro';
import EComLayout from '../mainLayout';
import ProductDetail from '../../../modules/Product/productDetail';
import RegisterShop from '../../../modules/shop/RegisterShop';
import OrderDetail from '../../../modules/order/OrderDetail';
import Shop from '../../../modules/shop/ManageShop';
import Review from '../../../modules/shop/Review';
import DetailNews from '../../../modules/news/detail';
import HotProductsDemo from '../../Page/HotProductsDemo';
import ProductListByCategory from '../../../modules/Product/list/ProductListByCategory';
import {SearchIndex} from '../../../modules/Product/list/searchIndex';
import ManageProduct from '../../../modules/Product/ManageProduct';
import CreateNewProduct from '../../../modules/Product/CreateNewProduct';
import CartPage from '../../Page/CartPage';
import ChartReport from '../../../modules/shop/ShopReport';
import CreateReviewOrderDetail from '../../../modules/rating/CreateReviewOrder';
import CreateReviewOrder from '../../../modules/shop/CreateReviewOrder';
import MyWallet from '../../Page/myWallet';
import ConfigAffiliate from '../../../modules/shop/configAffiliate';
import TreeAffiliateDetail from '../../../modules/shop/treeAffiliateDetail';
import PolicyView from '../../../modules/customer/setting/policy';
import MyWalletProfile from '../../Page/MyWalletProfile';
import TransactionHistory from '../../Page/TransactionHistory';
import TransferCANPointScreen from '../../Page/TransferCANPoint/TransferCANPointScreen';
import NewsScreen from '../../../modules/news/NewsScreen';
import FavoriteProduct from '../../../modules/Product/FavoriteProduct';
import ProfileRankScreen from '../../../modules/customer/ProfileRank';
import TwoFactorAuthScreen from '../../Page/TwoFactorAuth/TwoFactorAuthScreen';
import MyAddress from '../../../modules/customer/setting/myAddress';
import EditAddress from '../../../modules/customer/form/edit-address';
import PaymentSetting from '../../../modules/customer/setting/paymentSetting';
import OrderDetailPage from '../../Page/OrderDetailPage';
import GiftExchange from '../../Page/GiftExchange';
import GiftDetail from '../../Page/GiftDetail';
import EmailAuthScreen from '../../Page/EmailAuth/EmailAuthScreen';
import ChatMainScreen from '../../../modules/chat/screens/ChatMainScreen';
import ChatListScreen from '../../../modules/chat/screens/ChatListScreen';
import ChatRoomScreen from '../../../modules/chat/screens/ChatRoomScreen';
import ContactsScreen from '../../../modules/chat/screens/ContactsScreen';
import CallHistoryScreen from '../../../modules/chat/screens/CallHistoryScreen';
import CallScreen from '../../../modules/call/screens/CallScreen';
import CreateGroupScreen from '../../../modules/chat/screens/CreateGroupScreen';
import ProductIndex from '../../../modules/Product/ProductIndex';
import OrderCustomerDetail from '../../../modules/order/OrderCustomerDetail';
import WIthDrawMoney from '../../Page/WIthDrawMoney/WithDraw money';
import RatingScreen from '../../../modules/Product/list/rating';
import DetailEvent from '../../../modules/event/DetailEvent';
import InforShopView from '../../../components/shop/inforShopView';
import OrderDetailPageForShop from '../../Page/OrderDetailPageForShop';
import ListByHashtagScreen from '../../../modules/news/ListByHashtagScreen';
import PostDetail from '../../../modules/community/detail/postDetail';
import CreatePost from '../../../modules/community/pages/createPost';
import ProfileCommunity from '../../../modules/community/pages/profileIndex';
import {SearchCommunity} from '../../../modules/community/pages/searchCommunity';
import ProductShopScreen from '../../../modules/Product/ProductShopScreen';
import ShopPromotion from '../../../modules/shop/ShopPromotion';
import RatingForAllScreen from '../../../modules/Product/list/ratingForAll';
// Deep link handling moved to App.tsx with DeepLinkListener component
import BonusFundPage from 'Screen/Page/BonusFundPage';
import LanguageSelectionScreen from '../../Page/LanguageSelectionScreen';

const Ecom = createNativeStackNavigator();

export type RootStackParamList = {
  [RootScreen.splashView]: undefined;
  [RootScreen.login]: {id: string};
  [RootScreen.navigateEComView]: undefined;
  [RootScreen.ProductDetail]: {
    id: string;
  };
  [RootScreen.PostDetail]: {
    id: string;
  };
  [RootScreen.DetailNews]: {id: string};
  [RootScreen.Intro]: undefined;
  [key: string]: any; // Cho phép các screen khác
};

export type DeepLinkUrl = string;

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

export function EComStackNavigator() {
  // Deep link handling moved to App.tsx with DeepLinkListener component
  return (
    <Ecom.Navigator
      screenOptions={{headerShown: false, orientation: 'portrait'}}>
      <Ecom.Screen
        name={RootScreen.splashView}
        component={SplashScreenWithAuthCheck}
      />
      <Ecom.Screen
        name={RootScreen.login}
        component={LoginScreen}
        options={{animation: 'fade'}}
      />
      <Ecom.Screen name={RootScreen.navigateEComView} component={EComLayout} />
      <Ecom.Screen
        name={RootScreen.Notification}
        component={NotificationIndex}
      />
      <Ecom.Screen
        name={RootScreen.SettingProfile}
        component={SettingProfile}
      />
      <Ecom.Screen name={RootScreen.ProductDetail} component={ProductDetail} />
      <Ecom.Screen name={RootScreen.DetailNews} component={DetailNews} />
      <Ecom.Screen name={RootScreen.DetailEvent} component={DetailEvent} />
      <Ecom.Screen name={RootScreen.CartPage} component={CartPage} />
      <Ecom.Screen
        name={RootScreen.CheckoutPage}
        component={require('../../Page/CheckoutPage').default}
      />
      <Ecom.Screen
        name={RootScreen.BiometricSetting}
        component={BiometricSetting}
      />
      <Ecom.Screen name={RootScreen.ForgotPass} component={ForgotPass} />
      <Ecom.Screen
        name={RootScreen.VnpayPaymentScreen}
        component={VnpayPaymentScreen}
      />
      <Ecom.Screen name={RootScreen.Intro} component={IntroPage} />
      <Ecom.Screen
        name={RootScreen.OrderDetailPage}
        component={OrderDetailPage}
      />
      <Ecom.Screen
        name={RootScreen.HotProductsDemo}
        component={HotProductsDemo}
      />
      <Ecom.Screen
        name={RootScreen.AllHotProductsPage}
        component={require('../../Page/AllHotProductsPage').default}
      />
      <Ecom.Screen name={RootScreen.OrderDetail} component={OrderDetail} />
      <Ecom.Screen
        name={RootScreen.OrderCustomerDetail}
        component={OrderCustomerDetail}
      />
      <Ecom.Screen name={RootScreen.RegisterShop} component={RegisterShop} />
      <Ecom.Screen name={RootScreen.Shop} component={Shop} />
      <Ecom.Screen name={RootScreen.ManageProduct} component={ManageProduct} />
      <Ecom.Screen
        name={RootScreen.CreateNewProduct}
        component={CreateNewProduct}
      />
      <Ecom.Screen name={RootScreen.SearchIndex} component={SearchIndex} />
      <Ecom.Screen
        name={RootScreen.ProductListByCategory}
        component={ProductListByCategory}
      />
      <Ecom.Screen name={RootScreen.MyWallet} component={MyWallet} />
      <Ecom.Screen name={RootScreen.Review} component={Review} />
      <Ecom.Screen name={RootScreen.ChartReport} component={ChartReport} />
      <Ecom.Screen
        name={RootScreen.ConfigAffiliate}
        component={ConfigAffiliate}
      />
      <Ecom.Screen
        name={RootScreen.TreeAffiliateDetail}
        component={TreeAffiliateDetail}
      />
      <Ecom.Screen
        name={RootScreen.CreateReviewOrderDetail}
        component={CreateReviewOrderDetail}
      />
      <Ecom.Screen
        name={RootScreen.CreateReviewOrder}
        component={CreateReviewOrder}
      />
      <Ecom.Screen
        name={RootScreen.MyWalletProfile}
        component={MyWalletProfile}
      />
      <Ecom.Screen
        name={RootScreen.TransactionHistory}
        component={TransactionHistory}
      />
      <Ecom.Screen
        name={RootScreen.TransferCANPoint}
        component={TransferCANPointScreen}
      />
      <Ecom.Screen name={RootScreen.PolicyView} component={PolicyView} />
      <Ecom.Screen name={RootScreen.FAQView} component={FAQView} />

      <Ecom.Screen name={RootScreen.NewsScreen} component={NewsScreen} />
      <Ecom.Screen
        name={RootScreen.FavoriteProduct}
        component={FavoriteProduct}
      />
      <Ecom.Screen name={RootScreen.MyAddress} component={MyAddress} />
      <Ecom.Screen name={RootScreen.EditAddress} component={EditAddress} />
      <Ecom.Screen
        name={RootScreen.ProfileRankScreen}
        component={ProfileRankScreen}
      />
      <Ecom.Screen
        name={RootScreen.TwoFactorAuth}
        component={TwoFactorAuthScreen}
      />
      <Ecom.Screen name={RootScreen.ShopPromotion} component={ShopPromotion} />
      <Ecom.Screen
        name={RootScreen.PaymentSetting}
        component={PaymentSetting}
      />
      <Ecom.Screen name={RootScreen.GiftExchange} component={GiftExchange} />
      <Ecom.Screen name={RootScreen.GiftDetail} component={GiftDetail} />

      {/* Chat Screens */}
      <Ecom.Screen
        name={RootScreen.ChatMain}
        component={ChatMainScreen}
        options={{title: 'Chat'}}
      />
      <Ecom.Screen
        name={RootScreen.ChatList}
        component={ChatListScreen}
        options={{title: 'Tin nhắn'}}
      />
      <Ecom.Screen
        name={RootScreen.ChatRoom}
        component={ChatRoomScreen}
        options={{title: 'Chat'}}
      />
      <Ecom.Screen
        name={RootScreen.CreateGroup}
        component={CreateGroupScreen}
        options={{title: 'Tạo nhóm'}}
      />
      <Ecom.Screen
        name={RootScreen.Contacts}
        component={ContactsScreen}
        options={{title: 'Danh bạ'}}
      />
      <Ecom.Screen
        name={RootScreen.CallHistory}
        component={CallHistoryScreen}
        options={{title: 'Lịch sử cuộc gọi'}}
      />
      <Ecom.Screen
        name={RootScreen.CallScreen}
        component={CallScreen}
        options={{title: 'Cuộc gọi'}}
      />
      <Ecom.Screen name={RootScreen.AccountAuth} component={EmailAuthScreen} />
      <Ecom.Screen name={RootScreen.ProductIndex} component={ProductIndex} />
      <Ecom.Screen name={RootScreen.WithDrawMoney} component={WIthDrawMoney} />
      <Ecom.Screen name={RootScreen.RatingScreen} component={RatingScreen} />
      <Ecom.Screen
        name={RootScreen.RatingForAllScreen}
        component={RatingForAllScreen}
      />
      <Ecom.Screen name={RootScreen.InforShopView} component={InforShopView} />
      <Ecom.Screen
        name={RootScreen.OrderDetailPageForShop}
        component={OrderDetailPageForShop}
      />
      <Ecom.Screen
        name={RootScreen.ListByHashtagScreen}
        component={ListByHashtagScreen}
      />
      <Ecom.Screen name={RootScreen.PostDetail} component={PostDetail} />
      <Ecom.Screen name={RootScreen.createPost} component={CreatePost} />
      <Ecom.Screen
        name={RootScreen.ProfileCommunity}
        component={ProfileCommunity}
      />
      <Ecom.Screen
        name={RootScreen.SearchCommunity}
        component={SearchCommunity}
      />
      <Ecom.Screen
        name={RootScreen.ProductShopScreen}
        component={ProductShopScreen}
      />
      <Ecom.Screen name={RootScreen.BonusFundPage} component={BonusFundPage} />
      <Ecom.Screen
        name={RootScreen.LanguageSelection}
        component={LanguageSelectionScreen}
      />
    </Ecom.Navigator>
  );
}

const SplashScreenWithAuthCheck = () => {
  const dispatch = useDispatch<any>();
  useEffect(() => {
    const checkAuthAndNavigate = async () => {
      try {
        // Wait for a minimum of 3 seconds for splash screen
        const splashTimer = new Promise(resolve =>
          setTimeout(() => resolve(true), 3000),
        );

        // Check for user token
        const tokenCheck = await getDataToAsyncStorage('accessToken');

        // Wait for both operations to complete
        const [_, accessToken] = await Promise.all([splashTimer, tokenCheck]);

        // check the first time open app and show IntroPage
        const isFirstTime = await getDataToAsyncStorage('isFirstTime');
        if (!isFirstTime) {
          await saveDataToAsyncStorage('isFirstTime', 'true');
          navigateReset(RootScreen.Intro);
          return;
        }

        // get info
        if (accessToken) {
          await dispatch(CustomerActions.getInfor(true));

          // Khởi tạo socket connection ngay khi app khởi động và user đã login
          // Delay một chút để đảm bảo customer data đã được load
          setTimeout(async () => {
            const AuthSocketService =
              require('../../../services/AuthSocketService').default;
            await AuthSocketService.initializeSocketConnection();
            console.log('🔌 [SplashScreen] Socket initialized on app startup');
          }, 1000);
        }

        const nextRoute =
          // accessToken
          //   ? RootScreen.navigateEComView
          //   : RootScreen.login;
          RootScreen.navigateEComView;

        navigateReset(nextRoute);
      } catch (error) {
        console.error('Authentication check error:', error);
        // Default to Auth flow if error occurs
        navigateReset(RootScreen.login);
      }
    };

    checkAuthAndNavigate();
  }, []);

  return <SplashScreen />;
};
