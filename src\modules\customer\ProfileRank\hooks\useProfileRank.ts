import {useEffect, useState} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';
import RankConfigDa from '../../../configRank/da/rankConfigDa';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {RankInfo} from '../../../../redux/types/rankTypes';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {RootState} from '../../../../redux/store/store';

export const useProfileRank = () => {
  const dispatch = useDispatch();
  const customer = useSelectorCustomerState().data;
  const {rankInfo} = useSelector((state: RootState) => state.customer);
  const configDa = new RankConfigDa();
  const [ranksData, setRanksData] = useState<RankInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (!customer?.Id) return;
        setLoading(true);

        // Fetch all ranks data
        const allRanks = await configDa.fetchAll();
        setRanksData(
          allRanks.sort((a: RankInfo, b: RankInfo) => a.Score - b.Score),
        );

        // Get current rank info from reducer if not available
        if (!rankInfo) {
          await CustomerActions.getRankInfo(dispatch);
        }

        setLoading(false);
      } catch (error) {
        showSnackbar({
          message: 'Lỗi khi lấy dữ liệu',
          status: ComponentStatus.ERROR,
        });
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [customer?.Id, dispatch, rankInfo]);

  // Use rankInfo from reducer as currentRankInfo
  const currentRankInfo = rankInfo || null;

  return {customer, ranksData, currentRankInfo, loading};
};
