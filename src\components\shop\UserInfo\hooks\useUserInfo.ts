import {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import ImagePicker from 'react-native-image-crop-picker';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';

import {RootState} from '../../../../redux/store/store';
import {BaseDA} from '../../../../base/BaseDA';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';

export const useUserInfo = () => {
  const dispatch = useDispatch<any>();
  const customer = useSelector((state: RootState) => state.customer.data);
  const {rankInfo, rankInfoLoading} = useSelector(
    (state: RootState) => state.customer,
  );

  const [avt, setAvt] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentRank, setCurrentRank] = useState<any>(null);

  useEffect(() => {
    if (customer) {
      setAvt(customer.AvatarUrl);
    }
  }, [customer]);

  useEffect(() => {
    if (customer && rankInfo && !rankInfoLoading) {
      setCurrentRank(rankInfo);
    }
  }, [customer, rankInfo, rankInfoLoading]);

  const pickerImg = async () => {
    try {
      const img = await ImagePicker.openPicker({
        multiple: false,
        cropping: true,
        cropperCircleOverlay: true,
      });

      if (img) {
        setIsLoading(true);
        const resImgs = await BaseDA.uploadFiles([
          {
            uri: img.path,
            type: img.mime,
            name: img.filename ?? 'new_file_img',
          },
        ]);

        if (resImgs && resImgs.length > 0) {
          await dispatch(
            CustomerActions.edit({
              ...customer,
              AvatarUrl: resImgs[0].Id,
              RanksData: undefined,
            }),
          );
          setAvt(resImgs[0].Id);
          showSnackbar({
            message: 'Cập nhật ảnh đại diện thành công',
            status: ComponentStatus.SUCCSESS,
            bottom: 60,
          });
          dispatch(CustomerActions.getInfor());
        }
      }
    } catch (error) {
      console.error('Image picker or upload failed:', error);
      showSnackbar({
        message: 'Cập nhật ảnh đại diện thất bại',
        status: ComponentStatus.ERROR,
        bottom: 60,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    customer,
    avt,
    isLoading,
    currentRank,
    pickerImg,
  };
};
