import {useState, useEffect} from 'react';
import {useRoute} from '@react-navigation/native';

interface UseOrderTypeReturn {
  typeCard: string;
  setTypeCard: (type: string) => void;
}

export const useOrderType = (): UseOrderTypeReturn => {
  const route = useRoute<any>();
  const [typeCard, setTypeCard] = useState<string>('');

  useEffect(() => {
    if (route?.params?.type) {
      setTypeCard(route.params.type);
    }
  }, [route?.params?.type]);

  return {
    typeCard,
    setTypeCard,
  };
};
