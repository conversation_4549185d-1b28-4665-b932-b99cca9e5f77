import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
} from 'react-native';
import { AppSvg } from 'wini-mobile-components';
import { ColorThemes } from '../../../../assets/skin/colors';
import { TypoSkin } from '../../../../assets/skin/typography';
import { Ultis } from '../../../../utils/Utils';
import iconSvg from '../../../../svg/icon';

interface PointsDisplayProps {
  totalPoints: number;
  loadingData: boolean;
}

const PointsDisplay: React.FC<PointsDisplayProps> = ({
  totalPoints,
  loadingData,
}) => {
  return (
    <View style={styles.pointsContainer}>
      <ImageBackground
        source={require('../../../../assets/Line-cate.png')}
        imageStyle={{ resizeMode: 'contain' }}
        style={styles.pointsBackground}>
        <View style={styles.pointsContent}>
          <View style={styles.pointsValueContainer}>
            <AppSvg SvgSrc={iconSvg.moneyIcon} size={28} />
            {loadingData ? (
              <Text style={styles.pointsValue}>Đang tải...</Text>
            ) : (
              <Text style={styles.pointsValue}>
                {Ultis.money(totalPoints)}
              </Text>
            )}
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  pointsContainer: {
    marginHorizontal: 16,
    marginVertical: 16,
  },
  pointsBackground: {
    paddingVertical: 24,
    paddingHorizontal: 20,
  },
  pointsContent: {
    alignItems: 'center',
  },
  pointsValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  pointsValue: {
    ...TypoSkin.heading3,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '700',
    marginRight: 20,
  },
});

export default PointsDisplay;
