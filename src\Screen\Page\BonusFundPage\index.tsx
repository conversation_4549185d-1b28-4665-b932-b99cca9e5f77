import React, {useRef, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {FLoading, Winicon} from 'wini-mobile-components';
import CustomBottomSheet, {
  CustomBottomSheetRef,
} from '../../../components/CustomBottomSheet';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {InforHeader} from '../../Layout/headers/inforHeader';
import {useBonusFund} from './hooks/useBonusFund';
import {useMonthFilter} from './hooks/useMonthFilter';
import MonthPickerBottomSheet from './components/MonthPickerBottomSheet';
import PointsDisplay from './components/PointsDisplay';
import InfoSection from './components/InfoSection';
import ActionSection from './components/ActionSection';
import TransactionList from './components/TransactionList';
import ErrorBoundary from './components/ErrorBoundary';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import BasePopupConfirm from '../../../components/Popup/BasePopupConfirm';

const BonusFundPage = () => {
  // Ref cho bottom sheet
  const bottomSheetRef = useRef<CustomBottomSheetRef>(null);

  // Lấy thông tin customer từ Redux store
  const customer = useSelectorCustomerState().data;

  const {
    isLoading,
    transactions,
    rewardFund,
    loadingData,
    fetchTransactions,
    fetchRewardFund,
    getTotalPoints,
    getTotalTransactionCount,
    handleStartBonus,
    showConfirmPopup,
    confirmLoading,
    handleCancelConfirm,
    handleConfirmShare,
  } = useBonusFund();

  const {
    selectedMonth,
    selectedYear,
    dateStart,
    dateEnd,
    setSelectedMonth,
    getMonthDateRange,
    generateMonthList,
  } = useMonthFilter();

  // Lấy thông tin ngày tháng từ hook
  const {startDate, endDate, monthName, isCurrentMonth} = getMonthDateRange();

  useEffect(() => {
    if (selectedMonth && dateStart && dateEnd) {
      fetchTransactions(dateStart, dateEnd);
      fetchRewardFund(selectedMonth, selectedYear);
    }
  }, [
    selectedMonth,
    selectedYear,
    dateStart,
    dateEnd,
    fetchTransactions,
    fetchRewardFund,
  ]);

  useEffect(() => {
    const {startDate: defaultStart, endDate: defaultEnd} = getMonthDateRange();
    const [startDay, startMonth, startYear] = defaultStart.split('/');
    const [endDay, endMonth, endYear] = defaultEnd.split('/');

    const startTimestamp = new Date(
      parseInt(startYear),
      parseInt(startMonth) - 1,
      parseInt(startDay),
      0,
      0,
      0,
      0,
    ).getTime();

    const endTimestamp = new Date(
      parseInt(endYear),
      parseInt(endMonth) - 1,
      parseInt(endDay),
      23,
      59,
      59,
      999,
    ).getTime();

    fetchTransactions(startTimestamp, endTimestamp);

    const currentDate = new Date();
    fetchRewardFund(currentDate.getMonth() + 1, currentDate.getFullYear());
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Xử lý chọn tháng
  const handleSelectMonth = (month: number) => {
    setSelectedMonth(month);
    bottomSheetRef.current?.hide();
  };

  // Hiển thị bottom sheet chọn tháng
  const showMonthPicker = () => {
    bottomSheetRef.current?.show(
      <MonthPickerBottomSheet
        months={generateMonthList()}
        selectedMonth={selectedMonth}
        onSelectMonth={handleSelectMonth}
      />,
    );
  };

  // Wrapper function để truyền các tham số cần thiết
  const onStartBonus = () => {
    if (!customer?.Id) {
      console.error('Customer ID not found');
      return;
    }
    if (!selectedMonth || !selectedYear) {
      console.error('Selected month or year not found');
      return;
    }
    handleStartBonus(selectedMonth, selectedYear, customer.Id);
  };

  return (
    <ErrorBoundary>
      <FLoading visible={isLoading || confirmLoading} />
      <View style={styles.container}>
        <CustomBottomSheet
          ref={bottomSheetRef}
          title="Chọn tháng"
          enableDismiss={true}
        />
        <InforHeader
          title="Quỹ thưởng"
          showAction={true}
          customActions={
            <TouchableOpacity
              style={styles.filterButton}
              onPress={showMonthPicker}>
              <Winicon
                src="outline/user interface/filter"
                size={20}
                color={ColorThemes.light.neutral_text_title_color}
              />
            </TouchableOpacity>
          }
        />

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            <Text style={styles.headerTitle}>Quỹ thưởng tháng {monthName}</Text>
            <Text style={styles.headerSubtitle}>
              {isCurrentMonth
                ? `Từ ${startDate} đến ${endDate}`
                : `Tháng trước: ${startDate} đến ${endDate}`}
            </Text>
          </View>

          {/* Points Display */}
          <PointsDisplay
            totalPoints={getTotalPoints()}
            loadingData={loadingData}
          />

          {/* Info Section */}
          <InfoSection
            startDate={startDate}
            endDate={endDate}
            loadingData={loadingData}
            rewardFund={rewardFund}
            totalTransactionCount={getTotalTransactionCount()}
          />

          {/* Action Button */}
          <ActionSection
            isLoading={isLoading}
            onStartBonus={onStartBonus}
            selectedMonth={selectedMonth}
            selectedYear={selectedYear}
            rewardFund={rewardFund}
          />

          {/* Transaction List */}
          <TransactionList transactions={transactions} loading={loadingData} />
        </ScrollView>

        {/* Popup Confirm cho chia thưởng */}
        <BasePopupConfirm
          visible={showConfirmPopup}
          loading={confirmLoading}
          title="Xác nhận chia thưởng"
          message="Việc chia thưởng sẽ mất chút thời gian. Vui lòng kiên nhẫn chờ đợi."
          onCancel={handleCancelConfirm}
          onConfirm={handleConfirmShare}
          cancelText="Hủy"
          confirmText="Xác nhận"
        />
      </View>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  headerSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
  },
  headerTitle: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  headerSubtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  // Styles cho filter button
  filterButton: {
    marginRight: 6,
  },
});

export default BonusFundPage;
