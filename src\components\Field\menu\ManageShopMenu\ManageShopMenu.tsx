import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {TypoSkin} from '../../../../assets/skin/typography';
import {ColorThemes} from '../../../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';
import React from 'react';

interface MenuNotActionProps {
  title: string;
  svgIcon: string;
  setSelect: (value: string) => void;
  selected: boolean;
  type: string;
  numberOrder: number;
}

const NotificationBadge = ({count}: {count: number}) => {
  if (!count || count === 0) {
    return null;
  }
  return (
    <View style={styles.badgeContainer}>
      <Text style={styles.badgeText}>{count}</Text>
    </View>
  );
};

export const MenuNotAction = (props: MenuNotActionProps) => {
  const {title, svgIcon, setSelect, type, selected, numberOrder} = props;

  const handlePress = () => {
    if (setSelect) {
      setSelect(type);
    }
  };

  const textStyle = selected ? styles.activeTabText : styles.inactiveTabText;
  const wrapperProps = selected
    ? {
        start: {x: 0, y: 0},
        end: {x: 1, y: 0},
        colors: ['#90C8FB', '#8DC4F7', '#B6F5FE'],
        borderRadius: 10,
      }
    : {
        borderRadius: 10,
      };

  return (
    <TouchableOpacity style={styles.tabContainer} onPress={handlePress}>
      {selected ? (
        <LinearGradient
          {...(wrapperProps as React.ComponentProps<typeof LinearGradient>)}>
          <View style={styles.tabContent}>
            <AppSvg SvgSrc={svgIcon} size={20} />
            <Text style={[styles.tabText, textStyle]}>{title}</Text>
            {title === 'Shop' && <NotificationBadge count={numberOrder} />}
          </View>
        </LinearGradient>
      ) : (
        <View style={styles.tabContent}>
          <AppSvg SvgSrc={svgIcon} size={20} />
          <Text style={[styles.tabText, textStyle]}>{title}</Text>
          {title === 'Shop' && <NotificationBadge count={numberOrder} />}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    borderRadius: 10,
    justifyContent: 'center',
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignContent: 'center',
    gap: 4,
    paddingVertical: 5,
    paddingHorizontal: 10,
  },
  tabText: {
    ...TypoSkin.title3,
  },
  activeTabText: {
    color: ColorThemes.light.primary_main_color,
  },
  inactiveTabText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  badgeContainer: {
    position: 'absolute',
    left: 2,
    top: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
  },
});
