import {ConfigRewardRank} from 'redux/types/configRewardRank';
import {DataController} from '../../../base/baseController';

class ConfigRewardRankDa {
  private controller: DataController;
  constructor() {
    this.controller = new DataController('ConfigRewardRank');
  }

  async fetchConfigRewardRank({
    type,
  }: {
    type: number;
  }): Promise<ConfigRewardRank[]> {
    const config = {
      query: `@Type: [${type}]`,
      page: 1,
      size: 9999,
    };
    const res = await this.controller.getListSimple(config);
    if (res.code === 200) {
      return res.data || [];
    }
    return [];
  }
}

export default ConfigRewardRankDa;
