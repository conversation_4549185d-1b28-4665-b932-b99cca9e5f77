import React from 'react';
import {View, StyleSheet} from 'react-native';
import MenuOrders from '../../setting/menuOrders';
import iconSvg from '../../../../svg/icon';
import {RootScreen} from '../../../../router/router';
import {StatusOrder} from '../../../../Config/Contanst';

const OrderSummary = ({orderDetail}: {orderDetail: any[]}) => {
  const getBadgeOrder = (status: string) => {
    return orderDetail?.filter(item => item.Status === status).length;
  };

  return (
    <View style={styles.container}>
      <MenuOrders
        svgIcon={iconSvg.walletAction}
        title="Đơn hàng mới"
        getBadgeOrder={getBadgeOrder(StatusOrder.new.toString())}
        orderRoute={RootScreen.OrderCustomerDetail}
        status={StatusOrder.new}
      />
      <MenuOrders
        svgIcon={iconSvg.deliveryIcon}
        title="Đang xử lý"
        getBadgeOrder={getBadgeOrder(StatusOrder.proccess.toString())}
        orderRoute={RootScreen.OrderCustomerDetail}
        status={StatusOrder.proccess}
      />
      <MenuOrders
        svgIcon={iconSvg.done}
        title="Hoàn thành"
        orderRoute={RootScreen.OrderCustomerDetail}
        status={StatusOrder.success}
      />
      <MenuOrders
        svgIcon={iconSvg.cancel}
        title="Hủy/hoàn"
        // getBadgeOrder={getBadgeOrder(StatusOrder.cancel.toString())}
        orderRoute={RootScreen.OrderCustomerDetail}
        status={StatusOrder.cancel}
      />
      <MenuOrders
        svgIcon={iconSvg.star}
        title="Đánh giá"
        orderRoute={RootScreen.RatingForAllScreen}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 8,
    marginBottom: 8,
  },
});

export default OrderSummary;
