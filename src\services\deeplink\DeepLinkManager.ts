/**
 * Deep Link Manager
 * Centralized service for handling all deeplink operations
 */

import {Linking, Platform, AppState, AppStateStatus} from 'react-native';
import {NavigationContainerRef} from '@react-navigation/native';
import {RootStackParamList} from '../../Screen/Layout/navigation/ecomNavigator';
import {RootScreen} from '../../router/router';
import {saveDataToAsyncStorage} from '../../utils/AsyncStorage';
import {
  DeepLinkUrl,
  DeepLinkManagerConfig,
  DeepLinkNavigationResult,
  DeepLinkEvent,
  DeepLinkEventListener,
  DEFAULT_DEEPLINK_CONFIG,
  ParsedDeepLink,
} from './DeepLinkTypes';
import {parseDeepLink, validateDeepLink, hasValidId} from './DeepLinkUtils';

class DeepLinkManager {
  private static instance: DeepLinkManager;
  private navigationRef: NavigationContainerRef<RootStackParamList> | null =
    null;
  private config: DeepLinkManagerConfig = DEFAULT_DEEPLINK_CONFIG;
  private listeners: Map<DeepLinkEvent, DeepLinkEventListener[]> = new Map();
  private isInitialized = false;
  private pendingUrl: string | null = null;
  private linkingSubscription: any = null;
  private appStateSubscription: any = null;

  private constructor() {
    this.initializeEventListeners();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): DeepLinkManager {
    if (!DeepLinkManager.instance) {
      DeepLinkManager.instance = new DeepLinkManager();
    }
    return DeepLinkManager.instance;
  }

  /**
   * Initialize the DeepLink Manager
   */
  public initialize(
    navigationRef: NavigationContainerRef<RootStackParamList>,
    config?: Partial<DeepLinkManagerConfig>,
  ): void {
    this.navigationRef = navigationRef;
    this.config = {...DEFAULT_DEEPLINK_CONFIG, ...config};
    this.isInitialized = true;

    this.log('DeepLinkManager initialized');

    // Setup URL listeners
    this.setupLinkingListeners();

    // Check for initial URL
    this.checkInitialUrl();

    // Process any pending URL
    if (this.pendingUrl) {
      this.handleDeepLink(this.pendingUrl);
      this.pendingUrl = null;
    }
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    if (this.linkingSubscription) {
      this.linkingSubscription.remove();
      this.linkingSubscription = null;
    }

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    this.isInitialized = false;
    this.navigationRef = null;
    this.pendingUrl = null;
    this.listeners.clear();

    this.log('DeepLinkManager cleaned up');
  }

  /**
   * Handle a deeplink URL
   */
  public async handleDeepLink(
    url: DeepLinkUrl,
  ): Promise<DeepLinkNavigationResult> {
    this.log(`Handling deeplink: ${url}`);
    this.emitEvent('url_received', {url});

    // If not initialized, store for later
    if (!this.isInitialized || !this.navigationRef) {
      this.log('Manager not ready, storing URL for later');
      this.pendingUrl = url;
      return {success: false, error: 'Manager not initialized'};
    }

    // Validate URL
    const validation = validateDeepLink(url);
    if (!validation.isValid) {
      this.log(`Invalid URL: ${validation.error}`);
      this.emitEvent('parsing_error', {url, error: validation.error});
      return this.navigateToFallback();
    }

    // Parse URL
    const parseResult = parseDeepLink(url);
    if (!parseResult.success || !parseResult.data) {
      this.log(`Parsing failed: ${parseResult.error}`);
      this.emitEvent('parsing_error', {url, error: parseResult.error});
      return this.navigateToFallback();
    }

    // Navigate to screen
    return this.navigateToScreen(parseResult.data);
  }

  /**
   * Navigate to a screen based on parsed deeplink data
   */
  private async navigateToScreen(
    parsed: ParsedDeepLink,
  ): Promise<DeepLinkNavigationResult> {
    if (!this.navigationRef) {
      return {success: false, error: 'Navigation ref not available'};
    }

    try {
      // Handle special cases
      if (parsed.type === 'share') {
        await this.handleShareLink(parsed);
        return {success: true, screen: RootScreen.navigateEComView};
      }

      // Validate ID for screens that require it
      if (parsed.id && !hasValidId(parsed)) {
        this.log(`Invalid ID for ${parsed.type}: ${parsed.id}`);
        return this.navigateToFallback();
      }

      // Navigate with retry logic
      return this.navigateWithRetry(parsed.screen, parsed.params);
    } catch (error) {
      this.log(`Navigation error: ${error}`);
      this.emitEvent('navigation_error', {parsed, error});
      return this.navigateToFallback();
    }
  }

  /**
   * Handle share link (QR code sharing)
   */
  private async handleShareLink(parsed: ParsedDeepLink): Promise<void> {
    try {
      const refCode = parsed.id || '';
      if (refCode) {
        await saveDataToAsyncStorage('RefCodeShared', refCode);
        this.log(`RefCodeShared saved: ${refCode}`);
      } else {
        this.log('No reference code found in share link');
      }
    } catch (error) {
      this.log(`Error saving share link info: ${error}`);
    }
  }

  /**
   * Navigate with retry logic
   */
  private async navigateWithRetry(
    screen: RootScreen,
    params?: any,
    attempt = 1,
  ): Promise<DeepLinkNavigationResult> {
    try {
      if (!this.navigationRef) {
        throw new Error('Navigation ref not available');
      }

      // Use reset navigation for better UX
      this.navigationRef.reset({
        index: 1,
        routes: [
          {name: RootScreen.navigateEComView as any},
          {name: screen as any, params},
        ],
      });

      this.log(`Successfully navigated to ${screen}`);
      this.emitEvent('navigation_success', {screen, params});
      return {success: true, screen};
    } catch (error) {
      this.log(`Navigation attempt ${attempt} failed: ${error}`);

      if (attempt < this.config.retryAttempts) {
        this.log(`Retrying navigation in ${this.config.retryDelay}ms...`);
        await this.delay(this.config.retryDelay);
        return this.navigateWithRetry(screen, params, attempt + 1);
      }

      this.emitEvent('navigation_error', {screen, params, error});
      return this.navigateToFallback();
    }
  }

  /**
   * Navigate to fallback screen
   */
  private navigateToFallback(): DeepLinkNavigationResult {
    try {
      if (this.navigationRef) {
        this.navigationRef.navigate(this.config.fallbackScreen as any);
        this.log(`Navigated to fallback screen: ${this.config.fallbackScreen}`);
        return {success: true, screen: this.config.fallbackScreen};
      }
      return {success: false, error: 'Navigation ref not available'};
    } catch (error) {
      this.log(`Fallback navigation failed: ${error}`);
      return {success: false, error: `Fallback navigation failed: ${error}`};
    }
  }

  /**
   * Setup linking event listeners
   */
  private setupLinkingListeners(): void {
    try {
      // Modern addEventListener (React Native 0.65+)
      this.linkingSubscription = Linking.addEventListener('url', event => {
        this.log(`URL event received: ${event.url}`);
        this.handleDeepLink(event.url);
      });

      this.log('Linking listeners setup successfully');
    } catch (error) {
      this.log(`Failed to setup linking listeners: ${error}`);

      // Try legacy method as fallback
      try {
        this.log('Trying legacy Linking.addListener...');
        // @ts-ignore - Legacy method
        this.linkingSubscription = Linking.addListener('url', event => {
          this.log(`URL event received (legacy): ${event.url}`);
          this.handleDeepLink(event.url);
        });
        this.log('Legacy Linking listener setup successfully');
      } catch (legacyError) {
        this.log(`Both addEventListener methods failed: ${legacyError}`);
      }
    }
  }

  /**
   * Initialize event listeners for app state changes
   */
  private initializeEventListeners(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange,
    );
  }

  /**
   * Handle app state changes
   */
  private handleAppStateChange = (nextAppState: AppStateStatus): void => {
    if (nextAppState === 'active') {
      this.log('App became active, checking for initial URL');
      // Small delay to ensure app is fully active
      setTimeout(() => {
        this.checkInitialUrl();
      }, 100);
    }
  };

  /**
   * Check for initial URL when app starts
   */
  private async checkInitialUrl(): Promise<void> {
    try {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        this.log(`Initial URL found: ${initialUrl}`);
        // Add a small delay to ensure navigation is ready
        setTimeout(() => {
          this.handleDeepLink(initialUrl);
        }, 100);
      } else {
        this.log('No initial URL found');
      }
    } catch (error) {
      this.log(`Error getting initial URL: ${error}`);
    }
  }

  /**
   * Add event listener
   */
  public addEventListener(
    event: DeepLinkEvent,
    listener: DeepLinkEventListener,
  ): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  /**
   * Remove event listener
   */
  public removeEventListener(
    event: DeepLinkEvent,
    listener: DeepLinkEventListener,
  ): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emitEvent(event: DeepLinkEvent, data?: any): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(event, data);
        } catch (error) {
          this.log(`Error in event listener: ${error}`);
        }
      });
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Logging utility
   */
  private log(message: string): void {
    if (this.config.enableLogging) {
      console.log(`[DeepLinkManager] ${message}`);
    }
  }
}

export default DeepLinkManager;
