import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';
import {StatusOption} from '../hooks/useOrderStatusUpdate';

interface OrderStatusListProps {
  statusOptions: StatusOption[];
  selectedStatus: string | null;
  onStatusSelect: (statusType: string) => void;
}

const OrderStatusList: React.FC<OrderStatusListProps> = ({
  statusOptions,
  selectedStatus,
  onStatusSelect,
}) => {
  return (
    <View style={styles.statusList}>
      {statusOptions.map(status => (
        <TouchableOpacity
          key={status.id}
          style={[
            styles.statusOption,
            selectedStatus === status.type && styles.selectedStatus,
          ]}
          onPress={() => onStatusSelect(status.type)}
          accessibilityLabel={`Chọn trạng thái ${status.name}`}
          accessibilityRole="button"
          accessibilityState={{selected: selectedStatus === status.type}}>
          <View style={styles.statusOptionContent}>
            <View
              style={[
                styles.statusIndicator,
                selectedStatus === status.type && styles.selectedIndicator,
              ]}
            />
            <Text
              style={[
                styles.statusOptionText,
                selectedStatus === status.type && styles.selectedStatusText,
              ]}>
              {status.name}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  statusList: {
    width: '100%',
    marginBottom: 28,
    marginTop: 20,
  },
  statusOption: {
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderWidth: 2,
    borderColor: '#f0f0f0',
    borderRadius: 16,
    marginBottom: 12,
    backgroundColor: '#fafbfc',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    position: 'relative',
    overflow: 'hidden',
  },
  selectedStatus: {
    backgroundColor: '#fff8e1',
    borderColor: '#ffc107',
    borderWidth: 2,
    shadowColor: '#ffc107',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
    transform: [{scale: 1.02}],
  },
  statusOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 16,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    backgroundColor: 'transparent',
  },
  selectedIndicator: {
    backgroundColor: '#ffc107',
    borderColor: '#ffc107',
    shadowColor: '#ffc107',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  statusOptionText: {
    ...TypoSkin.title3,
    color: '#333333',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 20,
  },
  selectedStatusText: {
    color: '#e65100',
    fontWeight: '700',
  },
});

export default OrderStatusList;
