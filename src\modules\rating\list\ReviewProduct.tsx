import React, {useEffect, useState, useCallback} from 'react';
import {FlatList} from 'react-native-gesture-handler';
import {TypeMenuReview} from '../../../Config/Contanst';
import {DataController} from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import {ReviewDataDto, ReviewProductProps} from '../types';
import {View, RefreshControl, ActivityIndicator} from 'react-native';
import ReviewProductIteCard from '../card/CardReviewItem';
import {OrderDA} from '../../order/orderDA';
import ConfigAPI from '../../../Config/ConfigAPI';
import {ColorThemes} from '../../../assets/skin/colors';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

const ReviewProduct = (props: ReviewProductProps) => {
  const {type} = props;
  const [data, setData] = useState<ReviewDataDto[] | any[]>([]);
  const RatingController = new DataController('Rating');
  const shopInfo = useSelectorShopState().data;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [hasMoreData, setHasMoreData] = useState<boolean>(true);
  const orderDA = new OrderDA();
  const PAGE_SIZE = 10;
  useEffect(() => {
    setIsLoading(true);
    setPage(1);
    setHasMoreData(true);
    if (type == TypeMenuReview.Product) {
      callApiGetReview(1, false);
    } else {
      callApiGetReview(1, false);
    }
  }, [type]);

  let callApiGetReview = async (pageNumber: number, isLoadMore: boolean) => {
    if (isLoadMore) {
      setIsLoadingMore(true);
    } else {
      setIsLoading(true);
    }

    let resRating = await RatingController.getPatternList({
      page: pageNumber,
      size: PAGE_SIZE,
      query: `@ShopId: {${shopInfo[0].Id}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
        ProductId: ['Id', 'Name', 'Description', 'Img', 'Content'],
        OrderId: ['Id', 'Code'],
      },
    });
    if (resRating && resRating.code === 200) {
      let arrayOrderId = resRating?.data?.map((item: any) => item.OrderId);
      let getRefund = await orderDA.getAllHistoryRewardByListId(arrayOrderId);
      let arrayData = resRating?.data?.map((item: any) => {
        return {
          ...item,
          Customer: resRating?.Customer?.find(
            (customer: any) => customer.Id == item.CustomerId,
          ),
          Order: resRating?.Order?.find(
            (order: any) => order.Id == item.OrderId,
          ),
          Product: resRating?.Product?.find(
            (product: any) => product.Id == item.ProductId,
          )
            ? {
                ...resRating?.Product?.find(
                  (product: any) => product.Id == item.ProductId,
                ),
                Img:
                  ConfigAPI.urlImg +
                  resRating?.Product?.find(
                    (product: any) => product.Id == item.ProductId,
                  ).Img,
              }
            : null,
          Refund:
            getRefund && Array.isArray(getRefund)
              ? (
                  getRefund?.find(
                    (refund: any) => refund.OrderId == item.OrderId,
                  ) as any
                )?.value || 0
              : 0,
        };
      });

      // Handle pagination data
      if (isLoadMore) {
        setData(prevData => [...prevData, ...arrayData]);
        setPage(pageNumber);
      } else {
        setData(arrayData);
        setPage(pageNumber);
      }

      // Check if there's more data
      if (arrayData.length < PAGE_SIZE) {
        setHasMoreData(false);
      }

      setIsLoading(false);
      setIsLoadingMore(false);
    } else {
      setIsLoading(false);
      setIsLoadingMore(false);
      setHasMoreData(false);
    }
  };

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    setPage(1);
    setHasMoreData(true);
    if (type == TypeMenuReview.Product) {
      callApiGetReview(1, false);
    } else {
      callApiGetReview(1, false);
    }
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  }, [type, shopInfo]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && hasMoreData && !isLoading) {
      const nextPage = page + 1;
      if (type == TypeMenuReview.Product) {
        callApiGetReview(nextPage, true);
      } else {
        callApiGetReview(nextPage, true);
      }
    }
  }, [isLoadingMore, hasMoreData, isLoading, page, type, shopInfo]);

  // Render footer component for load more indicator
  const renderFooter = () => {
    if (isLoadingMore) {
      return (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            paddingVertical: 20,
          }}>
          <ActivityIndicator
            color={ColorThemes.light.primary_main_color}
            size="small"
          />
        </View>
      );
    }
    return null;
  };

  // Skeleton component for ReviewProductIteCard
  const ReviewProductSkeleton = () => {
    return (
      <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
        <View
          style={{
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
            marginHorizontal: 16,
            marginVertical: 8,
            borderRadius: 8,
            padding: 12,
          }}>
          {/* Header with avatar and user info */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'flex-start',
              marginBottom: 12,
            }}>
            {/* Avatar */}
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                marginRight: 12,
                marginTop: 8,
              }}
            />

            {/* User info and rating */}
            <View style={{flex: 1}}>
              {/* User name */}
              <View
                style={{
                  width: 120,
                  height: 16,
                  borderRadius: 4,
                  marginBottom: 8,
                }}
              />

              {/* Rating stars */}
              <View style={{flexDirection: 'row', marginBottom: 12}}>
                {[1, 2, 3, 4, 5].map((_, index) => (
                  <View
                    key={index}
                    style={{
                      width: 20,
                      height: 20,
                      borderRadius: 10,
                      marginRight: 4,
                    }}
                  />
                ))}
              </View>
            </View>
          </View>

          {/* Review content */}
          <View style={{marginBottom: 12}}>
            {/* Comment lines */}
            <View
              style={{
                width: '100%',
                height: 16,
                borderRadius: 4,
                marginBottom: 6,
              }}
            />
            <View
              style={{
                width: '80%',
                height: 16,
                borderRadius: 4,
                marginBottom: 12,
              }}
            />
          </View>

          {/* Product info */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 12,
            }}>
            {/* Product image */}
            <View
              style={{
                width: 56,
                height: 56,
                borderRadius: 8,
                marginRight: 12,
              }}
            />

            {/* Product details */}
            <View style={{flex: 1}}>
              {/* Product name */}
              <View
                style={{
                  width: '70%',
                  height: 16,
                  borderRadius: 4,
                  marginBottom: 6,
                }}
              />

              {/* Refund info */}
              <View
                style={{
                  width: '50%',
                  height: 14,
                  borderRadius: 4,
                }}
              />
            </View>
          </View>

          {/* Date */}
          <View
            style={{
              width: 80,
              height: 12,
              borderRadius: 4,
            }}
          />
        </View>
      </SkeletonPlaceholder>
    );
  };

  return (
    <FlatList
      data={isLoading && data.length === 0 ? [] : data}
      style={{flex: 1}}
      keyExtractor={(item, i) => `${i} ${item.Id}`}
      renderItem={({item, index}) =>
        ReviewProductIteCard({item, index}, type as string)
      }
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          colors={[ColorThemes.light.primary_main_color]}
          tintColor={ColorThemes.light.primary_main_color}
        />
      }
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      ListHeaderComponent={() => {
        // Show skeletons when refreshing with existing data
        if (isLoading && data.length > 0) {
          return (
            <View>
              {[1, 2, 3].map((_, index) => (
                <ReviewProductSkeleton key={`header-skeleton-${index}`} />
              ))}
            </View>
          );
        }
        return null;
      }}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      ListEmptyComponent={() => {
        if (isLoading) {
          return (
            <View>
              {[1, 2, 3, 4, 5].map((_, index) => (
                <ReviewProductSkeleton key={`skeleton-${index}`} />
              ))}
            </View>
          );
        }
        return (
          <View
            style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            <EmptyPage />
          </View>
        );
      }}
    />
  );
};

export default ReviewProduct;
