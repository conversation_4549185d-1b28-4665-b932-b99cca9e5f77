import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableWithoutFeedback,
  StatusBar,
  Platform,
} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../assets/skin/colors';
import {TypoSkin} from '../assets/skin/typography';

interface CustomBottomSheetProps {
  title?: string;
  enableDismiss?: boolean;
  onDismiss?: () => void;
}

export interface CustomBottomSheetRef {
  show: (content: React.ReactNode) => void;
  hide: () => void;
}

const {height: SCREEN_HEIGHT} = Dimensions.get('window');

const CustomBottomSheet = forwardRef<
  CustomBottomSheetRef,
  CustomBottomSheetProps
>(({title, enableDismiss = true, onDismiss}, ref) => {
  const [visible, setVisible] = useState(false);
  const [content, setContent] = useState<React.ReactNode>(null);
  const [slideAnim] = useState(new Animated.Value(SCREEN_HEIGHT));
  const [opacityAnim] = useState(new Animated.Value(0));
  const insets = useSafeAreaInsets();

  useImperativeHandle(ref, () => ({
    show: (newContent: React.ReactNode) => {
      setContent(newContent);
      setVisible(true);

      // Animate in
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    },
    hide: () => {
      // Animate out
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: SCREEN_HEIGHT,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setVisible(false);
        setContent(null);
        onDismiss?.();
      });
    },
  }));

  const handleBackdropPress = () => {
    if (enableDismiss) {
      ref?.current?.hide();
    }
  };

  const handleClosePress = () => {
    ref?.current?.hide();
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={enableDismiss ? handleBackdropPress : undefined}>
      <View style={styles.modalContainer}>
        {/* Backdrop */}
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <Animated.View
            style={[
              styles.backdrop,
              {
                opacity: opacityAnim,
              },
            ]}
          />
        </TouchableWithoutFeedback>

        {/* Bottom Sheet */}
        <Animated.View
          style={[
            styles.bottomSheet,
            {
              transform: [{translateY: slideAnim}],
              paddingBottom: insets.bottom,
            },
          ]}>
          {/* Header */}
          {title && (
            <View style={styles.header}>
              <View style={styles.headerLeft} />
              <Text style={styles.title}>{title}</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={handleClosePress}
                hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
                <Winicon
                  src="outline/layout/xmark"
                  size={20}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              </TouchableOpacity>
            </View>
          )}

          {/* Content */}
          <View style={styles.content}>{content}</View>
        </Animated.View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bottomSheet: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: SCREEN_HEIGHT * 0.9,
    minHeight: 400,
    // Shadow
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  headerLeft: {
    width: 24, // Same width as close button for centering
  },
  title: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    flex: 1,
  },
  closeButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
  },
});

export default CustomBottomSheet;
