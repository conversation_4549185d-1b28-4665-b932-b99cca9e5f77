# Hướng Dẫn Test Deep Link iOS - DeepLinkListener Pattern

## 🔧 Vấn Đề Đã Sửa

### Vấn đề chính: `Linking.addEventListener` không hoạt động ổn định

- **Nguyên nhân**: Hook-based listener trong navigator có timing issues
- **Giải pháp**: Tạo `DeepLinkListener` component ở App level (pattern giống NotificationBadgeListener)

### Các cải tiến:

1. **App-level listener** - Đặt ở App.tsx như NotificationBadgeListener
2. **AppState integration** - Lắng nghe khi app active để catch URLs
3. **Better timing** - Navigation ready detection
4. **Comprehensive logging** với prefix [DeepLinkListener]
5. **Retry mechanism** cho iOS `getInitialURL()` (thử 3 lần)

## 📱 Cách Test

### 1. Qua Settings Screen (Khuyến nghị)

1. Mở app trong development mode
2. Vào Profile/Settings
3. Cuộn xuống tìm "🛠️ Development Tools"
4. <PERSON><PERSON><PERSON>n "🔗 Test Deep Links"
5. Chọn loại link muốn test

### 2. <PERSON><PERSON> Console (Development)

Trong Metro console hoặc debugger:

```javascript
// Test share link cụ thể
testDeepLink('chainivo://share/TEST123');

// Kiểm tra RefCodeShared hiện tại
checkRefCodeShared();

// Test tất cả share links
testShareLinks();

// Test tất cả deep links
testAllDeepLinks();

// Hiển thị menu test nhanh
showQuickTestMenu();
```

### 3. Test Manual

#### Share Links (Custom Scheme)

```
chainivo://share/ABC123
chainivo://share/XYZ789?ref=social&campaign=test
```

#### Share Links (Universal Links)

```
https://chainivo.com/share/UNI123
https://www.chainivo.com/share/TEST456
```

#### Other Deep Links

```
chainivo://product/12345
chainivo://posts/67890
chainivo://news/11111
chainivo://events/22222
```

### 4. Test trên iOS Simulator

1. Mở Safari trên iOS Simulator
2. Gõ deep link URL vào address bar
3. Nhấn "Open" khi được hỏi mở app
4. Kiểm tra console logs
5. Verify RefCodeShared được lưu (dùng development tools)

### 5. Test trên Physical Device

1. Gửi deep link qua Messages, Email, hoặc Notes
2. Nhấn vào link để mở app
3. Kiểm tra app mở và xử lý link đúng
4. Với share links, verify RefCodeShared được lưu

## ✅ Kết Quả Mong Đợi

### Share Links (`chainivo://share/REFCODE`)

1. App mở/hiện lên foreground
2. RefCodeShared được lưu vào AsyncStorage với ref code
3. App navigate về home screen
4. Console hiển thị logs chi tiết

### Other Deep Links

1. App mở/hiện lên foreground
2. App navigate đến screen tương ứng với parameters
3. Console hiển thị navigation details

## 🐛 Debug

### Console Logs Quan Trọng

- `🔗 [DeepLinkListener] Initialized`
- `🧭 [DeepLinkListener] Navigation is ready`
- `🎯 [DeepLinkListener] URL received: [URL]`
- `✅ [DeepLinkListener] App opened with initial URL: [URL]`
- `✅ [DeepLinkListener] RefCodeShared saved: [ref code]`

### Các Vấn Đề Thường Gặp

1. **No initial URL found**: Kiểm tra iOS configuration và URL scheme registration
2. **Navigation not ready**: Kiểm tra timing delays và navigation listener
3. **RefCodeShared not saved**: Kiểm tra AsyncStorage permissions và error logs
4. **Listener not working**: Hook sẽ tự động fallback sang polling method

## 🔧 Technical Details

### useDeepLinkFixed vs useDeepLink

- **useDeepLinkFixed**: Phiên bản đơn giản, ổn định với fallback methods
- **useDeepLink**: Phiên bản phức tạp có thể có issues với addEventListener

### Fallback Methods

1. **Modern addEventListener** (React Native 0.65+)
2. **Legacy addListener** (older versions)
3. **Polling fallback** (nếu cả 2 methods trên fail)

### iOS Specific Improvements

- **Retry mechanism**: Thử `getInitialURL()` 3 lần với delay 200ms
- **Longer delays**: 1000ms cho iOS vs 300ms cho Android
- **Better error handling**: Comprehensive logging và fallbacks

## 📋 Checklist Test

### Trước khi test:

- [ ] App đang chạy trong development mode
- [ ] Console logs visible
- [ ] iOS Simulator hoặc physical device ready

### Test Cases:

- [ ] Share link với ref code đơn giản: `chainivo://share/TEST123`
- [ ] Share link với query params: `chainivo://share/ABC?ref=social`
- [ ] Universal link: `https://chainivo.com/share/UNI123`
- [ ] Product link: `chainivo://product/12345`
- [ ] App từ closed state (kill app trước khi test)
- [ ] App từ background state
- [ ] RefCodeShared được lưu đúng

### Sau khi test:

- [ ] Check RefCodeShared trong AsyncStorage
- [ ] Verify navigation đúng screen
- [ ] Check console logs không có errors
- [ ] Test multiple times để đảm bảo consistency

## 🚀 Next Steps

Nếu deep links vẫn không hoạt động:

1. **Check console logs** để tìm error messages
2. **Verify iOS configuration** trong Info.plist
3. **Test với development tools** để isolate issue
4. **Check app registration** cho URL schemes
5. **Ensure associated domains** configured đúng cho Universal Links

## 📞 Support

Nếu cần hỗ trợ thêm:

- Check console logs và báo cáo specific errors
- Test với development tools để xác định vấn đề
- Verify iOS configuration files
- Test trên cả simulator và physical device
