import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Dimensions,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {
  showSnackbar,
  ComponentStatus,
  FLoading,
  Winicon,
  AppButton,
  ListTile,
  FDialog,
  Checkbox,
  FPopup,
  showPopup,
  closePopup,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {DataController} from '../../base/baseController';
import LocalAuthen from '../../features/local-authen/local-authen';
import {navigateReset, RootScreen} from '../../router/router';
import {
  saveDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import {randomGID, regexPassWord, Ultis} from '../../utils/Utils';
import {validatePhoneNumber} from '../../utils/validate';
import {
  FAddressPickerForm,
  TextFieldForm,
} from '../Default/form/component-form';
import {useForm} from 'react-hook-form';
import GoogleLogin, {
  webClientId,
} from '../../features/socials-login/GoogleSignIn/GoogleSignIn';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {CustomerStatus, StorageContanst} from '../../Config/Contanst';
import AuthSocketService from '../../services/AuthSocketService';
// import ConfigAPI from '../../Config/ConfigAPI';
// import { decrypt, encrypt } from '../../utils/CryptoUtils';
// import { de } from 'date-fns/locale';
import AppleSignIn from '../../features/socials-login/AppleSignIn/apple-sign-in';
import store from '../../redux/store/store';
import {PopupQrcodeScan} from '../../features/qrcode-scanner/qrcode-scan';
import {SafeAreaView} from 'react-native-safe-area-context';
import {SvgXml} from 'react-native-svg';
import ConfigAPI from '../../Config/ConfigAPI';
import {CustomerDA} from './da';
import {useTranslation} from '../../locales/useTranslation';
import LanguageSwitcher from '../../components/LanguageSwitcher';
import {useLanguage} from '../../locales/languageContext';

const scanner = `<svg clip-rule="evenodd" fill-rule="evenodd" height="512" stroke-linejoin="round" stroke-miterlimit="2" viewBox="0 0 32 32" width="512" xmlns="http://www.w3.org/2000/svg"><g transform="translate(-48)"><path d="m68 20h6v6h-6z" fill="#95ecff"/><g transform="translate(-14)"><path d="m68 20h6v6h-6z" fill="#00bce5"/></g><g transform="translate(-14 -14)"><path d="m68 20h6v6h-6z" fill="#00bce5"/></g><path d="m70 31h6c1.657 0 3-1.343 3-3v-6c0-.552-.448-1-1-1s-1 .448-1 1v6c0 .552-.448 1-1 1h-6c-.552 0-1 .448-1 1s.448 1 1 1zm-12-2h-6c-.552 0-1-.448-1-1v-6c0-.552-.448-1-1-1s-1 .448-1 1v6c0 1.657 1.343 3 3 3h6c.552 0 1-.448 1-1s-.448-1-1-1zm5-9v6c0 .552.448 1 1 1s1-.448 1-1v-6c0-.552-.448-1-1-1s-1 .448-1 1zm-2 0c0-.552-.448-1-1-1h-6c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h6c.552 0 1-.448 1-1zm14 0c0-.552-.448-1-1-1h-6c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h6c.552 0 1-.448 1-1zm-16 1v4h-4v-4zm14 0v4h-4v-4zm-10-4h1c.552 0 1-.448 1-1s-.448-1-1-1h-1c-.552 0-1 .448-1 1s.448 1 1 1zm-9 0h5c.552 0 1-.448 1-1s-.448-1-1-1h-5c-.552 0-1 .448-1 1s.448 1 1 1zm18-2h1v1c0 .552.448 1 1 1s1-.448 1-1v-2c0-.552-.448-1-1-1h-2c-.552 0-1 .448-1 1s.448 1 1 1zm-3 1v-5h1c.552 0 1-.448 1-1s-.448-1-1-1h-2c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1s1-.448 1-1zm-6-6v2c0 .552.448 1 1 1s1-.448 1-1v-2c0-.552-.448-1-1-1s-1 .448-1 1zm-2-4c0-.552-.448-1-1-1h-6c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h6c.552 0 1-.448 1-1zm-3-5h-6c-1.657 0-3 1.343-3 3v6c0 .552.448 1 1 1s1-.448 1-1v-6c0-.552.448-1 1-1h6c.552 0 1-.448 1-1s-.448-1-1-1zm1 6v4h-4v-4zm11-4h6c.552 0 1 .448 1 1v6c0 .552.448 1 1 1s1-.448 1-1v-6c0-1.657-1.343-3-3-3h-6c-.552 0-1 .448-1 1s.448 1 1 1zm-6 4h9v3c0 .552.448 1 1 1s1-.448 1-1v-4c0-.552-.448-1-1-1h-10c-.552 0-1 .448-1 1s.448 1 1 1z" fill="#1b4a99"/></g></svg>`;

export default function LoginScreen() {
  const methods = useForm({shouldFocusError: false});
  const navigation = useNavigation<any>();
  const {t} = useTranslation();
  const dispatch = useDispatch<any>();
  const [isSignUp, setSignUp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingG, setLoadingG] = useState(false);
  const [loadingA, setLoadingA] = useState(false);
  const [bio, setBio] = useState<any>('');
  const customerController = new DataController('Customer');
  const popupRef = useRef<any>(null);

  useEffect(() => {
    // check local storage
    getDataToAsyncStorage('RefCodeShared').then(rs => {
      if (rs) {
        // for deep link
        methods.setValue('RefCode', rs);
      }
    });
  }, [methods]);

  useEffect(() => {
    getDataToAsyncStorage('Mobile').then(result => {
      methods.setValue('LastMobile', result);
      methods.setValue('Mobile', result);
    });
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Biometrics').then(result => {
          if (result) {
            setBio(result);
          }
        });
      }
    });
    getDataToAsyncStorage('RememberPass').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Password').then(result => {
          if (result) {
            methods.setValue('Password', result);
          }
        });
      }
    });
  }, []);

  const _loginSocial = async (value: any, type: any) => {
    if (value) {
      console.log('social login success', value);
      let isFirstTime = false;
      setLoading(true);
      if (value?.user?.email && type === 'google') {
        const resEmail = await customerController.getListSimple({
          page: 1,
          size: 1,
          query: `@Email:("${value?.user?.email}")`,
        });
        if (resEmail.code == 200 && resEmail.data.length == 0) {
          isFirstTime = true;
          await saveDataToAsyncStorage('Email', value?.user?.email);
        }
      } else if (value?.email && type === 'apple') {
        const resEmail = await customerController.getListSimple({
          page: 1,
          size: 1,
          query: `@Email:("${value?.email}")`,
        });
        if (resEmail.code == 200 && resEmail.data.length == 0) {
          isFirstTime = true;
          await saveDataToAsyncStorage('Email', value?.email);
        }
      }

      const res = await CustomerActions.login({
        type: type,
        ggClientId: webClientId,
        token: type === 'google' ? value.idToken : value.identityToken,
      });
      if (res?.code === 200) {
        //tạo ví
        // const customerController = new DataController('Customer');
        // try {
        //   const resultWallet = await CustomerActions.createWallet(
        //     res.data.user.Mobile ?? res.data.user.Email,
        //   );
        //   if(resultWallet && resultWallet.address && resultWallet.privateKey){
        //     await customerController.edit([
        //       {
        //         Id: res.data.user.Id,
        //         WalletAddress: resultWallet.address,
        //         PrivateKey: resultWallet.privateKey,
        //       },
        //     ]);
        //     console.log('Wallet created successfully for user:', res.data.user.Mobile);
        //   } else {
        //     console.warn('Failed to create wallet for user:', res.data.user.Mobile, 'but login continues');
        //   }
        // } catch (walletError) {
        //   console.error('Wallet creation failed:', walletError, 'but login continues');
        //   // Không hiển thị lỗi cho user vì đăng nhập đã thành công
        // }
        await saveDataToAsyncStorage(
          StorageContanst.accessToken,
          res.data.accessToken,
        );
        await dispatch(CustomerActions.getInfor(true)).then(() => {
          // Kiểm tra dữ liệu trong store trước khi khởi tạo socket
          const state = store.getState();
          const customer = state.customer?.data;
          if (customer) {
            if (isFirstTime) {
              dispatch(
                CustomerActions.edit({
                  ...customer,
                  RefCode: Ultis.randomString(10),
                }),
              );
            }
            // Chỉ khởi tạo socket khi đã có dữ liệu customer
            AuthSocketService.initializeSocketConnection(
              customer?.Id,
              customer?.Name,
            );
            setLoading(false);
          } else {
            console.warn('Customer data not available after getInfor');
            setLoading(false);
          }
        });
        setLoading(false);

        //xóa mật khẩu, sdt, ghi nhớ mật khẩu
        removeDataToAsyncStorage('Password');
        removeDataToAsyncStorage('RememberPass');
        removeDataToAsyncStorage('Mobile');

        showSnackbar({
          message: t('auth.loginSuccess'),
          status: ComponentStatus.SUCCSESS,
        });

        navigateReset(RootScreen.navigateEComView, {isFirstTime: isFirstTime});
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        setLoading(false);
      }
    }
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      <FPopup ref={popupRef} />
      <KeyboardAvoidingView
        style={{
          width: '100%',
          height: '100%',
          paddingHorizontal: 32,
        }}>
        {!isSignUp && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-end',
            }}>
            <TouchableOpacity
              style={{paddingVertical: 8, paddingLeft: 12}}
              onPress={() => {
                showPopup({
                  ref: popupRef,
                  enableDismiss: true,
                  children: (
                    <LanguageSwitcher
                     ref={popupRef}
                    />
                  ),
                });
              }}>
              <Winicon src={`fill/text/text`} size={20} />
            </TouchableOpacity>
            <TouchableOpacity
              style={{paddingVertical: 8, paddingLeft: 12}}
              onPress={() => {
                showPopup({
                  ref: popupRef,
                  enableDismiss: true,
                  children: (
                    <PopupQrcodeScan
                      ref={popupRef}
                      onDone={(data: any) => {
                        methods.setValue('RefCode', data);
                        setSignUp(true);
                      }}
                    />
                  ),
                });
              }}>
              <SvgXml xml={scanner} width="20" height="20" />
            </TouchableOpacity>
          </View>
        )}
        <Image
          source={require('../../assets/splash.png')}
          style={{width: 120, height: 120, alignSelf: 'center'}}
        />

        {isSignUp ? (
          <SignUpView methods={methods} />
        ) : (
          <LoginView bio={bio} methods={methods} />
        )}

        <ListTile
          title={
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text
                style={[
                  TypoSkin.body3,
                  {color: ColorThemes.light.neutral_text_body_color},
                ]}>
                {isSignUp
                  ? t('auth.switchToLogin')
                  : t('auth.noAccount')}
              </Text>
              <AppButton
                title={isSignUp ? t('auth.login') : t('auth.register')}
                textColor={ColorThemes.light.primary_main_color}
                textStyle={{
                  ...TypoSkin.buttonText3,
                  color: ColorThemes.light.primary_main_color,
                  // underline text
                  textDecorationLine: 'underline',
                }}
                containerStyle={{
                  height: 32,
                  borderRadius: 8,
                  paddingHorizontal: 12,
                }}
                borderColor={ColorThemes.light.transparent}
                backgroundColor={ColorThemes.light.transparent}
                onPress={() => {
                  setSignUp(!isSignUp);
                  methods.reset();
                }}
              />
            </View>
          }
          style={{
            paddingVertical: 4,
            padding: 0,
            borderRadius: 0,
            borderTopColor: ColorThemes.light.neutral_main_border_color,
            borderTopWidth: 1,
          }}
          titleStyle={[
            TypoSkin.body3,
            {color: ColorThemes.light.neutral_text_body_color},
          ]}
        />

        {/* social login */}
        {isSignUp ? null : (
          <View style={{}}>
            <View
              style={{
                flexDirection: 'row',
                paddingBottom: 8,
                width: '100%',
                justifyContent: 'center',
              }}>
              <Text
                style={[
                  TypoSkin.body3,
                  {
                    alignSelf: 'baseline',
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  },
                ]}>
                {t('auth.or')}
              </Text>
            </View>
            <View style={{gap: 16}}>
              {Platform.OS === 'ios' && (
                <AppleSignIn
                  onLoading={setLoadingA}
                  isLoading={loadingA}
                  onAuthSuccess={value => _loginSocial(value, 'apple')}
                  title={t('auth.loginWithApple')}
                />
              )}
              <GoogleLogin
                onLoading={setLoadingG}
                isLoading={loadingG}
                onAuthSuccess={value => _loginSocial(value, 'google')}
                title={t('auth.loginWithGoogle')}
              />
            </View>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

// #region Login
const LoginView = ({methods, bio}: {methods: any; bio: string}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  
  const [isRemeberPass, setRemeberPass] = useState(false);
  const [loading, setLoading] = useState(false);

  const navigation = useNavigation<any>();
  const {t} = useTranslation();
  const dispatch = useDispatch<any>();

  const validationForm = useMemo(() => {
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
  ]);

  useEffect(() => {
    let timeoutVariable: ReturnType<typeof setTimeout> | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 20000);
    }

    return () => {
      if (timeoutVariable) {
        clearTimeout(timeoutVariable);
      }
    };
  }, [loading]);

  const _loginAction = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: t('auth.invalidPhone')});
      return;
    }

    setLoading(true);
    // using password
    // check sdt da dang ky
    if (!password) return;
    if (password === undefined || password.length == 0) {
      methods.setError('Password', {message: t('auth.passwordRequired')});
      return;
    }
    const valPass = regexPassWord.test(password);
    if (!valPass) {
      methods.setError('Password', {
        message: t('auth.passwordIncorrectFormat'),
      });
      setLoading(false);
      return;
    }
    methods.clearErrors('Password');
    setVisiblePass(true);

    // case login ADMIN CHAINIVO
    const customerController = new DataController('Customer');
    // lấy thông tin của admin chainivo
    const resAdmin = await customerController.getListSimple({
      page: 1,
      size: 1,
      query: `@Id: {${ConfigAPI.adminCHAINIVO}}`,
      returns: ['Id', 'Name', 'AvatarUrl', 'ListParent', 'RefCode'],
    });
    if (resAdmin.code === 200 && resAdmin.data.length > 0) {
      if (
        mobile === resAdmin.data[0].Mobile ||
        mobile === resAdmin.data[0].Email ||
        mobile?.toLowerCase() === resAdmin.data[0].Name?.toLowerCase()
      ) {
        const res = await CustomerActions.login({
          type: 'account',
          password: password,
          ...(mobile.includes('@')
            ? {email: mobile}
            : {
                phone:
                  mobile?.toLowerCase() === resAdmin.data[0].Name?.toLowerCase()
                    ? resAdmin.data[0].Mobile
                    : mobile,
              }),
        });
        switch (res?.code) {
          case 403:
            methods.setError('Password', {
              message: t('auth.passwordIncorrect'),
            });
            showSnackbar({
              message: t('auth.passwordIncorrect'),
              status: ComponentStatus.ERROR,
            });
            break;
          case 401:
            methods.setError('Mobile', {
              message: t('auth.phoneNotRegistered'),
            });
            showSnackbar({
              message: t('auth.phoneNotRegistered'),
              status: ComponentStatus.ERROR,
            });
            break;
          case 200:
            checkSavePass();
            // const deviceToken = await getDataToAsyncStorage('fcmToken');
            // if (deviceToken) {
            //   await CustomerActions.updateDeviceToken(deviceToken);
            // }
            // timeRefresh 10 phut
            await saveDataToAsyncStorage(
              StorageContanst.accessToken,
              res.data.accessToken,
            );
            await saveDataToAsyncStorage(
              StorageContanst.refreshToken,
              res.data.refreshToken,
            );
            await saveDataToAsyncStorage(
              StorageContanst.timeRefresh,
              `${Date.now() / 1000 + 9 * 60}`,
            );

            await saveDataToAsyncStorage('Mobile', mobile);
            dispatch(CustomerActions.getInfor(true)).then(async (res: any) => {
              // Khởi tạo kết nối socket sau khi đăng nhập thành công
              setTimeout(async () => {
                await AuthSocketService.initializeSocketConnection();
              }, 1000);

              setLoading(false);
              showSnackbar({
                message: t('auth.loginSuccess'),
                status: ComponentStatus.SUCCSESS,
              });
              navigation.replace(RootScreen.navigateEComView);
            });
            setLoading(false);
            break;
          default:
            showSnackbar({
              message: t('auth.genericError'),
              status: ComponentStatus.ERROR,
            });
            setLoading(false);
            break;
        }
        setLoading(false);
        return;
      }
    }

    const res = await CustomerActions.login({
      type: 'account',
      password: password,
      ...(mobile.includes('@') ? {email: mobile} : {phone: mobile}),
    });
    debugger;
    switch (res?.code) {
      case 403:
        methods.setError('Password', {
          message: t('auth.passwordIncorrect'),
        });
        showSnackbar({
          message: t('auth.passwordIncorrect'),
          status: ComponentStatus.ERROR,
        });
        break;
      case 401:
        methods.setError('Mobile', {
          message: t('auth.phoneNotRegistered'),
        });
        showSnackbar({
          message: t('auth.phoneNotRegistered'),
          status: ComponentStatus.ERROR,
        });
        break;
      case 200:
        checkSavePass();
        // const deviceToken = await getDataToAsyncStorage('fcmToken');
        // if (deviceToken) {
        //   await CustomerActions.updateDeviceToken(deviceToken);
        // }
        // timeRefresh 10 phut
        await saveDataToAsyncStorage(
          StorageContanst.accessToken,
          res.data.accessToken,
        );
        await saveDataToAsyncStorage(
          StorageContanst.refreshToken,
          res.data.refreshToken,
        );
        await saveDataToAsyncStorage(
          StorageContanst.timeRefresh,
          `${Date.now() / 1000 + 9 * 60}`,
        );

        await saveDataToAsyncStorage('Mobile', mobile);
        dispatch(CustomerActions.getInfor(true)).then(async (res: any) => {
          // Khởi tạo kết nối socket sau khi đăng nhập thành công
          setTimeout(async () => {
            await AuthSocketService.initializeSocketConnection();
          }, 1000);

          setLoading(false);
          showSnackbar({
            message: t('auth.loginSuccess'),
            status: ComponentStatus.SUCCSESS,
          });
          navigation.replace(RootScreen.navigateEComView);
        });
        setLoading(false);
        break;
      default:
        showSnackbar({
          message: t('auth.genericError'),
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        break;
    }
    setLoading(false);
  };

  

  useEffect(() => {
    getDataToAsyncStorage('RememberPass').then(rs => {
      if (rs == 'true') {
        setRemeberPass(true);
      }
    });
  }, []);

  const checkSavePass = () => {
    if (isRemeberPass) {
      saveDataToAsyncStorage('RememberPass', 'true');
      if (methods.watch('Password') && methods.watch('Password').length > 0)
        saveDataToAsyncStorage('Password', methods.watch('Password'));
    } else {
      saveDataToAsyncStorage('RememberPass', 'false');
      removeDataToAsyncStorage('Password');
    }
  };

  return (
    <TouchableWithoutFeedback
      style={{width: '100%', height: '100%'}}
      onPress={Keyboard.dismiss}>
      <View
        pointerEvents={loading ? 'none' : 'auto'}
        style={{
          width: '100%',
        }}>
        <FLoading
          visible={loading}
          avt={require('../../assets/appstore.png')}
        />

        <KeyboardAvoidingView style={{width: '100%', gap: 24}}>
          <TextFieldForm
            control={methods.control}
            name="Mobile"
            placeholder={t('auth.phonePlaceholder')}
            label={t('auth.phoneNumber')}
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              paddingLeft: 8,
              backgroundColor: ColorThemes.light.transparent,
            }}
            register={methods.register}
            prefix={
              <View
                style={{
                  flexDirection: 'row',
                  height: 32,
                  width: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: ColorThemes.light.primary_main_color,
                  borderRadius: 100,
                }}>
                <Winicon
                  src="fill/users/contact"
                  size={12}
                  color={ColorThemes.light.neutral_absolute_background_color}
                />
              </View>
            }
            type="number-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('Mobile', {
                  message: t('auth.invalidPhone'),
                });
                return;
              }
              var mobile = ev.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: t('auth.invalidPhone'),
                });
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="Password"
            label={t('auth.password')}
            prefix={
              <View
                style={{
                  flexDirection: 'row',
                  height: 32,
                  width: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: ColorThemes.light.primary_main_color,
                  borderRadius: 100,
                }}>
                <Winicon
                  src="fill/user interface/password"
                  size={12}
                  color={ColorThemes.light.neutral_absolute_background_color}
                />
              </View>
            }
            returnKeyType="done"
            placeholder={t('auth.passwordPlaceholder')}
            errors={methods.formState.errors}
            secureTextEntry={isVisiblePass}
            textFieldStyle={{
              height: 48,
              backgroundColor: ColorThemes.light.transparent,
              paddingVertical: 16,
              paddingLeft: 8,
            }}
            register={methods.register}
            suffix={
              <TouchableOpacity
                style={{padding: 12}}
                onPress={() => {
                  setVisiblePass(!isVisiblePass);
                }}>
                <Winicon
                  src={
                    isVisiblePass
                      ? `outline/user interface/view`
                      : `outline/user interface/hide`
                  }
                  size={14}
                />
              </TouchableOpacity>
            }
            onBlur={async (ev: string) => {
              var pass = ev.trim();
              if (!regexPassWord.test(pass))
                return methods.setError('Password', {
                  message: t('auth.passwordIncorrectFormat'),
                });
              methods.clearErrors('Password');
            }}
          />
        </KeyboardAvoidingView>

        <View
          style={{
            paddingTop: methods.formState.errors.Password ? 12 : 0,
            flexDirection: 'row',
            width: '100%',
            alignItems: 'center',
          }}>
          <TouchableOpacity
            onPress={async () => {
              setRemeberPass(!isRemeberPass);
            }}
            style={{
              paddingVertical: 8,
              alignItems: 'center',
              gap: 8,
              flexDirection: 'row',
            }}>
            <Checkbox
              onChange={(vl: any) => {
                saveDataToAsyncStorage('RememberPass', vl ? 'true' : 'false');
                setRemeberPass(vl);
              }}
              value={isRemeberPass}
            />
            <Text
              style={[
                TypoSkin.body3,
                {
                  color: isRemeberPass
                    ? ColorThemes.light.primary_main_color
                    : ColorThemes.light.neutral_text_subtitle_color,
                },
              ]}>
              {t('auth.rememberPassword')}
            </Text>
          </TouchableOpacity>
          <View style={{flex: 1}} />
          {/* <TouchableOpacity
            onPress={() => {
              var mobile = methods.watch('Mobile')?.trim();
              // Check if the number doesn't already start with 0 or +84
              if (
                mobile &&
                !/^(\+84|0)/.test(mobile) &&
                !mobile?.includes('@')
              ) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              navigation.navigate(RootScreen.ForgotPass, {
                isLogin: true,
                mobile: mobile || '',
              });
            }}
            style={{
              paddingVertical: 8,
              alignItems: 'center',
            }}>
            <Text
              style={[
                TypoSkin.body3,
                {
                  color: ColorThemes.light.neutral_text_subtitle_color,
                },
              ]}>
              Quên mật khẩu?
            </Text>
          </TouchableOpacity> */}
          <View />
        </View>

        <View
          style={{
            flexDirection: 'row',
            width: '100%',
            gap: 8,
            alignItems: 'center',
          }}>
          <AppButton
            title={t('auth.login')}
            textColor={ColorThemes.light.neutral_absolute_background_color}
            textStyle={{
              ...TypoSkin.buttonText1,
              color: ColorThemes.light.white,
            }}
            disabled={!validationForm}
            containerStyle={{
              height: 48,
              flex: 1,
              borderRadius: 8,
              marginTop: 8,
            }}
            borderColor={ColorThemes.light.neutral_main_border_color}
            backgroundColor={ColorThemes.light.primary_main_color}
            onPress={() => {
              _loginAction();
            }}
          />
          {bio == 'true' ? (
            <View style={{paddingTop: 8}}>
              <LocalAuthen
                isFirstTime={
                  methods.watch('LastMobile') !== methods.watch('Mobile') &&
                  methods.watch('Mobile')?.length != 0
                }
                onSuccess={async value => {
                  if (value === true) {
                    var mobile = await getDataToAsyncStorage('Mobile');
                    var password = await getDataToAsyncStorage('Password');
                    if (!mobile || !password) return;
                    // Check if the number doesn't already start with 0 or +84
                    if (/^(\+84|0)/.test(mobile) && !mobile.includes('@')) {
                      const val = validatePhoneNumber(mobile);
                      if (!val) {
                        methods.setError('Mobile', {
                          message: t('auth.invalidPhone'),
                        });
                        return;
                      }
                    }
                    setLoading(true);
                    const res = await CustomerActions.login({
                      type: 'account',
                      ...(mobile.includes('@')
                        ? {email: mobile}
                        : {phone: mobile}),
                      password: password,
                    });

                    if (res?.code === 200) {
                      saveDataToAsyncStorage('Mobile', `${mobile}`);

                      // Lấy thông tin user và khởi tạo socket
                      await dispatch(CustomerActions.getInfor(true));

                      // Khởi tạo kết nối socket sau khi đăng nhập thành công
                      setTimeout(async () => {
                        await AuthSocketService.initializeSocketConnection();
                      }, 1000);

                      showSnackbar({
                        message: t('auth.welcomeBack'),
                        status: ComponentStatus.SUCCSESS,
                      });
                      setLoading(false);
                      navigateReset(RootScreen.navigateEComView);
                    } else {
                      showSnackbar({
                        message: res.message,
                        status: ComponentStatus.ERROR,
                      });
                      setLoading(false);
                    }
                    setLoading(false);
                  }
                }}
              />
            </View>
          ) : null}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

// #region SignUp
const SignUpView = ({methods}: {methods: any}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isVisiblePass2, setVisiblePass2] = useState(true);
  const [loading, setLoading] = useState(false);
  const dialogRef = useRef<any>(null);
  const popupRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const {t} = useTranslation();
  const [acceptTerm, setAcceptTerm] = useState(false);

  const validationForm = useMemo(() => {
    return (
      !methods.formState.errors.Email?.message &&
      methods.watch('Email')?.length > 0 &&
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message &&
      methods.watch('ConfirmPassword')?.length > 0 &&
      !methods.formState.errors.ConfirmPassword?.message
    );
  }, [
    methods.watch('Email'),
    methods.formState.errors.Email?.message,
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
    methods.watch('ConfirmPassword'),
    methods.formState.errors.ConfirmPassword?.message,
  ]);

  useEffect(() => {
    if (!__DEV__) {
      methods.setValue('Mobile', undefined);
    }
    methods.setValue('Password', undefined);
    setLoading(false);
    setAcceptTerm(false);
    devTest();
  }, []);

  useEffect(() => {
    let timeoutVariable: ReturnType<typeof setTimeout> | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 20000);
    }

    return () => {
      if (timeoutVariable) {
        clearTimeout(timeoutVariable);
      }
    };
  }, [loading]);

  const devTest = async () => {
    if (__DEV__) {
      methods.setValue('RefCode', 'RG33oMF2cy');
      methods.setValue('Mobile', '0385011233');
      methods.setValue('Name', 'Chienday123');
      methods.setValue('Email', '<EMAIL>');
      methods.setValue('Password', 'Chien123');
      methods.setValue('ConfirmPassword', 'Chien123');
      setAcceptTerm(true);
    }
  };

  const onCheckPhone = async () => {
    var mobile = methods.watch('Mobile').trim();
    var email = methods.watch('Email').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!validatePhoneNumber(mobile)) {
      methods.setError('Mobile', {message: t('auth.invalidPhone')});
      return false;
    }

    setLoading(true);

    // check sdt bi khoa
    // check sdt da dang ky
    const resCustomers = await customerController.getListSimple({
      page: 1,
      size: 1,
      query: `@Email:("${email}") | @Mobile:(*${mobile}*)`,
    });
    if (resCustomers) {
      if (resCustomers?.data?.length > 0) {
        // check sdt bi khoa
        if (resCustomers?.data[0]?.Status == CustomerStatus.locked) {
          showSnackbar({
            message: t('auth.accountLocked'),
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
          return false;
        }
        showSnackbar({
          message: t('auth.accountExists'),
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return false;
      }
    }
    setLoading(false);
    return true;
  };

  const customerController = new DataController('Customer');
  const scrollRef = useRef<any>(null);

  const _signUp = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: t('auth.invalidPhone')});
      return;
    }

    setLoading(true);

    const deviceToken = await getDataToAsyncStorage('fcmToken');
    const hashPass = await CustomerActions.hashPassword(password);

    if (hashPass.code != 200) {
      setLoading(false);
      showSnackbar({
        message: hashPass.message,
        status: ComponentStatus.ERROR,
      });
      return;
    }
    //lấy thông tin theo ref code
    var parentId;
    var listParent;
    var hasParent = false;
    if (methods.watch('RefCode')?.length > 0) {
      const resRef = await customerController.getListSimple({
        page: 1,
        size: 1,
        query: `@RefCode: (*${methods.watch('RefCode')}*)`,
        returns: ['Id', 'Name', 'AvatarUrl', 'ListParent', 'RefCode'],
      });
      debugger
      if (resRef.code === 200 && resRef.data.length > 0) {
        hasParent = true;
        parentId = resRef.data[0].Id;
        listParent = resRef.data[0].ListParent
          ? resRef.data[0].ListParent + ',' + resRef.data[0].Id
          : resRef.data[0].Id;
      } else {
        showSnackbar({
          message: t('auth.invalidRefCode'),
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }
    } else {
      // START case: nếu k có mã giới thiệu. Hệ thống tự add mã của chainivo
      // lấy thông tin của admin chainivo
      const resAdmin = await customerController.getListSimple({
        page: 1,
        size: 1,
        query: `@Id: {${ConfigAPI.adminCHAINIVO}}`,
        returns: ['Id', 'Name', 'AvatarUrl', 'ListParent', 'RefCode'],
      });
      if (resAdmin.code === 200 && resAdmin.data.length > 0) {
        parentId = resAdmin.data[0].Id;
        listParent = resAdmin.data[0].ListParent
          ? resAdmin.data[0].ListParent + ',' + resAdmin.data[0].Id
          : resAdmin.data[0].Id;
      }
      // END
    }
    // #endregion
    const newCus = {
      Id: randomGID(),
      Name: methods.watch('Name'),
      Email: methods.watch('Email'),
      Address: methods.watch('Address'),
      Long: methods.watch('Long'),
      Lat: methods.watch('Lat'),
      DateCreated: Date.now(),
      Mobile: mobile,
      Status: CustomerStatus.active,
      Password: hashPass.data,
      DeviceToken: deviceToken,
      RefCode: Ultis.randomString(10),
      ParentId: parentId,
      ListParent: listParent,
    };
    console.log('newCus', newCus);
    const customerRes = await customerController.add([newCus]);
    debugger
    if (customerRes?.code == 200) {
      // Đăng nhập trước để đảm bảo user có thể sử dụng app
      const res = await CustomerActions.login({
        type: 'account',
        phone: mobile,
        password: password,
      });

      if (res?.code === 200) {
        // nếu refCode == refcodeshare
        const refcodeInput = methods.watch('RefCode')?.toLowerCase()?.trim();
        const refcodeShare = await getDataToAsyncStorage('RefCodeShared');
        if (refcodeInput === refcodeShare?.toLowerCase()?.trim() || hasParent) {
          // call cộng point cho người giới thiệu
          const customerDA = new CustomerDA();
          await customerDA.pointPlusRefCode(newCus as any);
        }
        //
        saveDataToAsyncStorage('Mobile', `${mobile}`);
        removeDataToAsyncStorage('Password');
        removeDataToAsyncStorage('RememberPass');
        // Tạo ví trong background - không ảnh hưởng đến đăng nhập
        try {
          const resultWallet = await CustomerActions.createWallet(mobile);
          if (resultWallet && resultWallet.address && resultWallet.privateKey) {
            await customerController.edit([
              {
                Id: newCus.Id,
                WalletAddress: resultWallet.address,
                PrivateKey: resultWallet.privateKey,
              },
            ]);
            console.log('Wallet created successfully for user:', mobile);
          } else {
            console.warn(
              'Failed to create wallet for user:',
              mobile,
              'but login continues',
            );
          }
        } catch (walletError) {
          console.error(
            'Wallet creation failed:',
            walletError,
            'but login continues',
          );
          // Không hiển thị lỗi cho user vì đăng nhập đã thành công
        }

        dispatch(CustomerActions.getInfor()).then(() => {
          setLoading(false);
          showSnackbar({
            message: t('auth.registerSuccessAndLogin'),
            status: ComponentStatus.SUCCSESS,
          });
          navigation.replace(RootScreen.navigateEComView);
        });
        // khởi tạo address nếu người dùng nhập address, lat, long
        if (
          methods.watch('Address')?.length > 0 &&
          methods.watch('Long') &&
          methods.watch('Lat')
        ) {
          const address = {
            Id: randomGID(),
            CustomerId: newCus.Id,
            Name: 'Địa chỉ khởi tạo của ' + newCus.Name || newCus.Mobile,
            Address: methods.watch('Address'),
            Long: methods.watch('Long'),
            Lat: methods.watch('Lat'),
            IsDefault: true,
          };
          await dispatch(CustomerActions.editAddress(address, true));
        }
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        setLoading(false);
      }
    } else {
      showSnackbar({
        message: customerRes.message ?? t('auth.genericRegisterError'),
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }
    setLoading(false);
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{width: '100%'}}
      ref={scrollRef}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      <Pressable>
        <KeyboardAvoidingView
          behavior={'padding'}
          style={{width: '100%', marginTop: 8}}>
          <View style={{width: '100%', gap: 16}}>
            <TextFieldForm
              control={methods.control}
              name="RefCode"
              placeholder={t('auth.refCodePlaceholder')}
              label={t('auth.refCodeLabel')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingVertical: 16,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              type="name-phone-pad"
              onBlur={(ev: string) => {}}
              suffix={
                <TouchableOpacity
                  style={{paddingVertical: 12, paddingRight: 16}}
                  onPress={() => {
                    showPopup({
                      ref: popupRef,
                      enableDismiss: true,
                      children: (
                        <PopupQrcodeScan
                          ref={popupRef}
                          onDone={(data: any) => {
                            methods.setValue('RefCode', data);
                          }}
                        />
                      ),
                    });
                  }}>
                  <Winicon src={`outline/touch gestures/scan`} size={20} />
                </TouchableOpacity>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Name"
              placeholder={t('auth.namePlaceholder')}
              label={t('auth.nameLabel')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingVertical: 16,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              type="name-phone-pad"
              onBlur={(ev: string) => {
                if (ev?.length !== 0) methods.clearErrors('Name');
                else
                  methods.setError('Name', {
                    message: t('auth.nameRequired'),
                  });
              }}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Email"
              required
              placeholder={t('auth.email')}
              label={t('auth.email')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingVertical: 16,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              type="email-address"
              onBlur={async (ev: string) => {
                const resCustomers = await customerController.getListSimple({
                  page: 1,
                  size: 1,
                  query: `@Email:("${ev?.trim()}")`,
                });
                if (
                  resCustomers?.code == 200 &&
                  resCustomers?.data?.length > 0
                ) {
                  methods.setError('Email', {
                    message: t('auth.emailExists'),
                  });
                  return;
                }

                if (ev?.length !== 0) methods.clearErrors('Email');
                else
                  methods.setError('Email', {
                    message: t('auth.emailRequired'),
                  });
              }}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/users/contact"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
            />
            <TextFieldForm
              control={methods.control}
              name="Mobile"
              required
              label={t('auth.phoneNumber')}
              placeholder={t('auth.phonePlaceholder')}
              returnKeyType="done"
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                paddingLeft: 8,
                backgroundColor: ColorThemes.light.transparent,
              }}
              register={methods.register}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/user interface/phone"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              type="number-pad"
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('Mobile', {
                    message: t('auth.phoneRequired'),
                  });
                  return;
                }
                var mobile = ev.trim();
                // Check if the number doesn't already start with 0 or +84
                if (!/^(\+84|0)/.test(mobile)) {
                  mobile = '0' + mobile; // Add 0 at the beginning
                }
                const resCustomers = await customerController.getListSimple({
                  page: 1,
                  size: 1,
                  query: `@Mobile:(*${mobile}*)`,
                });

                if (
                  resCustomers?.code == 200 &&
                  resCustomers?.data?.length > 0
                ) {
                  methods.setError('Mobile', {
                    message: t('auth.phoneExists'),
                  });
                  return;
                }
                const val = validatePhoneNumber(mobile);
                if (val) methods.clearErrors('Mobile');
                else
                  methods.setError('Mobile', {
                    message: t('auth.invalidPhone'),
                  });
              }}
            />
            <View style={{width: '100%', gap: 8}}>
              <TextFieldForm
                control={methods.control}
                name="Password"
                required
                secureTextEntry={isVisiblePass}
                label={t('auth.password')}
                returnKeyType="done"
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 32,
                      width: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 100,
                    }}>
                    <Winicon
                      src="fill/user interface/password"
                      size={12}
                      color={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                    />
                  </View>
                }
                placeholder={t('auth.passwordPlaceholder')}
                suffix={
                  <TouchableOpacity
                    style={{padding: 12}}
                    onPress={() => {
                      setVisiblePass(!isVisiblePass);
                    }}>
                    <Winicon
                      src={
                        isVisiblePass
                          ? `outline/user interface/view`
                          : `outline/user interface/hide`
                      }
                      size={14}
                    />
                  </TouchableOpacity>
                }
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  backgroundColor: ColorThemes.light.transparent,
                  paddingLeft: 8,
                  paddingVertical: 16,
                  marginBottom: methods.formState.errors.Password?.message
                    ? 16
                    : 8,
                }}
                register={methods.register}
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('Password', {
                      message: t('auth.passwordRequired'),
                    });
                    return;
                  }
                  var pass = ev.trim();
                  if (!regexPassWord.test(pass))
                    return methods.setError('Password', {
                      message: t('auth.passwordIncorrectFormat'),
                    });
                  methods.clearErrors('Password');
                }}
              />
              <Text
                style={[
                  TypoSkin.subtitle4,
                  {
                    alignSelf: 'baseline',
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  },
                ]}>{t('auth.passwordRequirements')}</Text>
              <TextFieldForm
                control={methods.control}
                name="ConfirmPassword"
                required
                label={t('auth.confirmPasswordLabel')}
                returnKeyType="done"
                secureTextEntry={isVisiblePass2}
                suffix={
                  <TouchableOpacity
                    style={{padding: 12}}
                    onPress={() => {
                      setVisiblePass2(!isVisiblePass2);
                    }}>
                    <Winicon
                      src={
                        isVisiblePass2
                          ? `outline/user interface/view`
                          : `outline/user interface/hide`
                      }
                      size={14}
                    />
                  </TouchableOpacity>
                }
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 32,
                      width: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 100,
                    }}>
                    <Winicon
                      src="fill/user interface/password"
                      size={12}
                      color={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                    />
                  </View>
                }
                placeholder={t('auth.passwordPlaceholder')}
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  backgroundColor: ColorThemes.light.transparent,
                  paddingLeft: 8,
                  paddingVertical: 16,
                }}
                register={methods.register}
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('ConfirmPassword', {
                      message: t('auth.confirmPasswordRequired'),
                    });
                    return;
                  }
                  var rePass = ev.trim();
                  if (methods.watch('Password') !== rePass)
                    return methods.setError('ConfirmPassword', {
                      message: t('auth.passwordMismatch'),
                    });
                  methods.clearErrors('ConfirmPassword');
                }}
              />
            </View>
            <FAddressPickerForm
              control={methods.control}
              errors={methods.formState.errors}
              name="Address"
              label={t('auth.addressLabel')}
              prefix={
                <View
                  style={{
                    flexDirection: 'row',
                    height: 32,
                    width: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: ColorThemes.light.primary_main_color,
                    borderRadius: 100,
                  }}>
                  <Winicon
                    src="fill/location/map-marker"
                    size={12}
                    color={ColorThemes.light.neutral_absolute_background_color}
                  />
                </View>
              }
              placeholder={t('auth.addressPlaceholder')}
              onChange={value => {
                methods.setValue('Long', value.geometry.location.lng);
                methods.setValue('Lat', value.geometry.location.lat);
                methods.setValue('Address', value.formatted_address);
                return value.formatted_address;
              }}
              textFieldStyle={{
                paddingLeft: 8,
                gap: 12,
              }}
            />
          </View>
          {/* checkbox xác nhận với điều khoản sử dụng chainivo */}
          <TouchableOpacity
            onPress={() => {
              setAcceptTerm(!acceptTerm);
            }}
            style={{
              alignItems: 'center',
              flexDirection: 'row',
              width: '100%',
              paddingTop: 16,
              gap: 4,
            }}>
            <Checkbox
              onChange={(vl: any) => {
                setAcceptTerm(vl);
              }}
              value={acceptTerm}
            />
            <View style={{flex: 1}}>
              <Text
                style={[
                  TypoSkin.body3,
                  {
                    color: acceptTerm
                      ? ColorThemes.light.primary_main_color
                      : ColorThemes.light.neutral_text_subtitle_color,
                  },
                ]}>
                {t('auth.agreeToTerms')}
                <Text
                  style={{
                    color: ColorThemes.light.primary_main_color,
                    textDecorationLine: 'underline',
                  }}
                  onPress={() => {
                    navigation.navigate(RootScreen.PolicyView);
                  }}>
                  {t('auth.termsAndConditions')}
                </Text>
              </Text>
            </View>
          </TouchableOpacity>
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              paddingTop: 16,
              paddingBottom: 130,
            }}>
            <AppButton
              title={t('auth.register')}
              textColor={ColorThemes.light.neutral_absolute_background_color}
              textStyle={{
                ...TypoSkin.buttonText1,
                color: ColorThemes.light.neutral_absolute_background_color,
              }}
              disabled={!validationForm || loading || !acceptTerm}
              containerStyle={{
                height: 48,
                flex: 1,
                borderRadius: 8,
                marginTop: 8,
              }}
              borderColor={ColorThemes.light.neutral_main_border_color}
              backgroundColor={ColorThemes.light.primary_main_color}
              onPress={async () => {
                methods.handleSubmit(async (data: any) => {
                  if (loading) return;
                  const rs = await onCheckPhone();
                  if (rs) {
                    _signUp();
                  }
                })();
              }}
            />
          </View>
        </KeyboardAvoidingView>
      </Pressable>
    </ScrollView>
  );
};
