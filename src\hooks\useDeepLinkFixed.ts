import {useEffect, useRef} from 'react';
import {Al<PERSON>, Linking, Platform} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  DeepLinkUrl,
  RootStackParamList,
} from '../Screen/Layout/navigation/ecomNavigator';
import {RootScreen} from '../router/router';
import {
  extractProductInfo,
  extractPostInfo,
  extractNewsInfo,
  extractLoginInfo,
  isValidDeepLink,
  parseDeepLink,
  extractEventsInfo,
} from '../utils/deepLinkUtils';
import {saveDataToAsyncStorage} from '../utils/AsyncStorage';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface DeepLinkHandler {
  handleDeepLink: (url: DeepLinkUrl) => void;
}

export const useDeepLinkFixed = (): DeepLinkHandler => {
  const navigation = useNavigation<NavigationProp>();
  const pendingUrl = useRef<string | null>(null);
  const navigationReady = useRef<boolean>(false);
  const initialUrlProcessed = useRef<boolean>(false);

  useEffect(() => {
    console.log('🔗 DeepLink hook initialized (Fixed Version)');

    // Check if navigation is ready
    const unsubscribe = navigation.addListener('state', () => {
      navigationReady.current = true;
      console.log('🧭 Navigation is ready');

      // Process pending URL if exists
      if (pendingUrl.current) {
        const url = pendingUrl.current;
        pendingUrl.current = null;
        console.log('📝 Processing pending URL:', url);
        handleDeepLink(url);
      }
    });

    // Handle app opening from deep link (app was closed)
    const getInitialURL = async (): Promise<void> => {
      try {
        // For iOS, try multiple times as getInitialURL can be unreliable
        let initialUrl: string | null = null;
        let attempts = 0;
        const maxAttempts = Platform.OS === 'ios' ? 3 : 1;

        while (!initialUrl && attempts < maxAttempts) {
          attempts++;
          console.log(
            `🔍 Attempt ${attempts}/${maxAttempts} to get initial URL`,
          );

          try {
            initialUrl = await Linking.getInitialURL();
            if (initialUrl) {
              console.log(
                `✅ Got initial URL on attempt ${attempts}:`,
                initialUrl,
              );
              break;
            }
          } catch (attemptError) {
            console.warn(`❌ Attempt ${attempts} failed:`, attemptError);
          }

          // Wait before next attempt (iOS only)
          if (Platform.OS === 'ios' && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(() => resolve(true), 200));
          }
        }

        console.log('📱 Platform:', Platform.OS);
        console.log('🔍 Final initial URL check:', initialUrl);

        if (initialUrl && !initialUrlProcessed.current) {
          initialUrlProcessed.current = true;
          console.log('✅ App opened with initial URL:', initialUrl);

          // iOS specific handling with longer delay
          if (Platform.OS === 'ios') {
            // Wait longer for iOS navigation to be ready
            setTimeout(() => {
              if (navigationReady.current) {
                handleDeepLink(initialUrl);
              } else {
                pendingUrl.current = initialUrl;
                console.log('📝 Navigation not ready, storing URL for later');
              }
            }, 1000); // Increased delay for iOS
          } else {
            // Android can handle with shorter delay
            setTimeout(() => {
              if (navigationReady.current) {
                handleDeepLink(initialUrl);
              } else {
                pendingUrl.current = initialUrl;
                console.log('📝 Navigation not ready, storing URL for later');
              }
            }, 300);
          }
        } else if (!initialUrl) {
          console.log('❌ No initial URL found after all attempts');
        } else {
          console.log('⚠️ Initial URL already processed, skipping');
        }
      } catch (error) {
        console.error('❌ Error getting initial URL:', error);
      }
    };

    // Handle app receiving deep link while running
    const handleUrlChange = (event: {url: string}) => {
      console.log('🔄 App received URL while running:', event.url);

      // Check if navigation is ready
      if (navigationReady.current) {
        handleDeepLink(event.url);
      } else {
        // Store URL to process later
        pendingUrl.current = event.url;
        console.log('📝 URL stored for later processing');
      }
    };

    // Setup URL listener with proper error handling and verification
    let subscription: any = null;
    let listenerWorking = false;

    // Test function to verify listener is working
    const testListener = () => {
      console.log('🧪 Testing URL listener...');

      // Try to trigger a test to see if listener responds
      setTimeout(async () => {
        try {
          const currentUrl = await Linking.getInitialURL();
          console.log('🔍 Current URL for listener test:', currentUrl);

          if (!listenerWorking) {
            console.warn(
              '⚠️ URL listener may not be working - no events received',
            );
            console.log(
              '🔄 Consider using manual testing or check iOS configuration',
            );
          }
        } catch (error) {
          console.warn('❌ Listener test failed:', error);
        }
      }, 3000); // Test after 3 seconds
    };

    // Enhanced URL change handler with verification
    const enhancedHandleUrlChange = (event: {url: string}) => {
      listenerWorking = true; // Mark that listener is receiving events
      console.log('🎯 URL listener WORKING - received event:', event.url);
      handleUrlChange(event);
    };

    try {
      // Try the modern addEventListener first (React Native 0.65+)
      console.log('🔗 Trying modern Linking.addEventListener...');
      subscription = Linking.addEventListener('url', enhancedHandleUrlChange);
      console.log('✅ Modern Linking.addEventListener setup successful');

      // Test the listener
      testListener();
    } catch (error) {
      console.warn('❌ Modern addEventListener failed:', error);

      try {
        // Fallback to legacy method for older RN versions
        console.log('🔗 Trying legacy Linking.addListener...');
        // @ts-ignore - Legacy method
        subscription = Linking.addListener('url', enhancedHandleUrlChange);
        console.log('✅ Legacy Linking.addListener setup successful');

        // Test the listener
        testListener();
      } catch (legacyError) {
        console.error('❌ Both addEventListener methods failed:', legacyError);
        console.log(
          '⚠️ Deep link listener setup failed - URLs while app is running may not work',
        );
        console.log(
          '💡 Try testing with: testDeepLink("chainivo://share/TEST123")',
        );
      }
    }

    // Delay initial URL check to ensure navigation is set up
    const initTimer = setTimeout(
      () => {
        getInitialURL();
      },
      Platform.OS === 'ios' ? 500 : 100,
    );

    console.log('👂 URL listener setup complete');
    console.log(
      '🧪 To test listener: Open Safari and go to "chainivo://share/TEST123"',
    );
    console.log('🔍 Or use console: testDeepLink("chainivo://share/TEST123")');

    return () => {
      console.log('🧹 Cleaning up URL listener');
      clearTimeout(initTimer);
      subscription?.remove();
      unsubscribe();
    };
  }, []);

  const handleDeepLink = (url: DeepLinkUrl): void => {
    console.log('\n=== DEEP LINK PROCESSING ===');
    console.log('Raw URL:', url);

    try {
      // Step 1: Validate URL
      if (!isValidDeepLink(url)) {
        console.warn('Invalid deep link format');
        showErrorAlert('Invalid link format');
        safeNavigate(RootScreen.navigateEComView);
        return;
      }

      // Step 2: Parse URL
      const parsed = parseDeepLink(url);
      if (!parsed) {
        console.warn('Unable to parse deep link');
        showErrorAlert('Unable to process link');
        safeNavigate(RootScreen.navigateEComView);
        return;
      }

      console.log('Parsed Deep Link:', JSON.stringify(parsed, null, 2));

      // Step 3: Navigate based on parsed data
      navigateToScreen(parsed);
    } catch (error) {
      console.error('Deep link error:', error);
      showErrorAlert('Error processing link');
      safeNavigate(RootScreen.navigateEComView);
    }

    console.log('=== END DEEP LINK PROCESSING ===\n');
  };

  const safeNavigate = (screenName: string, params?: any): void => {
    try {
      if (params) {
        navigation.navigate(screenName as any, params);
      } else {
        navigation.navigate(screenName as any);
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback navigation
      setTimeout(() => {
        navigation.navigate(RootScreen.navigateEComView as any);
      }, 100);
    }
  };

  const navigateToScreen = (parsed: any): void => {
    switch (parsed.screen?.toLowerCase()) {
      case 'product':
        handleProductNavigation(parsed);
        break;

      case 'posts':
        handlePostNavigation(parsed);
        break;

      case 'news':
        handleNewsNavigation(parsed);
        break;

      case 'events':
        handleEventsNavigation(parsed);
        break;

      case 'share':
        handleLoginNavigation(parsed);
        break;

      default:
        console.warn('Unknown screen:', parsed.screen);
        safeNavigate(RootScreen.navigateEComView);
    }
  };

  const handleProductNavigation = (parsed: any): void => {
    const productInfo = extractProductInfo(parsed);

    if (!productInfo?.productId) {
      console.warn('No product ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('Product Info:', productInfo);

    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {
          name: RootScreen.ProductDetail as any,
          params: {id: productInfo.productId},
        },
      ],
    });
  };

  const handlePostNavigation = (parsed: any): void => {
    const postInfo = extractPostInfo(parsed);

    if (!postInfo?.postId) {
      console.warn('No post ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('Post Info:', postInfo);

    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {
          name: RootScreen.PostDetail as any,
          params: {id: postInfo.postId},
        },
      ],
    });
  };

  const handleNewsNavigation = (parsed: any): void => {
    const newsInfo = extractNewsInfo(parsed);

    if (!newsInfo?.newsId) {
      console.warn('No news ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('News Info:', newsInfo);

    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {
          name: RootScreen.DetailNews as any,
          params: {id: newsInfo.newsId},
        },
      ],
    });
  };

  const handleEventsNavigation = (parsed: any): void => {
    const newsInfo = extractEventsInfo(parsed);

    if (!newsInfo?.eventId) {
      console.warn('No event ID found');
      safeNavigate(RootScreen.navigateEComView);
      return;
    }

    console.log('event Info:', newsInfo);

    navigation.reset({
      index: 1,
      routes: [
        {name: RootScreen.navigateEComView as any},
        {
          name: RootScreen.DetailEvent as any,
          params: {id: newsInfo.eventId},
        },
      ],
    });
  };

  const handleLoginNavigation = async (parsed: any): Promise<void> => {
    const loginInfo = extractLoginInfo(parsed);
    console.log('🔗 Share Link Info:', loginInfo);

    try {
      // Save RefCodeShared to AsyncStorage
      const refCode = loginInfo?.qrId || '';
      if (refCode) {
        await saveDataToAsyncStorage('RefCodeShared', refCode);
        console.log('✅ RefCodeShared saved to AsyncStorage:', refCode);
      } else {
        console.warn('⚠️ No reference code found in share link');
      }

      // Navigate to home screen
      safeNavigate(RootScreen.navigateEComView);
    } catch (error) {
      console.error('❌ Error saving share link info:', error);
      // Still navigate to home even if save fails
      safeNavigate(RootScreen.navigateEComView);
    }
  };

  const showErrorAlert = (message: string): void => {
    Alert.alert('Link Error', message, [{text: 'OK', style: 'default'}], {
      cancelable: true,
    });
  };

  return {handleDeepLink};
};
