import {useState, useEffect, useCallback} from 'react';

export interface MonthItem {
  id: number;
  name: string;
  fullName: string;
  value: number;
}

export interface MonthDateRange {
  startDate: string;
  endDate: string;
  monthName: string;
  isCurrentMonth: boolean;
}

export const useMonthFilter = () => {
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(
    new Date().getFullYear(),
  );
  const [dateStart, setDateStart] = useState<number | null>(null);
  const [dateEnd, setDateEnd] = useState<number | null>(null);

  // Tính toán khoảng thời gian và số point theo logic mới
  const getMonthDateRange = useCallback((): MonthDateRange => {
    const now = new Date();

    let targetMonth: Date;
    let startDate: Date;
    let endDate: Date;
    let isCurrentMonth: boolean;

    // <PERSON><PERSON><PERSON> có bộ lọc tháng được chọn, sử dụng tháng đó
    if (selectedMonth !== null) {
      targetMonth = new Date(selectedYear, selectedMonth - 1, 1);
      startDate = new Date(selectedYear, selectedMonth - 1, 1);
      endDate = new Date(selectedYear, selectedMonth, 0); // Ngày cuối tháng được chọn

      // Kiểm tra xem có phải tháng hiện tại không
      const currentMonth = now.getMonth() + 1;
      const currentYear = now.getFullYear();
      isCurrentMonth =
        selectedMonth === currentMonth && selectedYear === currentYear;
    } else {
      // Logic cũ: dựa trên ngày hiện tại
      const currentDay = now.getDate();

      if (currentDay >= 1 && currentDay <= 5) {
        // Từ mùng 1 đến mùng 5: hiển thị tháng trước
        targetMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        startDate = new Date(
          targetMonth.getFullYear(),
          targetMonth.getMonth(),
          1,
        );
        endDate = new Date(
          targetMonth.getFullYear(),
          targetMonth.getMonth() + 1,
          0,
        ); // Ngày cuối tháng trước
        isCurrentMonth = false;
      } else {
        // Từ mùng 6 đến 31: hiển thị tháng này
        targetMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        startDate = new Date(
          targetMonth.getFullYear(),
          targetMonth.getMonth(),
          1,
        );
        endDate = new Date(); // Ngày hiện tại
        isCurrentMonth = true;
      }
    }

    const formatDate = (date: Date) => {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    };

    const getMonthName = (date: Date) => {
      const month = date.getMonth() + 1;
      const year = date.getFullYear();
      return `${month}/${year}`;
    };

    return {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      monthName: getMonthName(targetMonth),
      isCurrentMonth,
    };
  }, [selectedMonth, selectedYear]);

  // Tạo danh sách 12 tháng
  const generateMonthList = useCallback((): MonthItem[] => {
    const months = [];
    for (let i = 1; i <= 12; i++) {
      const monthName = new Date(selectedYear, i - 1, 1).toLocaleDateString(
        'vi-VN',
        {
          month: 'long',
          year: 'numeric',
        },
      );
      months.push({
        id: i,
        name: `Tháng ${i}`,
        fullName: monthName,
        value: i,
      });
    }
    return months;
  }, [selectedYear]);

  // Cập nhật dateStart và dateEnd khi có thay đổi
  useEffect(() => {
    if (selectedMonth !== null) {
      // Tính toán timestamp cho ngày đầu tháng (0h) và cuối tháng (23:59)
      const startOfMonth = new Date(selectedYear, selectedMonth - 1, 1);
      startOfMonth.setHours(0, 0, 0, 0);

      const endOfMonth = new Date(selectedYear, selectedMonth, 0);
      endOfMonth.setHours(23, 59, 59, 999);

      const startTimestamp = startOfMonth.getTime();
      const endTimestamp = endOfMonth.getTime();

      setDateStart(startTimestamp);
      setDateEnd(endTimestamp);
    }
  }, [selectedMonth]);

  return {
    selectedMonth,
    selectedYear,
    dateStart,
    dateEnd,
    setSelectedMonth,
    setSelectedYear,
    getMonthDateRange,
    generateMonthList,
  };
};
