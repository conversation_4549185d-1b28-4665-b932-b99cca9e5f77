import i18n from 'i18next';
import {initReactI18next} from 'react-i18next';
import * as RNLocalize from 'react-native-localize';
import AsyncStorage from '@react-native-async-storage/async-storage';

import en from './en.json';
import vi from './vi.json';
import ja from './ja.json';
import zh from './zh.json';
import de from './de.json';

// Lấy ngôn ngữ mặc định từ hệ thống
const getDeviceLanguage = () => {
  const supportedLngs = ['vi', 'en', 'ja', 'zh', 'de'];
  const deviceLanguage = RNLocalize.findBestLanguageTag(supportedLngs);

  // Mặc định là 'vi' nếu không tìm thấy ngôn ngữ nào được hỗ trợ
  return deviceLanguage?.languageTag || 'vi';
};

const loadLanguage = async () => {
  const storedLang = await AsyncStorage.getItem('language');
  return storedLang || getDeviceLanguage();
};

loadLanguage().then(language => {
  i18n.use(initReactI18next).init({
    resources: {
      en: {translation: en},
      vi: {translation: vi},
      ja: {translation: ja},
      zh: {translation: zh},
      de: {translation: de},
    },
    lng: language,
    fallbackLng: 'vi', // Đặt tiếng Việt làm ngôn ngữ dự phòng
    interpolation: {escapeValue: false},
  });
});

export default i18n;
