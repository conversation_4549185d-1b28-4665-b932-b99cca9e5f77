{"welcome": "Welcome to our app!", "change_language": "Change Language", "common": {"back": "Back", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "search": "Search", "create": "Create", "description": "Description", "noData": "No data", "loading": "Loading", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "next": "Next", "previous": "Previous", "continue": "Continue", "finish": "Finish", "retry": "Retry", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "seeMore": "See more", "seeLess": "See less", "all": "All", "more": "More", "less": "Less", "filter": "Filter", "sort": "Sort", "apply": "Apply", "reset": "Reset", "clear": "Clear", "done": "Done", "notFound": "Not found", "titleBottom": "Title", "noMoreData": "No more data", "selectLanguage": "Select Language", "welcomeMessage": "Welcome to our app!", "refCodeInvalid": "Invalid referral code", "refCodePlaceholder": "Referral code (Optional)", "refCodeLabel": "Referral code (Optional)", "refCodeRequired": "Referral code is required", "refCodeInvalidFormat": "Incorrect referral code format, please try again", "noLogin": "You have not logged in"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "fullName": "Full Name", "phoneNumber": "Phone Number", "rememberMe": "Remember Me", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signUp": "Sign Up", "signIn": "Sign In", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "enterFullName": "Enter your full name", "enterPhoneNumber": "Enter your phone number", "passwordNotMatch": "Passwords do not match", "passwordRequirements": "Password must be at least 8 characters", "forgotPasswordDescription": "Enter your email and we'll send you a link to reset your password", "resetPasswordSuccess": "Your password has been reset successfully", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "registerSuccess": "Registration successful", "registerFailed": "Registration failed", "logoutConfirm": "Are you sure you want to logout?", "logoutSuccess": "Logout successful", "welcomeBack": "Login successful, Welcome back", "registerSuccessAndLogin": "Account created and logged in successfully", "switchToLogin": "Switch to login now?", "noAccount": "Don't have an account?", "or": "Or", "invalidPhone": "Invalid phone number", "passwordRequired": "Password is required", "passwordIncorrectFormat": "Incorrect password format, please try again", "passwordIncorrect": "Incorrect password, please check again.", "phoneNotRegistered": "Phone number is not registered.", "genericError": "An error has occurred", "phonePlaceholder": "Enter your phone number", "passwordPlaceholder": "Enter your password", "rememberPassword": "Remember password", "forgotPasswordLink": "Forgot password?", "accountLocked": "Your account has been locked, please contact the administrator for support.", "accountExists": "Account already registered", "invalidRefCode": "Invalid referral code", "genericRegisterError": "An error occurred while creating the account.", "refCodePlaceholder": "Referral code (Optional)", "refCodeLabel": "Referral code (Optional)", "namePlaceholder": "Full name/Nickname", "nameLabel": "Full name/Nickname", "nameRequired": "Full name is required", "emailExists": "Email already registered", "emailRequired": "Email is required", "phoneRequired": "Phone number is required", "phoneExists": "Phone number already registered", "confirmPasswordLabel": "Confirm password", "passwordMismatch": "Passwords do not match", "confirmPasswordRequired": "Confirm password is required", "addressPlaceholder": "Address (Optional)", "addressLabel": "Address (Optional)", "defaultAddressName": "Initial address of", "agreeToTerms": "I have read and agree to the terms and conditions", "termsRequired": "Please agree to the terms and conditions", "loginWithGoogle": "Login with Google", "loginWithApple": "Login with Apple", "termsAndConditions": "termsAndConditions"}, "profile": {"saveChangesConfirm": "Are you sure you want to save changes?", "title": "Profile", "editInfo": "Edit Information", "settings": "Settings", "personalInfo": "Personal Information", "accountSettings": "Account <PERSON><PERSON>", "notifications": "Notifications", "security": "Security", "privacy": "Privacy", "language": "Language", "account": "Account", "biometricSetting": "Biometric Settings", "accountVerification": "Account Verification", "twoFactorAuth": "Two-Factor Authentication", "favoriteProducts": "Favorite Products", "shippingInfo": "Shipping Information", "policy": "Policy", "logout": "Logout", "loginRedirect": "Go to Login", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemDefault": "System Default", "name": "Name/ Nickname", "email": "Email", "phone": "Phone", "address": "Address", "bio": "Bio", "avatar": "Avatar", "changeAvatar": "Change Avatar", "removeAvatar": "Remove Avatar", "saveChanges": "Save Changes", "cancelChanges": "Cancel Changes", "deleteAccount": "Delete Account", "deleteAccountConfirm": "Are you sure you want to delete your account?", "deleteAccountWarning": "All data will be deleted and cannot be recovered.", "accountDeleted": "Your account has been deleted successfully", "faq": "Frequently Asked Questions", "faqCategory": "FAQ Categories", "faqCategoryLabel": "Question categories", "birthdate": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "rank": "Rank", "rankPrefix": "Rank", "rankNormal": "Normal", "rankVIP": "VIP", "accountCreationDate": "Account Creation Date", "createPassword": "Create Password", "updatePassword": "Update Password", "selectBirthdate": "Select birthdate", "invalidPhone": "Invalid phone number", "phoneRequired": "Phone number is required"}, "purchase": {"history": "Purchase History", "details": "Purchase Details", "date": "Purchase Date", "amount": "Amount", "status": "Status", "orderNumber": "Order Number", "paymentMethod": "Payment Method", "billingInfo": "Billing Information", "noOrders": "No orders found", "orderStatus": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "cancelled": "Cancelled", "refunded": "Refunded", "failed": "Failed"}, "receipt": "Receipt", "invoice": "Invoice", "downloadInvoice": "Download Invoice", "viewDetails": "View Details"}, "community": {"posts": "Posts", "comments": "Comments", "likes": "<PERSON>s", "share": "Share", "report": "Report", "follow": "Follow", "following": "Following", "followers": "Followers", "groups": "Groups", "createGroup": "Create Group", "joinGroup": "Join Group", "leaveGroup": "Leave Group", "members": "Members", "admin": "Admin", "moderator": "Moderator", "member": "Member", "post": "Post", "createPost": "Create Post", "editPost": "Edit Post", "deletePost": "Delete Post", "comment": "Comment", "reply": "Reply", "writeComment": "Write a comment...", "writePost": "What's on your mind?", "postPlaceholder": "Share your thoughts...", "addPhoto": "Add Photo", "addVideo": "Add Video", "trending": "Trending", "latest": "Latest", "popular": "Popular", "noPostsYet": "No posts yet", "beTheFirstToPost": "Be the first to post!", "reportReason": "Reason for reporting", "reportSubmitted": "Report submitted", "blockUser": "Block User", "unblockUser": "Unblock User", "blockedUsers": "Blocked Users", "activities": "Community\nActivities", "featureInDevelopment": "Feature in development", "reportPost": "Report post", "actions": "Actions", "confirm": "Confirm", "moderation": "Moderation", "groupName": "Group Name", "groupImage": "Group Image", "tabs": {"home": "Home", "explore": "Explore", "group": "Group", "chat": "Cha<PERSON>", "notify": "Notify", "popular": "Popular", "forYou": "For you", "following": "Following", "bookmark": "Bookmark"}}, "notification": {"title": "Notifications", "all": "All", "unread": "Unread", "readAll": "Read all", "markAllAsRead": "All notifications marked as read", "noNotifications": "You have no notifications"}, "shop": {"editTitle": "Edit Shop", "registerTitle": "Register Shop", "storeNamePlaceholder": "Enter your store name", "phonePlaceholder": "Enter store phone number", "emailPlaceholder": "Email", "descriptionPlaceholder": "Brief description", "statusPlaceholder": "Select status", "addressPlaceholder": "Enter your address", "confirmTitle": "Confirm", "confirmEdit": "Are you sure you want to edit the shop?", "confirmRegister": "Are you sure you want to register the shop?", "editButton": "Edit", "registerButton": "Register", "editSuccessMessage": "Shop edited successfully", "registerSuccessMessage": "Shop registered successfully", "phoneInvalidMessage": "Invalid phone number", "emailInvalidMessage": "Invalid email", "statusOptions": {"initial": "Initial", "active": "Active"}}, "app": {"name": "Chainivo", "version": "Version", "about": "About", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "contactUs": "Contact Us", "feedback": "<PERSON><PERSON><PERSON>", "rateApp": "Rate App", "shareApp": "Share App", "update": "Update", "newVersionAvailable": "New version available", "updateNow": "Update Now", "later": "Later", "loading": "Loading", "error": "Error", "retry": "Retry", "offline": "You are offline", "checkConnection": "Please check your internet connection", "reconnect": "Reconnect", "sessionExpired": "Session expired", "loginAgain": "Please login again", "maintenance": "Maintenance", "maintenanceMessage": "The app is currently under maintenance. Please try again later."}, "call": {"calling": "Calling...", "incomingCall": "Incoming call", "ringing": "Ringing...", "connecting": "Connecting...", "ended": "Call ended", "failed": "Call failed", "timeout": "Call timeout", "mute": "Mute", "unmute": "Unmute", "speakerOn": "Speaker on", "speakerOff": "Speaker off"}, "category": {"title": "Product Categories", "productCategories": "Product Categories", "loadError": "Unable to load categories. Please try again.", "noCategories": "No categories available", "allCategories": "All Categories"}, "chart": {"revenueReport": "Revenue Report", "productReport": "Product Report", "orderReport": "Order Report", "todayRevenue": "Today's Revenue", "weekRevenue": "This Week's Revenue", "monthRevenue": "This Month's Revenue", "sixMonthRevenue": "Last 6 Months Revenue", "yearRevenue": "This Year's Revenue", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "revenue": "Revenue", "commission": "Commission", "details": "Details", "cancelledOrders": "Cancelled Orders", "completedOrders": "Completed Orders", "newOrders": "New Orders", "processingOrders": "Processing Orders", "orders": "orders", "week": "Week", "month": "Month", "year": "Year", "last7Days": "Last 7 days", "last1Month": "Last 1 month", "last3Months": "Last 3 months", "last6Months": "Last 6 months", "last1Year": "Last 1 year"}, "chat": {"avatarColorDemo": "Avatar Color Demo", "testRandomColors": "Test random colors for avatar fallback", "largeAvatars": "Large Avatars (120px)", "mediumAvatars": "Medium Avatars (80px)", "smallAvatars": "Small Avatars (50px)", "colorPalette": "Color Palette", "consistencyTest": "Consistency Test", "consistencyDescription": "Same name must have same color each render", "selectEmoji": "Select emoji", "smileFaces": "Smile Faces", "emotions": "Emotions", "activities": "Activities", "hearts": "Hearts", "objects": "Objects", "cancel": "Cancel", "selectMembers": "Select Members", "done": "Done", "selected": "Selected", "searchUsers": "Search users...", "noUsersFound": "No users found", "loadingUsers": "Loading user list...", "searching": "Searching...", "loadingMore": "Loading more...", "missedCall": "Missed call", "seconds": "seconds", "minutes": "minutes", "noCallHistory": "No call history yet", "unknown": "Unknown"}}