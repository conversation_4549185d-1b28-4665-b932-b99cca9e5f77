buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "1.9.24"
        googlePlayServicesAuthVersion = "20.7.0"
        
        // Add RNSVG version explicitly
        rnsvgVersion = "15.12.0"
    }
    repositories {
        google()
        mavenCentral()
        // Add multiple mirrors for better connectivity
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://jcenter.bintray.com/' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        maven { url 'https://repository.jboss.org/nexus/content/repositories/public/' }
        maven { url 'https://repo.spring.io/milestone' }
        // Additional repositories
        maven { url 'https://jitpack.io' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        gradlePluginPortal()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.google.gms:google-services:4.4.2")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
        // Primary mirrors for better connectivity
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://jcenter.bintray.com/' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        maven { url 'https://repository.jboss.org/nexus/content/repositories/public/' }
        maven { url 'https://repo.spring.io/milestone' }
        maven { url 'https://www.jitpack.io' }
        // KSP specific repositories
        maven { url 'https://plugins.gradle.org/m2/' }
        maven { url 'https://maven.google.com' }
        // Additional repositories
        maven { url 'https://jitpack.io' }
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots/' }
        gradlePluginPortal()
    }
}
apply plugin: "com.facebook.react.rootproject"
