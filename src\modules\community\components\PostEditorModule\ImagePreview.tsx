import React from 'react';
import {View, Text, FlatList, TouchableOpacity} from 'react-native';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {ImageItem} from './types';
import {styles} from './styles';
import FastImage from '@d11/react-native-fast-image';

type ImagePreviewProps = {
  selectedImages: ImageItem[];
  handleRemoveImage: (imageId: string) => void;
  handlePickImages: () => void;
  maxImages: number;
};

const ImagePreview: React.FC<ImagePreviewProps> = ({
  selectedImages,
  handleRemoveImage,
  handlePickImages,
  maxImages,
}) => {
  if (selectedImages.length === 0) return null;

  return (
    <View style={styles.imagePreviewContainer}>
      <Text style={styles.imagePreviewTitle}>
        Ảnh đã chọn ({selectedImages.length})
      </Text>
      <FlatList
        data={selectedImages}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={item => item.id}
        renderItem={({item}) => (
          <View style={styles.imageContainer}>
            <FastImage source={{uri: item.uri}} style={styles.previewImage} />
            <TouchableOpacity
              style={styles.removeImageButton}
              onPress={() => handleRemoveImage(item.id)}>
              <FontAwesomeIcon
                icon={faMinusCircle}
                size={20}
                color="#667994"
                style={{backgroundColor: '#fff', borderRadius: 20}}
              />
            </TouchableOpacity>
          </View>
        )}
        ListFooterComponent={
          selectedImages.length < maxImages ? (
            <TouchableOpacity
              style={styles.addMoreImagesButton}
              onPress={handlePickImages}>
              <Text style={styles.addMoreImagesText}>+</Text>
            </TouchableOpacity>
          ) : null
        }
      />
    </View>
  );
};

export default ImagePreview;
