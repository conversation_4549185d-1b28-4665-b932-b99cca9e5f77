import {useState, useEffect} from 'react';
import {Title} from '../../../../Config/Contanst';

interface UseOrderDataProps {
  orderInfo: any;
  typeCard: string;
}

interface UseOrderDataReturn {
  numberCard: number;
}

export const useOrderData = ({
  orderInfo,
  typeCard,
}: UseOrderDataProps): UseOrderDataReturn => {
  const [numberCard, setNumberCard] = useState<number>(0);

  useEffect(() => {
    if (orderInfo && typeCard) {
      let currentOrderCount = 0;
      
      switch (typeCard) {
        case Title.New:
          currentOrderCount = orderInfo?.NewOrder?.data?.length || 0;
          break;
        case Title.Processing:
          currentOrderCount = orderInfo?.ProcessOrder?.data?.length || 0;
          break;
        case Title.Done:
          currentOrderCount = orderInfo?.DoneOrder?.data?.length || 0;
          break;
        case Title.Cancel:
          currentOrderCount = orderInfo?.CancelOrder?.data?.length || 0;
          break;
        default:
          currentOrderCount = 0;
      }
      
      setNumberCard(currentOrderCount);
    }
  }, [orderInfo, typeCard]);

  return {
    numberCard,
  };
};
