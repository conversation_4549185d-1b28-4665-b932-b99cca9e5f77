import React from 'react';
import {InforHeader} from '../../../../Layout/headers/inforHeader';
import {TransactionType} from 'Config/Contanst';

interface TransferHeaderProps {
  type: TransactionType;
  onBack: () => void;
}

const TransferHeader: React.FC<TransferHeaderProps> = ({type, onBack}) => {
  const getTitle = () => {
    return type === TransactionType.tranfer ? 'Chuyển Point' : 'Rút Point';
  };

  return <InforHeader onBack={onBack} title={getTitle()} />;
};

export default TransferHeader;
