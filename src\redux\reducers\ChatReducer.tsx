import {createSlice, PayloadAction, Dispatch} from '@reduxjs/toolkit';
import {
  ChatState,
  ChatRoom,
  ChatMessage,
  CreateGroupRequest,
} from '../../modules/chat/types/ChatTypes';
import ChatAPI from '../../modules/chat/services/ChatAPI';
import SocketService from '../../modules/chat/services/SocketService';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import store from '../store/store';
import {DataController} from '../../base/baseController';

const initialState: ChatState = {
  rooms: [],
  unreadCount: 0,
  currentRoom: null,
  messages: {},
  loading: false,
  error: null,
  isConnected: false,
  userId: undefined,
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    setUserId: (state, action: PayloadAction<string | undefined>) => {
      state.userId = action.payload;
    },
    setChatRooms: (state, action: PayloadAction<ChatRoom[]>) => {
      state.rooms = action.payload;
    },
    addChatRoom: (state, action: PayloadAction<ChatRoom>) => {
      const existingIndex = state.rooms.findIndex(
        room => room.id === action.payload.id,
      );
      if (existingIndex >= 0) {
        state.rooms[existingIndex] = action.payload;
      } else {
        state.rooms.unshift(action.payload);
      }
    },
    updateChatRoom: (state, action: PayloadAction<ChatRoom>) => {
      const index = state.rooms.findIndex(
        room => room.id === action.payload.id,
      );

      if (index >= 0) {
        state.rooms[index] = action.payload;
      } else {
        state.rooms.unshift(action.payload);
      }
    },
    setCurrentRoom: (state, action: PayloadAction<ChatRoom | null>) => {
      state.currentRoom = action.payload;
    },
    setMessages: (
      state,
      action: PayloadAction<{roomId: string; messages: ChatMessage[]}>,
    ) => {
      state.messages[action.payload.roomId] = action.payload.messages;
    },
    addMessage: (
      state,
      action: PayloadAction<{roomId: string; message: ChatMessage}>,
    ) => {
      const {roomId, message} = action.payload;
      if (!state.messages[roomId]) {
        state.messages[roomId] = [];
      }

      // Kiểm tra xem tin nhắn đã tồn tại chưa
      const existingIndex = state.messages[roomId].findIndex(
        msg => msg.Id === message.Id,
      );
      if (existingIndex >= 0) {
        state.messages[roomId][existingIndex] = message;
      } else {
        state.messages[roomId].unshift(message);
      }

      // Cập nhật lastMessage trong room
      const roomIndex = state.rooms.findIndex(
        room => (room.Id || room.id) === roomId,
      );
      if (roomIndex >= 0) {
        state.rooms[roomIndex].LastMessage =
          message.Type === 2
            ? 'Hình ảnh'
            : message.Type === 3
            ? 'File đính kèm'
            : message.Content;
      }
    },
    markMessageAsRead: (
      state,
      action: PayloadAction<{
        roomId: string;
        messageId: string;
        readBy: string;
        readAt: number;
      }>,
    ) => {
      const {roomId, messageId, readBy, readAt} = action.payload;
      if (state.messages[roomId]) {
        const messageIndex = state.messages[roomId].findIndex(
          msg => msg.Id === messageId,
        );
        if (messageIndex >= 0) {
          const message = state.messages[roomId][messageIndex];
          message.IsRead = true;
          message.readAt = readAt;
          if (!message.readBy) {
            message.readBy = [];
          }
          if (!message.readBy.includes(readBy)) {
            message.readBy.push(readBy);
          }
        }
      }
    },
    updateMessage: (
      state,
      action: PayloadAction<{roomId: string; message: ChatMessage}>,
    ) => {
      const {roomId, message} = action.payload;
      if (state.messages[roomId]) {
        const index = state.messages[roomId].findIndex(
          msg => msg.Id === message.Id,
        );
        if (index >= 0) {
          state.messages[roomId][index] = message;
        }
      }
    },
    clearMessages: (state, action: PayloadAction<string>) => {
      delete state.messages[action.payload];
    },
    setUnreadCount: (state, action: PayloadAction<number>) => {
      state.unreadCount = action.payload;
    },
    incrementUnreadCount: state => {
      state.unreadCount += 1;
    },
    resetUnreadCount: state => {
      state.unreadCount = 0;
    },
    decrementUnreadCount: (state, action: PayloadAction<number>) => {
      state.unreadCount -= action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setConnected,
  setUserId,
  setChatRooms,
  addChatRoom,
  updateChatRoom,
  setCurrentRoom,
  setMessages,
  addMessage,
  markMessageAsRead,
  updateMessage,
  clearMessages,
  setUnreadCount,
  incrementUnreadCount,
  decrementUnreadCount,
  resetUnreadCount,
} = chatSlice.actions;

// Async actions - CHỈ CẬP NHẬT REDUX STATE, KHÔNG TẠO CONNECTION
export const connectSocket =
  (userId: string, Name: string) => async (dispatch: Dispatch) => {
    try {
      console.log('🔌 [ChatReducer] Updating Redux state for user:', userId);

      // CHỈ cập nhật Redux state, không tạo socket connection
      // Socket connection được quản lý bởi SocketConnectionManager
      dispatch(setConnected(true));
      dispatch(setUserId(userId));

      console.log('✅ [ChatReducer] Redux state updated successfully');

      // Setup socket listeners nếu cần
      // SocketService.onNewMessage((message) => {
      //   dispatch(addMessage({ roomId: message.user._id.toString(), message }));
      // });

      // SocketService.onRoomUpdate((room) => {
      //   dispatch(updateChatRoom(room));
      // });
    } catch (error) {
      console.error('❌ [ChatReducer] Failed to update Redux state:', error);
      dispatch(setError('Không thể cập nhật trạng thái chat'));
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: 'Không thể cập nhật trạng thái chat',
      });
    }
  };

export const disconnectSocket = () => (dispatch: Dispatch) => {
  SocketService.disconnect();
  dispatch(setConnected(false));
  dispatch(setUserId(undefined)); // Clear userId khi disconnect
};
export const fetchChatRoomsForUser =
  (userId: string, page: number = 1) =>
  async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading(true));
      const response = await ChatAPI.getChatRoomsForUser(userId, page);
      const currentUser = store.getState().customer.data;
      if (response.Customer) {
        const customer = response.Customer;
        const messageController = new DataController('Message');
        const chatRomIds = response.data.map((item: any) => item.Id);
        const messageResponse = await messageController.getListSimple({
          query: `@ChatRoomId:{${chatRomIds.join(
            ' | ',
          )}} -@CustomerId: {${userId}} -@IsRead: {true}`,
          returns: [
            'Id',
            'Content',
            'DateCreated',
            'Type',
            'FileUrl',
            'ChatRoomId',
            'CustomerId',
          ],
        });
        response.data = response.data.map((item: any) => {
          const cus = customer.find(
            (customer: any) => customer.Id == item.CustomerId,
          );
          const unreadCount = messageResponse.data.filter(
            (message: any) => message.ChatRoomId === item.Id,
          ).length;
          if (unreadCount > 0) {
            debugger;
          }
          return {
            ...item,
            Name: currentUser.Id == cus.Id ? item.Name : cus.Name,
            Avatar: currentUser.Id == cus.Id ? item.Avatar : cus.AvatarUrl,
            unreadCount: unreadCount ?? 0,
          };
        });
      }
      if (page === 1) {
        dispatch(setChatRooms(response.data));
      } else {
        // Append for pagination
      }
    } catch (error) {
      console.error('Error fetching user chat rooms:', error);
      dispatch(setError('Không thể tải danh sách chat'));
    } finally {
      dispatch(setLoading(false));
    }
  };

export const fetchMessages =
  (roomId: string, page: number = 1) =>
  async (dispatch: Dispatch) => {
    try {
      const response = await ChatAPI.getMessages(roomId, page);

      if (page === 1) {
        dispatch(setMessages({roomId, messages: response.data}));
      } else {
        // Append older messages for pagination
        const {messages} = chatSlice.getInitialState();
        const existingMessages = messages[roomId] || [];
        dispatch(
          setMessages({
            roomId,
            messages: [...existingMessages, ...response.data],
          }),
        );
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      dispatch(setError('Không thể tải tin nhắn'));
    }
  };

export const createPrivateRoom =
  (participantId: string) => async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading(true));
      const room = await ChatAPI.createPrivateRoom(participantId);
      dispatch(addChatRoom(room));
      return room;
    } catch (error) {
      console.error('Error creating private room:', error);
      dispatch(setError('Không thể tạo cuộc trò chuyện'));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };

export const createGroupRoom =
  (groupData: CreateGroupRequest) => async (dispatch: Dispatch) => {
    try {
      dispatch(setLoading(true));
      const room = await ChatAPI.createGroupRoom(groupData);
      dispatch(addChatRoom(room));
      return room;
    } catch (error) {
      console.error('Error creating group room:', error);
      dispatch(setError('Không thể tạo nhóm chat'));
      throw error;
    } finally {
      dispatch(setLoading(false));
    }
  };
export const getMessageUnreadCount = () => async (dispatch: Dispatch) => {
  try {
    const messageController = new DataController('Message');
    const chatRoomController = new DataController('ChatRoom');
    const userId = store.getState().customer.data?.Id;
    //lấy tất cả room chat
    await chatRoomController
      .getListSimple({
        query: `@CustomerId:{${userId}} | @Members:(*${userId}*)`,
        size: 0,
        returns: ['Id'],
      })
      .then(response => {
        if (response.code === 200) {
          const roomIds = response.data.map((item: any) => item.Id);
          //lấy tất cả tin nhắn trong các room chat
          messageController
            .getListSimple({
              query: `@ChatRoomId:{${roomIds.join(
                ' | ',
              )}} -@CustomerId: {${userId}} -@IsRead: {true}`,
              returns: ['Id'],
            })
            .then(response => {
              if (response.code === 200) {
                dispatch(setUnreadCount(response.totalCount));
              }
            });
        }
      });
  } catch (error) {
    console.error('Error fetching unread count:', error);
    dispatch(setError('Không thể tải số tin nhắn chưa đọc'));
  }
};
export const incrementUnreadCountTotal = () => async (dispatch: Dispatch) => {
  try {
    dispatch(incrementUnreadCount());
  } catch (error) {
    console.error('Error marking as read:', error);
  }
};
export const decrementUnreadCountTotal =
  (roomId: string) => async (dispatch: Dispatch) => {
    try {
      //lấy hết tin nhắn chưa đọc trong room
      const messageController = new DataController('Message');
      const userId = store.getState().customer.data?.Id;
      await messageController
        .getListSimple({
          query: `@ChatRoomId:{${roomId}} -@CustomerId: {${userId}} -@IsRead: {true}`,
          returns: ['Id'],
        })
        .then(response => {
          if (response.code === 200) {
            //update trạng thái đã đọc của tin nhắn
            messageController
              .edit(
                response.data.map((item: any) => ({Id: item.Id, IsRead: true})),
              )
              .then(response => {
                if (response.code === 200) {
                  //cập nhật lại số tin nhắn chưa đọc
                  dispatch(decrementUnreadCount(response.totalCount));
                }
              });
          }
        });
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  };

export default chatSlice.reducer;
