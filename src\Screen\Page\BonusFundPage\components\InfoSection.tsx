import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';
import {RewardFund} from '../../../../redux/types/rewardFundTypes';

interface InfoSectionProps {
  startDate: string;
  endDate: string;
  loadingData: boolean;
  rewardFund: RewardFund | null;
  totalTransactionCount?: number;
}

const InfoSection: React.FC<InfoSectionProps> = ({
  startDate,
  endDate,
  loadingData,
  rewardFund,
  totalTransactionCount = 0,
}) => {
  // Hiển thị tên quỹ thưởng từ RewardFund hoặc fallback
  const fundName =
    rewardFund?.Name ||
    `Quỹ thưởng tháng ${startDate.split('/')[1]}/${startDate.split('/')[2]}`;

  // Hi<PERSON>n thị trạng thái quỹ thưởng
  const getStatusText = (status?: number) => {
    if (!status) return 'Chưa xác định';
    if (status === 1) return 'Chưa chia thưởng';
    if (status === 2) return 'Đã chia thưởng';
    return 'Chưa xác định';
  };

  const getStatusStyle = (status?: number) => {
    if (!status) return styles.statusNot;
    if (status === 1) return styles.statusActive;
    if (status === 2) return styles.statusCompleted;
    return styles.statusNot;
  };

  return (
    <View style={styles.infoSection}>
      <View style={styles.infoCard}>
        <Text style={styles.infoTitle}>{fundName}</Text>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Thời gian:</Text>
          <Text style={styles.infoValue}>
            {startDate} - {endDate}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Tổng điểm quỹ:</Text>
          <Text style={styles.infoValue}>
            {loadingData
              ? 'Đang tải...'
              : `${Ultis.money(rewardFund?.Value || 0)} điểm`}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Trạng thái:</Text>
          <Text style={[styles.infoValue, getStatusStyle(rewardFund?.Status)]}>
            {getStatusText(rewardFund?.Status)}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Text style={styles.infoLabel}>Tổng giao dịch:</Text>
          <Text style={styles.infoValue}>
            {loadingData ? 'Đang tải...' : `${totalTransactionCount} giao dịch`}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  infoSection: {
    paddingHorizontal: 16,
    marginVertical: 16,
  },
  infoCard: {
    backgroundColor: ColorThemes.light.white,
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  infoTitle: {
    ...TypoSkin.medium1,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 16,
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    flex: 1,
  },
  infoValue: {
    ...TypoSkin.medium1,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 1,
    textAlign: 'right',
  },
  statusNot: {
    color: ColorThemes.light.neutral_text_disabled_color,
    fontWeight: '600',
  },
  statusActive: {
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  statusCompleted: {
    color: ColorThemes.light.secondary2_main_color,
    fontWeight: '600',
  },
});

export default InfoSection;
