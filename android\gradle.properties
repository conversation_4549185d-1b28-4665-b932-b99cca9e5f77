# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
# Increased memory allocation for React Native 0.78 and complex builds
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
# Limiting to arm64-v8a only to reduce path length issues on Windows
reactNativeArchitectures=arm64-v8a

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=true

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

# Additional properties to help with Windows path length issues
android.enableJetifier=true
org.gradle.caching=true
org.gradle.configureondemand=true
android.enableDexingArtifactTransform.desugaring=true

# Node.js path configuration for react-native-svg and other packages
react.native.node.path=/Users/<USER>/.nvm/versions/node/v22.17.1/bin/node
# Additional Node.js configurations
react.native.node.dir=/Users/<USER>/.nvm/versions/node/v22.17.1
react.native.node.executable.path=/Users/<USER>/.nvm/versions/node/v22.17.1/bin/node

# Set PATH to include Node.js for Gradle processes
systemProp.PATH=/Users/<USER>/.nvm/versions/node/v22.17.1/bin:/usr/local/bin:/usr/bin:/bin

# OkHttp version for react-native-video
OKHTTP_VERSION=4.12.0

# Network timeout settings
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
systemProp.https.connectionTimeout=300000
systemProp.https.socketTimeout=300000

# Additional network settings for Maven repositories
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.maxConnectionsPerRoute=5

# Gradle daemon settings
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# Network retry settings
systemProp.http.maxRedirects=10
systemProp.https.maxRedirects=10

MYAPP_UPLOAD_STORE_FILE=my-upload-key.keystore
MYAPP_UPLOAD_KEY_ALIAS=upload
MYAPP_UPLOAD_STORE_PASSWORD=adminchainivo
MYAPP_UPLOAD_KEY_PASSWORD=adminchainivo