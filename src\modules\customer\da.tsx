import {Customer} from 'redux/types/customerType';
import {DataController} from '../../base/baseController';
import {
  FollowStatus,
  StorageContanst,
  TransactionStatus,
  TransactionType,
} from '../../Config/Contanst';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {randomGID} from '../../utils/Utils';
import WalletDA from 'modules/wallet/walletDa';
import ConfigAPI from 'Config/ConfigAPI';
import {ADMIN_MOBILE, ADMIN_NAME} from 'modules/customerRank/Da/customerRankDa';

export class CustomerDA {
  private customerController: DataController;
  private followerController: DataController;
  private orderDetailController: DataController;
  private addressController: DataController;

  constructor() {
    this.customerController = new DataController('Customer');
    this.followerController = new DataController('Customer_Follower');
    this.orderDetailController = new DataController('OrderDetail');
    this.addressController = new DataController('Address');
  }
  private customerCache: Map<string, any> = new Map();

  async pointPlusRefCode(customer: Customer) {
    // người giới thiệu F1 30P, người giới thiệu F0 10P
    // lấy thông tin người giới thiệu
    let referrerFirst: Customer | null = null; // người giới thiệu F0
    let referrerSecond: Customer | null = null; // người giới thiệu F1

    if (!customer.ParentId) {
      return {referrerFirst, referrerSecond};
    }
    debugger;
    const res = await this.customerController.getById(customer.ParentId);
    if (res.code === 200 && res.data.Id) {
      referrerSecond = res.data;
      if (referrerSecond && referrerSecond.ParentId) {
        const res2 = await this.customerController.getById(
          referrerSecond.ParentId,
        );
        if (res2.code === 200 && res2.data.Id) {
          referrerFirst = res2.data;
        }
      }
    }
    const walletDa = new WalletDA();
    // cộng điểm POINT cho first 10, second 30
    if (referrerFirst) {
      // cộng điểm cho first
      await walletDa.transferPoints({
        senderId: ConfigAPI.adminCHAINIVO,
        senderName: ADMIN_NAME,
        senderMobile: ADMIN_MOBILE,
        recipientId: referrerFirst.Id,
        recipientName: referrerFirst.Name,
        recipientMobile: referrerFirst.Mobile,
        amount: 10,
        type: TransactionType.hoahong,
        status: TransactionStatus.success,
        descriptionSender: `Chuyển Point người giới thiệu`,
        descriptionRecipient: `Nhận Point cho người giới thiệu từ ${ADMIN_NAME}`,
      });
    }
    if (referrerSecond) {
      // cộng điểm cho second
      await walletDa.transferPoints({
        senderId: ConfigAPI.adminCHAINIVO,
        senderName: ADMIN_NAME,
        senderMobile: ADMIN_MOBILE,
        recipientId: referrerSecond.Id,
        recipientName: referrerSecond.Name,
        recipientMobile: referrerSecond.Mobile,
        amount: 30,
        type: TransactionType.hoahong,
        status: TransactionStatus.success,
        descriptionSender: `Chuyển Point người giới thiệu`,
        descriptionRecipient: `Nhận Point cho người giới thiệu từ ${ADMIN_NAME}`,
      });
    }
  }

  async getCustomerName(id: string) {
    if (this.customerCache.has(id)) {
      return this.customerCache.get(id).name || '';
    }
    const customerResult = await this.customerController.getById(id);
    if (customerResult.code === 200) {
      this.customerCache.set(id, customerResult.data);
      return customerResult.data.name;
    }
    return '';
  }
  async getCustomerItem(id: string) {
    const customerResult = await this.customerController.getById(id);
    if (customerResult.code === 200) {
      return customerResult.data;
    }
    return null;
  }

  async getCustomerbyId(id: string) {
    if (this.customerCache.has(id)) {
      return {code: 200, data: this.customerCache.get(id)};
    }
    const customerResult = await this.customerController.getById(id);
    if (customerResult.code === 200) {
      this.customerCache.set(id, customerResult.data);
      return customerResult;
    }
    return null;
  }
  async getCustomersByIds(ids: string[]) {
    if (ids.length === 0) {
      return {code: 200, data: []};
    }
    const cachedResults = ids
      .filter(id => this.customerCache.has(id))
      .map(id => this.customerCache.get(id));
    const missingIds = ids.filter(id => !this.customerCache.has(id));
    if (missingIds.length === 0) {
      return {code: 200, data: cachedResults};
    }
    const customerResult = await this.customerController.getListSimple({
      query: `@Id: {${missingIds.join(' | ')}}`,
    });

    if (customerResult.code === 200) {
      customerResult.data.forEach((item: any) =>
        this.customerCache.set(item.Id, item),
      );
      return {code: 200, data: [...cachedResults, ...customerResult.data]};
    }
    return null;
  }

  async getFollowCustomer(id: string) {
    const followingResult = await this.followerController.getListSimple({
      query: `@CustomerId: (${id}) @Status:[1]`,
    });
    const followerResult = await this.followerController.getListSimple({
      query: `@Following: (${id}) @Status:[1]`,
    });
    if (followingResult.code === 200 && followerResult.code === 200) {
      return {
        following: followingResult.data || [],
        follower: followerResult.data || [],
      };
    }
    return null;
  }

  //lấy danh sách bạn bè và những người đang gửi yêu cầu kết bạn cho mình
  async getListFriend(
    Id: string,
    page?: number,
    size?: number,
    search?: string,
  ) {
    // var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    //(@CustomerId: (${Id}) @Status:[${FollowStatus.Pending}]) <- mình gửi kết bạn cho ng khác và trạng thái đang pending
    // lấy danh sách bạn bè của người dùng trường hợp mình gửi yêu cầu kết bạn và đã xác nhận thành công, người khác gửi yêu cầu đến mình và đã xác nhận thành công, người khác gửi yêu cầu đến mình và đang chờ xác nhận
    var query = `(@CustomerId: (${Id}) @Status:[${FollowStatus.Accept}]) | (@Following: (${Id}) @Status:[${FollowStatus.Accept}]) | (@Following: (${Id}) @Status:[${FollowStatus.Pending}])`;
    const followingResult = await this.followerController.getListSimple({
      query: query,
    });
    if (followingResult.code === 200) {
      const listfollow = followingResult.data.map(
        (item: any) => item.Following,
      );
      const listcustomer = followingResult.data.map(
        (item: any) => item.CustomerId,
      );
      var listId = [...listfollow, ...listcustomer];
      listId = [...new Set(listId)].filter((item: any) => item !== Id);
      var querySearch = `@Id: {${listId.join(' | ')}}`;
      if (search) querySearch += ` @Name:(*${search ?? ''}*)`;
      const customerResult = await this.customerController.getListSimple({
        query: querySearch,
      });
      if (customerResult.code === 200) {
        const listUpdate = customerResult.data.map((item: any) => {
          return {
            ...item,
            Status: followingResult.data.find(
              (a: any) => a.Following === item.Id || a.CustomerId === item.Id,
            )?.Status,
          };
        });
        return listUpdate;
      }
    }
    return null;
  }
  //lấy danh sách đã là bạn bè

  async checkFollowCustomer(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const followingResult = await this.followerController.getListSimple({
      query: `(@CustomerId: (${cusId}) | @Following:(${cusId})) (@Following:(${id}) | @CustomerId:(${id}))`,
      size: 1,
    });
    if (followingResult.code === 200 && followingResult.data?.length > 0) {
      return followingResult.data[0];
    }
    return null;
  }

  async follow(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const data = {
        Id: randomGID(),
        CustomerId: cusId,
        Following: id,
        DateCreated: new Date().getTime(),
        Status: FollowStatus.Pending,
      };
      const customerResult = await this.followerController.add([data]);
      if (customerResult.code === 200) {
        return data;
      }
    }
    return null;
  }
  async Acceptfollow(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const IdFollow = await this.followerController.getListSimple({
        page: 1,
        size: 1,
        query: `@CustomerId: (${id}) @Following:(${cusId})`,
      });
      if (IdFollow?.data?.length > 0) {
        const customerResult = await this.followerController.edit([
          {
            ...IdFollow?.data[0],
            Status: FollowStatus.Accept,
          },
        ]);
        if (customerResult.code === 200) {
          return true;
        }
      }
    }
    return false;
  }

  async unfollow(id: string) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const fl = await this.followerController.getListSimple({
        query: `@CustomerId: (${cusId}) @Following:(${id})`,
      });
      if (fl?.data) {
        const customerResult = await this.followerController.delete([
          fl.data[0].Id,
        ]);
        if (customerResult.code === 200) {
          return true;
        }
      }
    }
    return false;
  }

  // lấy danh sách tất cả người dùng
  async getAllCustomer(page: number, size: number, search?: string) {
    // change query to "(@Name:(%h%)) | (@Name:(*h*)) | (@Mobile:(*h*))"
    const query =
      search && search.trim() !== ''
        ? `@Name: (*${search}*) | @Email: (*${search}*) | @Mobile: (*${search}*)`
        : undefined;
    const customerResult = await this.customerController.getListSimple({
      page: page,
      size: size,
      query: query,
    });
    if (customerResult.code === 200) {
      return customerResult;
    }
    return null;
  }

  async getAllCustomerWithRank() {
    // Lấy tất cả customers
    const resCustomer = await this.customerController.getAll();
    if (resCustomer.code !== 200) throw new Error('Failed to get customers');

    const customers = resCustomer.data;
    if (!customers || customers.length === 0) {
      return {code: 200, data: []};
    }

    // Lấy tất cả ConfigRank (RankInfo)
    const rankController = new DataController('ConfigRank');
    const ranksResult = await rankController.getAll();
    if (ranksResult.code !== 200) {
      return {code: 200, data: customers};
    }
    const ranks = ranksResult.data;

    // Lấy tất cả CustomerRank cho các customers
    const customerRankController = new DataController('CustomerRank');
    const customerRankResult = await customerRankController.getListSimple({
      query: `@CustomerId: {${customers.map((c: any) => c.Id).join(' | ')}}`,
      page: 1,
      size: customers.length,
    });

    let customerRanks: any[] = [];
    if (customerRankResult.code === 200 && customerRankResult.data) {
      // Gắn ConfigRank vào mỗi CustomerRank
      customerRanks = customerRankResult.data.map((r: any) => ({
        ...r,
        ConfigRank: ranks.find((rank: any) => rank.Id === r.ConfigRankId),
      }));
    }

    // Gắn ConfigRank vào mỗi customer
    const customersWithRank = customers.map((customer: any) => {
      const customerRank = customerRanks.find(
        (r: any) => r.CustomerId === customer.Id,
      );

      return {
        ...customer,
        ConfigRank: customerRank?.ConfigRank || null,
        ConfigRankId: customerRank?.ConfigRankId || null,
      };
    });

    return customersWithRank;
  }

  async getCustomerWithRank({ids}: {ids: string[]}) {
    // Lấy customers theo danh sách IDs
    const customerResult = await this.customerController.getByListId(ids);
    if (customerResult.code !== 200) {
      throw new Error('Failed to get customers');
    }

    const customers = customerResult.data;
    if (!customers || customers.length === 0) {
      return {code: 200, data: []};
    }

    // Lấy tất cả ConfigRank (RankInfo)
    const rankController = new DataController('ConfigRank');
    const ranksResult = await rankController.getAll();
    if (ranksResult.code !== 200) {
      return {code: 200, data: customers};
    }
    const ranks = ranksResult.data;

    // Lấy CustomerRank cho các customers được chỉ định
    const customerRankController = new DataController('CustomerRank');
    const customerRankResult = await customerRankController.getListSimple({
      query: `@CustomerId: {${customers.map((c: any) => c.Id).join(' | ')}}`,
      page: 1,
      size: customers.length,
    });

    let customerRanks: any[] = [];
    if (customerRankResult.code === 200 && customerRankResult.data) {
      // Gắn ConfigRank vào mỗi CustomerRank
      customerRanks = customerRankResult.data.map((r: any) => ({
        ...r,
        ConfigRank: ranks.find((rank: any) => rank.Id === r.ConfigRankId),
      }));
    }

    // Gắn ConfigRank vào mỗi customer
    const customersWithRank = customers.map((customer: any) => {
      const customerRank = customerRanks.find(
        (r: any) => r.CustomerId === customer.Id,
      );

      return {
        ...customer,
        ConfigRank: customerRank?.ConfigRank || null,
        ConfigRankId: customerRank?.ConfigRankId || null,
      };
    });

    return customersWithRank;
  }
}
