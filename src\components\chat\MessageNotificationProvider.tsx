import React, { useEffect, useState } from 'react';
import MessageNotification from './MessageNotification';
import MessageNotificationService from '../../services/MessageNotificationService';
import { navigate, RootScreen } from '../../router/router';
import { ChatMessage, ChatRoom } from '../../modules/chat/types/ChatTypes';

interface MessageNotificationData {
  message: ChatMessage;
  room: ChatRoom | null;
  senderName: string;
  messagePreview: string;
}

const MessageNotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showNotification, setShowNotification] = useState(false);
  const [notificationData, setNotificationData] = useState<MessageNotificationData | null>(null);

  const handleShowMessageNotification = React.useCallback((data: MessageNotificationData) => {
    console.log('💬 handleShowMessageNotification received data:', data);
    console.log('💬 Current notification state before show:', { showNotification, notificationData });

    // Force reset state trướ<PERSON> khi show notification mới
    setShowNotification(false);
    setNotificationData(null);

    // Delay một chút để đảm bảo state được reset
    setTimeout(() => {
      setNotificationData(data);
      setShowNotification(true);
      console.log('💬 Message notification shown with new data');
    }, 100);
  }, [showNotification, notificationData]);

  const handleHideMessageNotification = React.useCallback(() => {
    console.log('💬 handleHideMessageNotification called');
    setShowNotification(false);
    setNotificationData(null);
  }, []);

  const handleTapNotification = React.useCallback((data: any) => {
    
    // Ẩn notification trước
    handleHideMessageNotification();
    
    // Navigate to chat room
    debugger
    if (data.room) {
      navigate(RootScreen.ChatRoom, { room: data.room?.data });
    }
  }, [handleHideMessageNotification]);

  useEffect(() => {
    console.log('💬 MessageNotificationProvider useEffect mounting, current state:', { showNotification, notificationData });

    // Register event listeners
    console.log('💬 Registering event listeners');
    MessageNotificationService.on('showMessageNotification', handleShowMessageNotification);
    MessageNotificationService.on('hideMessageNotification', handleHideMessageNotification);
    MessageNotificationService.on('tapNotification', handleTapNotification);
    console.log('💬 Event listeners registered, total listeners:', MessageNotificationService.listenerCount('showMessageNotification'));

    // Cleanup function
    return () => {
      console.log('💬 MessageNotificationProvider cleanup, removing event listeners');
      MessageNotificationService.off('showMessageNotification', handleShowMessageNotification);
      MessageNotificationService.off('hideMessageNotification', handleHideMessageNotification);
      MessageNotificationService.off('tapNotification', handleTapNotification);
      console.log('💬 Event listeners removed');
    };
  }, [handleShowMessageNotification, handleHideMessageNotification, handleTapNotification]);

  const handleTap = () => {
    console.log('💬 User tapped message notification');
    if (notificationData) {
      MessageNotificationService.tapNotification();
    }
  };

  const handleDismiss = () => {
    console.log('💬 User dismissed message notification');
    MessageNotificationService.hideMessageNotification();
  };

  // Debug effect để track state changes
  React.useEffect(() => {
    console.log('💬 MessageNotificationProvider state changed:', {
      showNotification,
      senderName: notificationData?.senderName,
      messagePreview: notificationData?.messagePreview
    });
  }, [showNotification, notificationData]);

  return (
    <>
      {children}

      {/* Message Notification - không block tương tác */}
      <MessageNotification
        visible={showNotification}
        message={notificationData?.message || {} as ChatMessage}
        room={notificationData?.room || null}
        senderName={notificationData?.senderName || 'Someone'}
        messagePreview={notificationData?.messagePreview || ''}
        onTap={handleTap}
        onDismiss={handleDismiss}
      />
    </>
  );
};

export default MessageNotificationProvider;
