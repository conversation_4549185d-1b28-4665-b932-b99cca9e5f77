import React from 'react';
import {View, Text, FlatList, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';
import TransactionItem from '../../MyWalletProfile/components/TransactionItem';
import {HistoryItem} from '../../MyWalletProfile/components/HistorySection';

interface Transaction {
  Id: string;
  CustomerId: string;
  Name: string;
  Mobile: string;
  Value: number;
  Description: string;
  DateCreated: number;
  Status: number;
  Type: number;
  CustomerRecive: string;
  Code: string;
}

interface TransactionListProps {
  transactions: Transaction[];
  loading: boolean;
}

const TransactionList: React.FC<TransactionListProps> = ({
  transactions,
  loading,
}) => {
  const formatDate = (timestamp: number) => {
    return Ultis.numberToTime(timestamp, true);
  };

  // Convert Transaction to HistoryItem format
  const convertToHistoryItem = (transaction: Transaction): HistoryItem => {
    return {
      id: transaction.Id,
      type: transaction.Value > 0 ? 'income' : 'expense',
      amount: Math.abs(transaction.Value),
      description:
        transaction.Description ||
        `Giao dịch từ ${transaction.Name} (${transaction.Mobile})`,
      time: formatDate(Number(transaction.DateCreated)),
    };
  };

  const renderTransactionItem = ({item}: {item: Transaction}) => {
    const historyItem = convertToHistoryItem(item);
    return <TransactionItem item={historyItem} />;
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Winicon
        src="outline/user interface/document-text"
        size={48}
        color={ColorThemes.light.neutral_text_subtitle_color}
      />
      <Text style={styles.emptyTitle}>Chưa có giao dịch</Text>
      <Text style={styles.emptySubtitle}>
        Không có giao dịch nào trong khoảng thời gian này
      </Text>
    </View>
  );

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <Text style={styles.loadingText}>Đang tải dữ liệu...</Text>
    </View>
  );

  if (loading) {
    return renderLoadingState();
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Danh sách giao dịch</Text>
        <Text style={styles.headerSubtitle}>
          {transactions.length} giao dịch
        </Text>
      </View>

      <View style={styles.listContainer}>
        <FlatList
          data={transactions}
          renderItem={renderTransactionItem}
          scrollEnabled={false}
          keyExtractor={item => item.Id}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={
            transactions.length === 0 ? styles.emptyList : undefined
          }
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  header: {
    marginBottom: 16,
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 4,
  },
  headerSubtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  listContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    ...TypoSkin.body1,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
  emptyList: {
    flexGrow: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default TransactionList;
