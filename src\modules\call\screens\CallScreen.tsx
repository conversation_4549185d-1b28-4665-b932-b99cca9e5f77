import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
  Alert,
} from 'react-native';
import {useRoute, useNavigation} from '@react-navigation/native';
import FastImage from '@d11/react-native-fast-image';
import {ColorThemes} from '../../../assets/skin/colors';
import {useWebRTCCall} from '../../../hooks/useWebRTCCall';
import {CallStatus} from '../../../services/webrtc/types/WebRTCTypes';
import ConfigAPI from '../../../Config/ConfigAPI';
import {ComponentStatus, showSnackbar, Winicon} from 'wini-mobile-components';
import {useTranslation} from '../../../locales/useTranslation';

const {width, height} = Dimensions.get('window');

interface CallScreenProps {
  route: {
    params: {
      contact: {
        Id: string;
        Name: string;
        AvatarUrl?: string;
      };
      isIncoming: boolean;
      currentUser: {
        Id: string;
        Name: string;
        AvatarUrl?: string;
      };
    };
  };
}

const CallScreen: React.FC = () => {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const navigation = useNavigation();
  const {contact, isIncoming, currentUser} = route.params || {};

  const {
    callStatus,
    currentCall,
    callTimer,
    callControls,
    acceptCall,
    rejectCall,
    endCall,
    toggleMute,
    toggleSpeaker,
    error,
  } = useWebRTCCall();

  // Format thời gian hiển thị
  const formatCallTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  // Xử lý khi call kết thúc
  useEffect(() => {
    if (
      callStatus === CallStatus.ENDED ||
      callStatus === CallStatus.FAILED ||
      callStatus === CallStatus.TIMEOUT
    ) {
      // Delay một chút trước khi quay lại
      setTimeout(() => {
        navigation.goBack();
      }, 1500);
    }
  }, [callStatus, navigation]);

  // Xử lý lỗi
  useEffect(() => {
    if (error) {
      showSnackbar({
        status: ComponentStatus.ERROR,
        message: error,
      });
    }
  }, [error]);

  // Xử lý accept call
  const handleAcceptCall = useCallback(async () => {
    try {
      await acceptCall();
    } catch (err) {
      console.error('Error accepting call:', err);
    }
  }, [acceptCall]);

  // Xử lý reject call
  const handleRejectCall = useCallback(() => {
    try {
      rejectCall();
      navigation.goBack();
    } catch (err) {
      console.error('Error rejecting call:', err);
    }
  }, [rejectCall, navigation]);

  // Xử lý end call
  const handleEndCall = useCallback(() => {
    try {
      endCall();
    } catch (err) {
      console.error('Error ending call:', err);
    }
  }, [endCall]);

  // Xử lý toggle mute
  const handleToggleMute = useCallback(() => {
    toggleMute();
  }, [toggleMute]);

  // Xử lý toggle speaker
  const handleToggleSpeaker = useCallback(() => {
    toggleSpeaker();
  }, [toggleSpeaker]);

  // Lấy trạng thái hiển thị
  const getCallStatusText = (): string => {
    switch (callStatus) {
      case CallStatus.CALLING:
        return t('call.calling');
      case CallStatus.RINGING:
        return isIncoming ? t('call.incomingCall') : t('call.ringing');
      case CallStatus.CONNECTING:
        return t('call.connecting');
      case CallStatus.CONNECTED:
        return formatCallTime(callTimer.duration);
      case CallStatus.ENDED:
        return t('call.ended');
      case CallStatus.FAILED:
        return t('call.failed');
      case CallStatus.TIMEOUT:
        return t('call.timeout');
      default:
        return t('call.connecting');
    }
  };

  // Render avatar
  const renderAvatar = () => {
    if (contact?.AvatarUrl) {
      return (
        <FastImage
          source={{uri: ConfigAPI.urlImg + contact.AvatarUrl}}
          style={styles.avatar}
        />
      );
    }

    return (
      <View style={[styles.avatar, styles.defaultAvatar]}>
        <Text style={styles.avatarText}>
          {contact?.Name?.charAt(0).toUpperCase() || '?'}
        </Text>
      </View>
    );
  };

  // Render call controls cho incoming call
  const renderIncomingCallControls = () => {
    if (callStatus !== CallStatus.RINGING || !isIncoming) return null;

    return (
      <View style={styles.incomingCallControls}>
        <TouchableOpacity
          style={[styles.callButton, styles.rejectButton]}
          onPress={handleRejectCall}
          activeOpacity={0.8}>
          <Winicon
            src="fill/user interface/phone-call"
            size={30}
            color="white"
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.callButton, styles.acceptButton]}
          onPress={handleAcceptCall}
          activeOpacity={0.8}>
          <Winicon
            src="fill/user interface/phone-call"
            size={30}
            color="white"
          />
        </TouchableOpacity>
      </View>
    );
  };

  // Render call controls cho active call
  const renderActiveCallControls = () => {
    if (callStatus !== CallStatus.CONNECTED) return null;

    return (
      <View style={styles.activeCallControls}>
        <TouchableOpacity
          style={[
            styles.controlButton,
            callControls.isMuted && styles.controlButtonActive,
          ]}
          onPress={handleToggleMute}
          activeOpacity={0.8}>
          <Winicon
            src={
              callControls.isMuted
                ? 'outline/multimedia/microphone-off'
                : 'outline/multimedia/microphone'
            }
            size={24}
            color="white"
          />
          <Text style={styles.controlButtonText}>
            {callControls.isMuted ? t('call.unmute') : t('call.mute')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.controlButton,
            callControls.isSpeakerOn && styles.controlButtonActive,
          ]}
          onPress={handleToggleSpeaker}
          activeOpacity={0.8}>
          <Winicon
            src={
              callControls.isSpeakerOn
                ? 'fill/multimedia/volume-up'
                : 'outline/multimedia/volume-up'
            }
            size={24}
            color="white"
          />
          <Text style={styles.controlButtonText}>
            {callControls.isSpeakerOn
              ? t('call.speakerOff')
              : t('call.speakerOn')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.callButton, styles.endCallButton]}
          onPress={handleEndCall}
          activeOpacity={0.8}>
          <Winicon
            src="fill/user interface/phone-call"
            size={30}
            color="white"
          />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={ColorThemes.light.primary_main_color}
      />

      <View style={styles.content}>
        {/* Avatar và thông tin người gọi */}
        <View style={styles.userInfo}>
          {renderAvatar()}
          <Text style={styles.userName}>{contact?.Name || 'Unknown'}</Text>
          <Text style={styles.callStatus}>{getCallStatusText()}</Text>
        </View>

        {/* Call controls */}
        <View style={styles.controlsContainer}>
          {renderIncomingCallControls()}
          {renderActiveCallControls()}

          {/* End call button cho outgoing call */}
          {(callStatus === CallStatus.CALLING ||
            callStatus === CallStatus.CONNECTING) && (
            <TouchableOpacity
              style={[styles.callButton, styles.endCallButton]}
              onPress={handleEndCall}
              activeOpacity={0.8}>
              <Winicon
                src="fill/user interface/phone-call"
                size={30}
                color="white"
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 60,
  },
  userInfo: {
    alignItems: 'center',
    marginTop: 80,
  },
  avatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: 30,
  },
  defaultAvatar: {
    backgroundColor: ColorThemes.light.neutral_background_color,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 60,
    fontWeight: 'bold',
    color: ColorThemes.light.primary_main_color,
  },
  userName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
    textAlign: 'center',
  },
  callStatus: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  controlsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  incomingCallControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '60%',
    marginBottom: 40,
  },
  activeCallControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '90%',
    marginBottom: 40,
    flexWrap: 'wrap',
  },
  callButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#F44336',
  },
  endCallButton: {
    backgroundColor: '#F44336',
  },

  controlButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    marginVertical: 10,
  },
  controlButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  controlButtonText: {
    fontSize: 12,
    color: 'white',
    textAlign: 'center',
  },
});

export default CallScreen;
