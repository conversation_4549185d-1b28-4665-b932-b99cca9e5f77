import React from 'react';
import {View, StyleSheet} from 'react-native';
import SearchBar from '../../../../components/shop/Search';

interface SearchSectionProps {
  setDataSearch: (value: string) => void;
}

const SearchSection: React.FC<SearchSectionProps> = ({setDataSearch}) => {
  return (
    <View style={styles.container}>
      <SearchBar setDataSearch={setDataSearch} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 10,
    paddingBottom: 10,
  },
});

export default SearchSection;
