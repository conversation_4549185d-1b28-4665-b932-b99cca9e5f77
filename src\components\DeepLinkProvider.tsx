/**
 * DeepLink Provider Component
 * Wrapper component to initialize and manage DeepLinkManager
 */

import React, {useEffect, useRef} from 'react';
import {useNavigation} from '@react-navigation/native';
import {
  NavigationContainerRef,
  NavigationContainerRefWithCurrent,
} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStackParamList} from '../Screen/Layout/navigation/ecomNavigator';
import {RootScreen} from '../router/router';
import {deepLinkManager, DeepLinkEvent} from '../services/deeplink';

interface DeepLinkProviderProps {
  children: React.ReactNode;
  navigationRef: NavigationContainerRefWithCurrent<any>;
}

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const DeepLinkProvider: React.FC<DeepLinkProviderProps> = ({
  children,
  navigationRef,
}) => {
  const navigation = useNavigation<NavigationProp>();
  const isInitialized = useRef(false);

  useEffect(() => {
    // Initialize DeepLinkManager when navigation is ready
    if (navigationRef.current && !isInitialized.current) {
      console.log('[DeepLinkProvider] Initializing DeepLinkManager...');

      deepLinkManager.initialize(navigationRef.current, {
        enableLogging: __DEV__,
        fallbackScreen: RootScreen.navigateEComView,
        retryAttempts: 2,
        retryDelay: 500,
      });

      // Add event listeners for debugging
      if (__DEV__) {
        deepLinkManager.addEventListener('url_received', handleUrlReceived);
        deepLinkManager.addEventListener(
          'navigation_success',
          handleNavigationSuccess,
        );
        deepLinkManager.addEventListener(
          'navigation_error',
          handleNavigationError,
        );
        deepLinkManager.addEventListener('parsing_error', handleParsingError);
      }

      isInitialized.current = true;
    }

    // Cleanup on unmount
    return () => {
      if (isInitialized.current) {
        console.log('[DeepLinkProvider] Cleaning up DeepLinkManager...');
        deepLinkManager.cleanup();
        isInitialized.current = false;
      }
    };
  }, [navigationRef]);

  // Event handlers for debugging
  const handleUrlReceived = (event: DeepLinkEvent, data: any) => {
    console.log('[DeepLinkProvider] URL received:', data?.url);
  };

  const handleNavigationSuccess = (event: DeepLinkEvent, data: any) => {
    console.log('[DeepLinkProvider] Navigation success:', data?.screen);
  };

  const handleNavigationError = (event: DeepLinkEvent, data: any) => {
    console.error('[DeepLinkProvider] Navigation error:', data?.error);
  };

  const handleParsingError = (event: DeepLinkEvent, data: any) => {
    console.error('[DeepLinkProvider] Parsing error:', data?.error);
  };

  // This component doesn't render anything visible
  return <>{children}</>;
};

export default DeepLinkProvider;
