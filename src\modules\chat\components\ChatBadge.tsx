import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {useChatRooms} from '../../../redux/hook/chatHook';
import {ColorThemes} from '../../../assets/skin/colors';

const ChatBadge: React.FC = () => {
  const chatRooms = useChatRooms();

  // Tính tổng số tin nhắn chưa đọc
  const totalUnreadCount = chatRooms.reduce((total, room) => {
    return total + (room.unreadCount || 0);
  }, 0);

  if (totalUnreadCount === 0) {
    return null;
  }

  return (
    <View style={styles.badge}>
      <Text style={styles.badgeText}>
        {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: ColorThemes.light.error_main_color,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 12,
    fontWeight: '600',
  },
});

export default ChatBadge;
