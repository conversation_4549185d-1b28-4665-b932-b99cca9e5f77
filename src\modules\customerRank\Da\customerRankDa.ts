import {DataController} from 'base/baseController';
import {TransactionStatus, TransactionType} from 'Config/Contanst';
import ConfigRewardDa from 'modules/configReward/da/configReward';
import ConfigRewardRankDa from 'modules/configRewardRank/da/configRewardRankDa';
import WalletDA from 'modules/wallet/walletDa';
import {ConfigReward} from 'redux/types/configReward';
import {ConfigRewardRank} from 'redux/types/configRewardRank';
import {Customer} from 'redux/types/customerType';
import {RankInfo} from 'redux/types/rankTypes';
import ConfigAPI from 'Config/ConfigAPI';
import {randomGID, Ultis} from 'utils/Utils';
import {CustomerRank} from 'redux/types/customerRank';
import {CustomerDA} from 'modules/customer/da';

export const ADMIN_NAME = 'CHAINIVOADM';
export const ADMIN_MOBILE = '';

// Add helper function to distribute referral rewards
type ReferrerInfo = {
  referrerFirst: Customer | null;
  referrerSecond: Customer | null;
  referrerChildren: Customer[];
};

type PointReward = {
  pointForReferrerSecond: number;
  pointForReferrerFirst: number;
  pointForReferrerChildren: number;
  pointForRefund: number;
  pointForAdmin: number;
};

class CustomerRankDa {
  private controller: DataController;
  private rankController: DataController;
  private customerController: DataController;
  private walletDa: WalletDA;
  private configRewardDa: ConfigRewardDa;
  private configRewardRankDa: ConfigRewardRankDa;
  private customerDa: CustomerDA;

  constructor() {
    this.controller = new DataController('CustomerRank');
    this.rankController = new DataController('ConfigRank');
    this.customerController = new DataController('Customer');
    this.walletDa = new WalletDA();
    this.configRewardDa = new ConfigRewardDa();
    this.configRewardRankDa = new ConfigRewardRankDa();
    this.customerDa = new CustomerDA();
  }

  async fetchAll() {
    const res = await this.controller.getAll();
    if (res.code === 200) {
      return res.data;
    }
    return [];
  }

  async fetchByCustomerId(customerId: string) {
    const res = await this.controller.getListSimple({
      query: `@CustomerId: {${customerId}}`,
    });
    const rank = await this.rankController.getById(res.data[0]?.ConfigRankId);
    if (res.code === 200) {
      const data = res.data[0] || null;
      if (data) data.ConfigRank = rank.data;
      return data || null;
    }
    return null;
  }

  async buyRank(customer: Customer, rankInfo: RankInfo) {
    if (rankInfo.Score <= 0) {
      const newCustomerRank: CustomerRank = {
        Id: randomGID(),
        Name: rankInfo.Name,
        CustomerId: customer.Id,
        ConfigRankId: rankInfo.Id,
        DateCreated: new Date().getTime(),
      };
      await this.controller.add([newCustomerRank]);
      return true;
    }
    const {totalPoint, configReward} = await this._prepareAndValidatePurchase(
      customer,
      rankInfo,
    );

    const referrerInfo = await this._getReferrer(customer);
    const pointReward = this._calculateReward(totalPoint, configReward);
    await this._handleBuyRank({
      customer,
      pointReward,
      rankInfo,
    });

    await this._distributeReferralRewards(customer, referrerInfo, pointReward);

    return true;
  }

  private async _prepareAndValidatePurchase(
    customer: Customer,
    rankInfo: RankInfo,
  ) {
    const point = await this.walletDa.getPointCustomer({
      customerId: customer.Id,
    });

    if (!point || point.total < rankInfo.Score) {
      throw new Error('Số dư Point không đủ');
    }

    const configReward = await this.configRewardDa.fetchConfigReward();
    if (!configReward) {
      throw new Error('Không tìm thấy cấu hình thưởng');
    }

    const configRewardRank =
      await this.configRewardRankDa.fetchConfigRewardRank({
        type: 1,
      });
    if (!configRewardRank) {
      throw new Error('Không tìm thấy cấu hình thưởng hạng');
    }

    return {totalPoint: rankInfo.Score, configReward};
  }

  private async _getReferrer(customer: Customer): Promise<ReferrerInfo> {
    let referrerFirst: Customer | null = null;
    let referrerSecond: Customer | null = null;
    let referrerChildren: Customer[] = [];

    if (!customer.ParentId) {
      return {referrerFirst, referrerSecond, referrerChildren};
    }

    const res = await this.customerController.getById(customer.ParentId);
    if (res.code === 200 && res.data.Id) {
      referrerSecond = res.data;
      if (referrerSecond && referrerSecond.ParentId) {
        const res2 = await this.customerController.getById(
          referrerSecond.ParentId,
        );
        if (res2.code === 200 && res2.data.Id) {
          referrerFirst = res2.data;
        }
      }
    }

    const res3 = await this.customerController.getListSimple({
      query: `@ParentId: {${customer.Id}}`,
    });
    if (res3.code === 200 && res3.data) {
      // direct children
      referrerChildren = [...res3.data];
      // also fetch each child's children (grandchildren)
      const grandchildPromises = res3.data.map((child: Customer) =>
        this.customerController.getListSimple({
          query: `@ParentId: {${child.Id}}`,
        }),
      );
      const grandchildResults = await Promise.all(grandchildPromises);
      grandchildResults.forEach(r => {
        if (r.code === 200 && r.data) {
          referrerChildren.push(...r.data);
        }
      });
    }

    return {referrerFirst, referrerSecond, referrerChildren};
  }

  private _calculateReward(totalPoint: number, configReward: ConfigReward) {
    const {ReferrerSecond, ReferrerFirst, ReferrerChildren} = configReward;

    const pointForReferrerSecond = (totalPoint / 100) * ReferrerSecond;
    const pointForReferrerFirst = (totalPoint / 100) * ReferrerFirst;
    const pointForReferrerChildren = (totalPoint / 100) * ReferrerChildren;
    const pointForRefund = (totalPoint / 100) * configReward.Refund;
    const pointForAdmin = totalPoint;

    return {
      pointForReferrerSecond,
      pointForReferrerFirst,
      pointForReferrerChildren,
      pointForRefund,
      pointForAdmin,
    };
  }

  async transferPointForChildren(
    admin: Customer,
    customer: Customer,
    referrerChildren: Customer[],
    pointForReferrerChildren: number,
  ) {
    const configRewardRanks =
      await this.configRewardRankDa.fetchConfigRewardRank({
        type: 1,
      });
    if (!configRewardRanks) {
      throw new Error('Không tìm thấy cấu hình thưởng hạng');
    }
    const children = await this.customerDa.getCustomerWithRank({
      ids: referrerChildren.map(c => c.Id),
    });
    const bills = [];
    for (const config of configRewardRanks) {
      const childrenRank = children.filter(
        (c: Customer | null): c is Customer =>
          c !== null && c.ConfigRankId === config.ConfigRankId,
      );
      const pointForChildren = (pointForReferrerChildren / 100) * config.Value;

      for (const child of childrenRank) {
        bills.push(
          ...this.walletDa.renderTransferPoint({
            senderId: admin.Id,
            senderName: admin.Name,
            senderMobile: admin.Mobile,
            recipientId: child.Id,
            recipientName: child.Name,
            recipientMobile: child.Mobile,
            amount: pointForChildren / childrenRank.length,
            type: TransactionType.hoahong,
            status: TransactionStatus.success,
            descriptionSender: `Chia hoa hồng khi người dùng ${customer.Name} nâng hạng`,
            descriptionRecipient: `Hoa hồng khi ${customer.Name} nâng hạng`,
          }),
        );
      }
    }
    return bills;
  }

  async _handleBuyRank({
    customer,
    pointReward,
    rankInfo,
  }: {
    customer: Customer;
    pointReward: PointReward;
    rankInfo: RankInfo;
  }) {
    try {
      const currentRank = await this.fetchByCustomerId(customer.Id);

      await this.walletDa.transferPoints({
        senderId: customer.Id,
        senderName: customer.Name,
        senderMobile: customer.Mobile,
        recipientId: ConfigAPI.adminCHAINIVO,
        recipientName: ADMIN_NAME,
        recipientMobile: ADMIN_MOBILE,
        amount: pointReward.pointForAdmin,
        type: TransactionType.tranfer,
        status: TransactionStatus.success,
        descriptionSender: `Chuyển Point nâng hạng`,
        descriptionRecipient: `Nhận Point nâng hạng từ ${customer.Name}`,
      });
      const newCustomerRank: CustomerRank = {
        Id: randomGID(),
        Name: rankInfo.Name,
        CustomerId: customer.Id,
        ConfigRankId: rankInfo.Id,
        DateCreated: new Date().getTime(),
      };
      if (!currentRank) {
        await this.controller.add([newCustomerRank]);
      } else if (currentRank.Id) {
        const dataEdit = {
          Id: currentRank.Id,
          ConfigRankId: rankInfo.Id,
          Name: rankInfo.Name,
          DateCreated: new Date().getTime(),
        };
        await this.controller.edit([dataEdit]);
      }
    } catch (error) {
      throw new Error('Đã xảy ra lỗi khi nâng hạng');
    }
  }

  private async _distributeReferralRewards(
    customer: Customer,
    {referrerFirst, referrerSecond, referrerChildren}: ReferrerInfo,
    pointReward: PointReward,
  ) {
    const bills = [];
    const adminRes = await this.customerController.getById(
      ConfigAPI.adminCHAINIVO,
    );
    if (adminRes.code !== 200 || !adminRes.data.Id)
      throw new Error('Không tìm thấy admin');
    const admin = adminRes.data;

    if (referrerSecond) {
      bills.push(
        ...this.walletDa.renderTransferPoint({
          senderId: admin.Id,
          senderName: admin.Name,
          senderMobile: admin.Mobile,
          recipientId: referrerSecond.Id,
          recipientName: referrerSecond.Name,
          recipientMobile: referrerSecond.Mobile,
          amount: pointReward.pointForReferrerSecond,
          type: TransactionType.hoahong,
          status: TransactionStatus.success,
          descriptionSender: `Chuyển Point hoa hồng khi nâng hạng`,
          descriptionRecipient: `Nhận Point hoa hồng khi ${customer.Name} nâng hạng`,
        }),
      );
    }
    if (referrerFirst) {
      bills.push(
        ...this.walletDa.renderTransferPoint({
          senderId: admin.Id,
          senderName: admin.Name,
          senderMobile: admin.Mobile,
          recipientId: referrerFirst.Id,
          recipientName: referrerFirst.Name,
          recipientMobile: referrerFirst.Mobile,
          amount: pointReward.pointForReferrerFirst,
          type: TransactionType.hoahong,
          status: TransactionStatus.success,
          descriptionSender: `Chuyển Point hoa hồng cho khi nâng hạng`,
          descriptionRecipient: `Nhận Point hoa hồng khi ${customer.Name} nâng hạng`,
        }),
      );
    }
    if (pointReward.pointForReferrerChildren > 0 && referrerChildren.length) {
      bills.push(
        ...(await this.transferPointForChildren(
          admin,
          customer,
          referrerChildren,
          pointReward.pointForReferrerChildren,
        )),
      );
    }
    bills.push(
      ...this.walletDa.renderTransferPoint({
        senderId: admin.Id,
        senderName: admin.Name,
        senderMobile: admin.Mobile,
        recipientId: customer.Id,
        recipientName: customer.Name,
        recipientMobile: customer.Mobile,
        amount: pointReward.pointForRefund,
        type: TransactionType.tranfer,
        status: TransactionStatus.success,
        descriptionSender: `Hoàn trả Point khi nâng hạng`,
        descriptionRecipient: `Nhận Point hoàn trả khi ${customer.Name} nâng hạng`,
      }),
    );
    await this.walletDa.transferManyBills(bills);
  }
}
export default CustomerRankDa;
