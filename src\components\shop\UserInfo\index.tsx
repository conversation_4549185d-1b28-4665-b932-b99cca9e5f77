import React from 'react';
import {StyleSheet, View, Text, ActivityIndicator} from 'react-native';
import FastImage from '@d11/react-native-fast-image';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useUserInfo} from './hooks/useUserInfo';
import Avatar from './components/Avatar';

const UserInfo = () => {
  const {customer, avt, isLoading, currentRank, pickerImg} = useUserInfo();

  if (!customer) {
    return null;
  }

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Avatar avt={avt} customer={customer} onPress={pickerImg} />
        <View style={styles.textContainer}>
          <Text style={styles.userName}>
            {customer?.Name ?? customer?.Email ?? ''}
          </Text>
          <View style={styles.rankContainer}>
            {currentRank?.Icon ? (
              <FastImage
                source={{
                  uri: ConfigAPI.urlImg + currentRank?.Icon,
                }}
                style={styles.rankIcon}
                resizeMode="contain"
              />
            ) : (
              <FastImage
                source={require('../../../assets/images/logo.png')}
                style={styles.rankIcon}
                resizeMode="contain"
              />
            )}
            <Text style={styles.rankName}>
              {currentRank?.Name ? `Hạng ${currentRank?.Name}` : 'Chưa có hạng'}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  centered: {
    padding: 24,
  },
  content: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    gap: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    width: '100%',
  },
  textContainer: {
    gap: 4,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  userName: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
  },
  rankContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  rankIcon: {
    width: 23,
    height: 23,
  },
  rankName: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
  },
});

export default UserInfo;
