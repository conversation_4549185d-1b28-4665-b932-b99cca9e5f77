import React from 'react';
import {View, TouchableOpacity} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {styles} from './styles';
import {ColorThemes} from '../../../../assets/skin/colors';

type ToolbarProps = {
  handlePickImages: () => void;
  handleShowYouTubeModal: () => void;
};

const Toolbar: React.FC<ToolbarProps> = ({
  handlePickImages,
  handleShowYouTubeModal,
}) => {
  return (
    <View style={styles.toolbar}>
      <View style={styles.formatButtons}>
        <TouchableOpacity
          style={styles.youtubeButton}
          onPress={handleShowYouTubeModal}>
          <Winicon
            src="fill/user interface/link"
            size={20}
            color={ColorThemes.light.neutral_text_subtitle_color}
          />
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={styles.imagePickerButton}
        onPress={handlePickImages}>
        <Winicon
          src="fill/development/image"
          size={20}
          color={ColorThemes.light.neutral_text_subtitle_color}
        />
      </TouchableOpacity>
    </View>
  );
};

export default Toolbar;
