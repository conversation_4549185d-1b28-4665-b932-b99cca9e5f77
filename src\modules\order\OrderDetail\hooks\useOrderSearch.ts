import {useState, useEffect, useCallback} from 'react';
import {Title} from '../../../../Config/Contanst';

interface UseOrderSearchProps {
  orderInfo: any;
  routeType: string;
}

interface UseOrderSearchReturn {
  dataSearch: string;
  setDataSearch: (value: string) => void;
  dataSearchResult: any[];
  numberCardSearch: number;
  isLoading: boolean;
}

export const useOrderSearch = ({
  orderInfo,
  routeType,
}: UseOrderSearchProps): UseOrderSearchReturn => {
  const [dataSearch, setDataSearch] = useState<string>('');
  const [dataSearchResult, setDataSearchResult] = useState<any[]>([]);
  const [numberCardSearch, setNumberCardSearch] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSearch = useCallback(
    (searchTerm: string, status: number) => {
      if (!searchTerm || !orderInfo) {
        setDataSearchResult([]);
        setNumberCardSearch(0);
        return;
      }

      let ordersData: any[] = [];

      // Lấy data theo status
      switch (status) {
        case 1:
          ordersData = orderInfo?.NewOrder?.data || [];
          break;
        case 2:
          ordersData = orderInfo?.ProcessOrder?.data || [];
          break;
        case 3:
          ordersData = orderInfo?.DoneOrder?.data || [];
          break;
        case 4:
          ordersData = orderInfo?.CancelOrder?.data || [];
          break;
        default:
          ordersData = [];
      }

      // Filter theo product name và các thông tin khác
      const searchLower = searchTerm.toLowerCase();
      const filteredData = ordersData.filter(order => {
        // Kiểm tra nếu order có orderDetails array
        if (order?.orderDetails && Array.isArray(order.orderDetails)) {
          const hasMatchInOrderDetails = order.orderDetails.some(
            (orderDetail: any) =>
              orderDetail.Name?.toLowerCase().includes(searchLower) ||
              orderDetail.productInfo?.Name?.toLowerCase().includes(
                searchLower,
              ) ||
              orderDetail.title?.toLowerCase().includes(searchLower),
          );

          if (hasMatchInOrderDetails) {
            return true;
          }
        }

        // Kiểm tra mã đơn hàng và các thông tin khác
        return (
          order.Code?.toLowerCase().includes(searchLower) ||
          order.Customer?.Name?.toLowerCase().includes(searchLower) ||
          order.Address?.Name?.toLowerCase().includes(searchLower) ||
          order.Shop?.Name?.toLowerCase().includes(searchLower)
        );
      });

      setDataSearchResult(filteredData);
      setNumberCardSearch(filteredData.length);
    },
    [orderInfo],
  );

  useEffect(() => {
    if (dataSearch) {
      setIsLoading(true);
      const timeoutId = setTimeout(() => {
        if (routeType === Title.New) {
          handleSearch(dataSearch, 1);
        } else if (routeType === Title.Processing) {
          handleSearch(dataSearch, 2);
        } else if (routeType === Title.Done) {
          handleSearch(dataSearch, 3);
        } else if (routeType === Title.Cancel) {
          handleSearch(dataSearch, 4);
        }
        setIsLoading(false);
      }, 1000);

      return () => clearTimeout(timeoutId);
    } else {
      setDataSearchResult([]);
      setNumberCardSearch(0);
      setIsLoading(false);
    }
  }, [dataSearch, routeType, orderInfo, handleSearch]);

  return {
    dataSearch,
    setDataSearch,
    dataSearchResult,
    numberCardSearch,
    isLoading,
  };
};
