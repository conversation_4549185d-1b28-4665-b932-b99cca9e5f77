import React, {useCallback, useMemo} from 'react';
import {View, Text, TouchableOpacity, Linking} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {contentStyles} from './styles';
import {truncateHtml} from './utils';
import {navigate, RootScreen} from '../../../../router/router';
import {ColorThemes} from '../../../../assets/skin/colors';
import {useTag} from '../../context/TagContext';

interface HTMLContentProps {
  content: string;
  contentWidth: number;
  isContentExpanded: boolean;
  onToggleExpand: () => void;
}

export const HTMLContent = React.memo(
  ({
    content,
    contentWidth,
    isContentExpanded,
    onToggleExpand,
  }: HTMLContentProps) => {
    // Tối ưu HTML content với cắt HTML an toàn
    const htmlSource = useMemo(() => {
      if (!content) return {html: ''};

      // Cắt HTML an toàn chỉ khi nội dung dài và chưa mở rộng
      const processedContent =
        !isContentExpanded && content.length > 500
          ? truncateHtml(content, 500)
          : content;

      return {html: processedContent};
    }, [content, isContentExpanded]);

    // Sử dụng useMemo để tính toán isLongContent
    const isLongContent = useMemo(() => {
      return content && content.length > 500;
    }, [content]);

    // Tối ưu renderersProps
    const renderersProps = useMemo(
      () => ({
        img: {
          enableExperimentalPercentWidth: true,
        },
      }),
      [],
    );

    // Memoize compute embedded max width function
    const computeEmbeddedMaxWidth = useCallback(
      () => contentWidth - 32,
      [contentWidth],
    );

    // Tối ưu domVisitors thành một đối tượng tĩnh để tránh tạo mới mỗi lần render
    const domVisitors = useMemo(
      () => ({
        onElement: (element: any) => {
          // Loại bỏ các thuộc tính không cần thiết
          if (element.attribs && element.attribs.style) {
            // Giữ lại chỉ các style cần thiết
            const importantStyles = [
              'color',
              'font-size',
              'font-weight',
              'text-align',
            ];
            const styleStr = element.attribs.style;
            const styles = styleStr.split(';');
            const filteredStyles = styles.filter((style: any) => {
              const prop = style.split(':')[0]?.trim();
              return prop && importantStyles.includes(prop);
            });
            element.attribs.style = filteredStyles.join(';');
          }
          return element;
        },
      }),
      [],
    );

    const {setSelectedTag} = useTag();

    // Custom renderer cho thẻ a
    const renderers = useMemo(
      () => ({
        a: ({TDefaultRenderer, ...props}: any) => {
          const {tnode} = props;
          const href = tnode.attributes?.href;

          return (
            <TDefaultRenderer
              {...props}
              onPress={() => {
                if (href) {
                  const customerId = href.includes('/profile-social')
                    ? href.split('/profile-social?id=')[1]
                    : undefined;

                  const hashtag = href.includes('/search?q=')
                    ? href.split('/search?q=')[1]
                    : undefined;

                  if (customerId) {
                    // Handle internal navigation
                    navigate(RootScreen.ProfileCommunity, {
                      Id: customerId,
                    });
                  } else if (hashtag) {
                    // Set tag in context
                    const objtag = {
                      tag: hashtag,
                      name: hashtag,
                    };
                    setSelectedTag(objtag);
                    // Handle internal navigation
                    // navigate(RootScreen.CommunityLayout, {
                    //   screen: 'Khám phá',
                    // });
                  } else {
                    // Open external link
                    Linking.openURL(href);
                  }
                }
              }}
            />
          );
        },
      }),
      [],
    );

    if (!content) return null;

    return (
      <View style={contentStyles.contentContainer}>
        <RenderHTML
          contentWidth={contentWidth}
          source={htmlSource}
          tagsStyles={{
            body: {margin: 0, padding: 0},
            div: {margin: 0, padding: 0},
            p: {margin: 0, marginBottom: 8},
            b: {fontWeight: 'bold' as const},
            i: {fontStyle: 'italic'},
            u: {textDecorationLine: 'underline'},
            a: {fontWeight: 'bold' as const},
          }}
          classesStyles={{
            'people-tag-link': {
              color: ColorThemes.light.Info_Color_Main,
              textDecorationLine: 'underline',
            },
            'search-tag-link': {
              color: ColorThemes.light.Info_Color_Main,
              textDecorationLine: 'underline',
            },
          }}
          renderers={renderers}
          renderersProps={renderersProps}
          defaultTextProps={{
            selectable: false,
          }}
          enableExperimentalMarginCollapsing={true}
          enableCSSInlineProcessing={false}
          systemFonts={['System']}
          ignoredDomTags={['script', 'style', 'iframe']}
          computeEmbeddedMaxWidth={computeEmbeddedMaxWidth}
          domVisitors={domVisitors}
        />
        {isLongContent && !isContentExpanded && (
          <TouchableOpacity
            onPress={onToggleExpand}
            style={contentStyles.readMoreButton}>
            <Text style={contentStyles.readMoreText}>Xem thêm</Text>
          </TouchableOpacity>
        )}
        {isLongContent && isContentExpanded && (
          <TouchableOpacity
            onPress={onToggleExpand}
            style={contentStyles.readMoreButton}>
            <Text style={contentStyles.readMoreText}>Thu gọn</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  },
);
