# Tài liệu hook `useTransferCANPoint`

## 1. T<PERSON>ng quan

Hook `useTransferCANPoint` đóng gói logic nghiệp vụ và quản lý trạng thái cho các tính năng liên quan đến điểm CAN, chẳng hạn như chuyển điểm hoặc rút điểm. Hook này xử lý một quy trình gồm nhiều bước bao gồm:

1.  <PERSON><PERSON><PERSON><PERSON> thông tin giao dịch (số điểm, người nhận).
2.  <PERSON><PERSON><PERSON> thực OTP.
3.  <PERSON><PERSON><PERSON> thị kết quả giao dịch.

<PERSON><PERSON> được thiết kế để hoạt động với giao diện người dùng dạng `PagerView`, trong đó mỗi bước tương ứng với một trang.

## 2. <PERSON><PERSON><PERSON> trị trả về (`TransferCANPointHookReturn`)

Hook trả về một đối tượng chứa state, refs, và các hàm xử lý cần thiết cho màn hình.

### State (Trạng thái)

- `currentStep: number`: B<PERSON>ớc hiện tại trong quy trình (1, 2, hoặc 3).
- `transferAmount: string`: Số điểm người dùng muốn giao dịch.
- `recipientName: string`: Tên của người nhận.
- `recipientPhone: string`: Số điện thoại của người nhận.
- `recipientId: string`: ID của người nhận.
- `otpValue: string`: Mã OTP do người dùng nhập.
- `isVerifying: boolean`: `true` khi ứng dụng đang trong quá trình xác thực OTP.
- `transactionData: any`: Đối tượng chứa chi tiết giao dịch sau khi hoàn tất, dùng để hiển thị ở màn hình kết quả.
- `currentPoints: number`: Số dư điểm hiện tại của người dùng.
- `loading: boolean`: `true` khi đang tải dữ liệu ban đầu (ví dụ: số dư điểm).
- `customer: any`: Dữ liệu của người dùng đang đăng nhập.
- `type: string`: Loại giao dịch ('tranfer' - chuyển điểm, 'withdraw' - rút điểm), được lấy từ tham số điều hướng.

### Refs

- `pagerRef: React.RefObject<any>`: Ref để gắn vào component `PagerView` giúp điều khiển chuyển trang theo logic.
- `customerBottomSheetRef: React.RefObject<any>`: Ref cho `FBottomSheet` dùng để hiển thị danh sách chọn người nhận.

### Handlers (Hàm xử lý)

- `handleBack(): void`: Xử lý khi người dùng nhấn nút quay lại. Sẽ quay lại bước trước đó hoặc thoát khỏi màn hình nếu đang ở bước đầu tiên.
- `handleSelectRecipient(): void`: Mở bottom sheet cho phép người dùng chọn người nhận điểm.
- `handleStep1Next(): void`: Chuyển từ bước 1 (Nhập thông tin) sang bước 2 (Xác thực OTP).
- `handleOTPComplete(otp: string): void`: Callback được gọi khi người dùng nhập xong mã OTP.
- `handleResendOTP(): void`: Xử lý logic gửi lại mã OTP.
- `handleVerifyOTP(otp: string): Promise<void>`: Xác thực OTP, thực hiện giao dịch, và chuyển sang bước 3 (Kết quả).
- `handleDone(): void`: Xử lý khi người dùng nhấn "Hoàn tất" ở màn hình kết quả, thường là để quay về màn hình trước đó.
- `setTransferAmount(amount: string): void`: Cập nhật trạng thái `transferAmount`.
- `setRecipientName(name: string): void`: Cập nhật trạng thái `recipientName`.
- `setRecipientPhone(phone: string): void`: Cập nhật trạng thái `recipientPhone`.
- `setRecipientId(id: string): void`: Cập nhật trạng thái `recipientId`.
- `fetchCurrentPoints(): Promise<void>`: Lấy số dư điểm hiện tại của người dùng.

## 3. Luồng hoạt động (Workflow)

Hook quản lý một quy trình 3 bước:

1.  **Bước 1: Nhập thông tin**: Người dùng nhập số điểm và chọn người nhận (nếu là chuyển điểm). Hàm `handleStep1Next` sẽ chuyển sang bước 2.
2.  **Bước 2: Xác thực OTP**: Người dùng nhập mã OTP được gửi về. Hàm `handleVerifyOTP` sẽ xác thực mã và thực hiện giao dịch.
3.  **Bước 3: Kết quả giao dịch**: Hiển thị tóm tắt giao dịch (thành công hoặc thất bại). Hàm `handleDone` kết thúc quy trình.

## 4. Logic cốt lõi & Các thành phần phụ thuộc

- **Lấy dữ liệu**: Sử dụng `DataController('HistoryReward')` để lấy lịch sử điểm và tính toán số dư hiện tại.
- **Xác thực OTP**: Tương tác với `CustomerActions.verify2Action` để xác thực OTP.
- **Tạo giao dịch**: Dùng `DataController('HistoryReward')` để tạo các bản ghi giao dịch cho người gửi và người nhận.
- **Thành phần UI**: Tận dụng các components từ `wini-mobile-components` như `showSnackbar` để hiển thị thông báo và `showBottomSheet` để hiển thị UI chọn người nhận.
- **Redux**: Sử dụng `useDispatch` và `useSelector` để tương tác với Redux store, ví dụ như lấy thông tin khách hàng hoặc cập nhật lại thông tin sau khi giao dịch thành công.

## 5. Ví dụ sử dụng

Hook được sử dụng trong component màn hình (`TransferCANPointScreen1`) để cung cấp dữ liệu và logic cho UI.

```tsx
// src/Screen/Page/TransferCANPoint/TransferCANPointScreen1/index.tsx

import React, {useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
// ... các import khác
import {useTransferCANPoint} from './hooks/useTransferCANPoint';
import TransferHeader from './components/TransferHeader';
import TransferStepIndicator from './components/TransferStepIndicator';
import TransferPagerView from './components/TransferPagerView';

const TransferCANPointScreen1: React.FC = () => {
  // Gọi hook để lấy tất cả state và handlers
  const hookValues = useTransferCANPoint();

  useEffect(() => {
    // Lấy số dư điểm khi màn hình được mount
    hookValues.fetchCurrentPoints();
  }, [hookValues.fetchCurrentPoints]);

  return (
    <View style={styles.container}>
      <TransferHeader type={hookValues.type} onBack={hookValues.handleBack} />

      {/* ... Giao diện loading ... */}

      <View style={styles.container}>
        <FBottomSheet ref={hookValues.customerBottomSheetRef} />
        <TransferStepIndicator
          currentStep={hookValues.currentStep}
          totalSteps={3}
        />
        <TransferPagerView
          // Truyền tất cả giá trị từ hook cho component con
          {...hookValues}
        />
      </View>
    </View>
  );
};

// ... styles

export default TransferCANPointScreen1;
```
