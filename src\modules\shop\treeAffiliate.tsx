import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';

import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../router/router';
import {DataController} from '../../base/baseController';
import store from '../../redux/store/store';
import {LoadingUI} from '../../features/loading';
import {Ultis} from '../../utils/Utils';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from '@d11/react-native-fast-image';
import {
  StatusOrder,
  TransactionStatus,
  TransactionType,
} from '../../Config/Contanst';
const TreeAffiliate = () => {
  const [data, setData] = useState<any>();
  const navigation = useNavigation<any>();
  const Customercontroller = new DataController('Customer');
  const Ordercontroller = new DataController('Order');
  const rewardHistorycontroller = new DataController('HistoryReward');
  const customerCurrent = store.getState().customer.data;
  // Xử lý khi nhấn "Xem tất cả"
  const handleViewAll = () => {
    const customer = store.getState().customer.data;
    if (!customer) return;
    navigation.navigate(RootScreen.TreeAffiliateDetail, {
      customerId: customer.Id,
      username: customer.Name,
    });
  };
  const [loading, setLoading] = useState(false);
  const fetchData = async () => {
    setLoading(true);

    const customerCurrent = store.getState().customer.data;
    //gọi chung vào promise các api

    // Tạo query để lấy:
    // 1. Cấp cha của user hiện tại (nếu có)
    // 2. User hiện tại
    // 3. Chỉ cấp con trực tiếp (F1) của user hiện tại
    if (!customerCurrent) return;
    let query = `@Id: {${customerCurrent.Id}} | @ParentId: {${customerCurrent.Id}}`;
    if (customerCurrent.ParentId) {
      query = `@Id: {${customerCurrent.ParentId}} | ` + query;
    }
    const [customerRes, rewardRes] = await Promise.all([
      Customercontroller.getListSimple({
        query: query,
        returns: ['Id', 'Name', 'ParentId', 'ListParent', 'AvatarUrl'],
        sortby: {BY: 'DateCreated', DIRECTION: 'ASC'},
      }),
      rewardHistorycontroller.group({
        reducers:
          'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalReward',
        searchRaw: `@CustomerId: {${customerCurrent.Id}} @Type: [${TransactionType.hoahong}] @Status: [${TransactionStatus.success}]`,
      }),
    ]);

    if (customerRes?.code === 200 && rewardRes?.code === 200) {
      //lấy hết danh sách member in tree
      var members = [];
      const memberRes = await Customercontroller.getListSimple({
        query: `@ListParent: (*${customerCurrent.Id}*)`,
        returns: ['Id', 'Name', 'ParentId', 'ListParent', 'AvatarUrl'],
        sortby: {BY: 'DateCreated', DIRECTION: 'ASC'},
      });
      if (memberRes?.code === 200 && memberRes?.data.length > 0) {
        members = memberRes.data.map((item: any) => item.Id);
      }
      // var customerIds = customerRes.data.filter((item: any) => item.Id !== customerCurrent.ParentId).map((item: any) => item.Id);
      // if (customerIds.length === 0) {
      //   setLoading(false);
      //   return;
      // }
      const customerIds = [customerCurrent.Id, ...members];
      const orderRes = await Ordercontroller.group({
        reducers:
          'LOAD * GROUPBY 1 @CustomerId REDUCE SUM 1 @Value AS TotalPrice',
        searchRaw: `@CustomerId: {${customerIds.join(' | ')}} @Status: [${
          StatusOrder.success
        }]`,
      });
      var totalprice = orderRes.data.reduce(
        (total: number, item: any) => total + parseFloat(item.TotalPrice),
        0,
      );
      var totalreward = rewardRes.data.reduce(
        (total: number, item: any) => total + parseFloat(item.TotalReward),
        0,
      );

      // Xây dựng cấu trúc tree đơn giản theo yêu cầu
      const buildSimpleTreeStructure = (customers: any[]) => {
        const result: any[] = [];

        // 1. Thêm cấp cha (nếu có) - Level -1
        const parentCustomer = customers.find(
          c => c.Id === customerCurrent.ParentId,
        );
        if (parentCustomer) {
          result.push({
            id: parentCustomer.Id,
            name: parentCustomer.Name,
            avatar: parentCustomer.AvatarUrl,
            isRoot: true,
            subtitle: '(Người giới thiệu)',
            level: 0,
            parentId: null,
            members: customers.filter(c => c.ParentId === parentCustomer.Id)
              .length,
            revenue:
              orderRes.data
                ?.filter((a: any) => a.CustomerId === parentCustomer.Id)
                .reduce(
                  (total: number, item: any) => total + item.TotalPrice,
                  0,
                ) || 0,
            hasChildren: true,
          });
        }

        // 2. Thêm user hiện tại - Level 0 hoặc 1 (tùy có cha hay không)
        const currentCustomer = customers.find(
          c => c.Id === customerCurrent.Id,
        );
        if (currentCustomer) {
          const currentLevel = parentCustomer ? 1 : 0;
          const memberList = memberRes.data.filter((c: any) =>
            c.ListParent.includes(currentCustomer.Id),
          );
          const memberListAddCurrent = [...memberList, currentCustomer];
          result.push({
            id: currentCustomer.Id,
            name: currentCustomer.Name,
            avatar: currentCustomer.AvatarUrl,
            isRoot: !parentCustomer,
            subtitle: '',
            level: 0,
            parentId: parentCustomer?.Id || null,
            members: memberList.length ?? 0,
            revenue:
              orderRes.data
                ?.filter((a: any) =>
                  memberListAddCurrent.some((c: any) => c.Id === a.CustomerId),
                )
                .reduce(
                  (total: number, item: any) =>
                    total + parseFloat(item.TotalPrice),
                  0,
                ) || 0,
            hasChildren: customers.some(c => c.ParentId === currentCustomer.Id),
          });
        }

        // 3. Thêm chỉ cấp con trực tiếp (F1) của user hiện tại
        const directChildren = customers.filter(
          c => c.ParentId === customerCurrent.Id,
        );
        directChildren.forEach(child => {
          const memberList = memberRes.data.filter((c: any) =>
            c.ListParent.includes(child.Id),
          );
          const memberListAddCurrent = [...memberList, child];
          result.push({
            id: child.Id,
            name: child.Name,
            avatar: child.AvatarUrl,
            isRoot: false,
            subtitle: '',
            level: 1,
            parentId: customerCurrent.Id,
            members: memberList.length ?? 0,
            revenue:
              orderRes.data
                ?.filter((a: any) =>
                  memberListAddCurrent.some((c: any) => c.Id === a.CustomerId),
                )
                .reduce(
                  (total: number, item: any) =>
                    total + parseFloat(item.TotalPrice),
                  0,
                ) || 0,
            hasChildren: false, // Không hiển thị children của F1
          });
        });

        return result;
      };

      const treeData = buildSimpleTreeStructure(customerRes.data);

      setData({
        stats: {
          totalMembers: customerRes.data.length,
          groupRevenue: totalprice,
          commission: totalreward,
        },
        treeData: treeData,
      });
    }

    setLoading(false);
  };
  useEffect(() => {
    fetchData();
  }, []);
  // Hàm lấy màu theo level
  const getColorByLevel = (level: number) => {
    switch (level) {
      case 0:
        return ColorThemes.light.neutral_text_title_color;
      case 1:
        return ColorThemes.light.primary_main_color;
      case 2:
        return ColorThemes.light.secondary1_main_color;
      default:
        return ColorThemes.light.neutral_text_subtitle_color;
    }
  };

  // Component thống kê
  const StatsCard = () => (
    <View style={styles.statsWrapper}>
      {/* Header */}
      <View style={styles.statsHeader}>
        <Text style={styles.statsTitle}>Hệ thống bán hàng</Text>
        <TouchableOpacity onPress={handleViewAll}>
          <Text style={styles.viewAllText}>Xem tất cả</Text>
        </TouchableOpacity>
      </View>

      {/* Stats Content */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Tổng thành viên</Text>
          <Text
            style={[
              styles.statValue,
              {color: ColorThemes.light.primary_main_color},
            ]}>
            {data?.stats?.totalMembers ?? 0}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Doanh thu nhóm</Text>
          <Text
            style={[
              styles.statValue,
              {color: ColorThemes.light.secondary1_main_color},
            ]}>
            {Ultis.formatNumberConvert(data?.stats?.groupRevenue ?? 0)}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Hoa hồng</Text>
          <Text
            style={[
              styles.statValue,
              {color: ColorThemes.light.secondary3_main_color},
            ]}>
            {Ultis.formatNumberConvert(data?.stats?.commission ?? 0)}
          </Text>
        </View>
      </View>
    </View>
  );

  // Xử lý khi click vào item để xem chi tiết
  const handleItemClick = (item: any) => {
    navigation.navigate(RootScreen.TreeAffiliateDetail, {
      customerId: item.id,
      username: item.name,
    });
  };

  // Component item trong cây
  const TreeItem = ({item, index}: {item: any; index: number}) => {
    // Kiểm tra xem có phải là item cuối cùng của cùng level không
    const isLastInLevel =
      index === data?.treeData?.length - 1 ||
      data?.treeData?.[index + 1]?.level <= item.level;

    // Kiểm tra xem có parent ở level trước không
    const hasParentAbove =
      index > 0 && data?.treeData?.[index - 1]?.level < item.level;

    return (
      <TouchableOpacity
        style={[styles.treeItem, {marginLeft: item.level * 20}]}
        onPress={() => handleItemClick(item)}>
        {/* Dây nối */}
        {item.level > 0 && (
          <View style={styles.connectionLines}>
            {/* Đường dọc từ trên xuống - chỉ hiển thị nếu không phải item cuối */}
            {!isLastInLevel && (
              <View
                style={[styles.verticalLine, {left: item.level * 20 - 10}]}
              />
            )}
            {/* Đường dọc ngắn từ trên đến ngang */}
            {hasParentAbove && (
              <View
                style={[styles.verticalLineShort, {left: item.level * 20 - 10}]}
              />
            )}
            {/* Đường ngang */}
            <View
              style={[styles.horizontalLine, {left: item.level * 20 - 10}]}
            />
          </View>
        )}

        <View style={styles.treeItemContent}>
          <View style={styles.treeItemLeft}>
            <Image
              source={
                item.avatar
                  ? {
                      uri: item.avatar?.includes('https')
                        ? item.avatar
                        : `${ConfigAPI.urlImg + item.avatar}`,
                    }
                  : require('../../assets/images/logo.png')
              }
              style={{width: 16, height: 16, borderRadius: 50}}
            />
            <Text
              style={[styles.treeName, {color: getColorByLevel(item.level)}]}>
              {item.name}
              {item.subtitle && (
                <Text style={styles.subtitle}> {item.subtitle}</Text>
              )}
            </Text>
          </View>
          {(item.level > 0 || (item.level === 0 && !item.isRoot)) && (
            <View style={styles.treeItemRight}>
              <View style={styles.memberInfo}>
                <Winicon
                  src="outline/sport/users-mm"
                  size={12}
                  color={getColorByLevel(item.level)}
                />
                <Text
                  style={[
                    styles.memberCount,
                    {color: getColorByLevel(item.level)},
                  ]}>
                  {item.members}
                </Text>
              </View>
              <View style={styles.memberInfo}>
                <Winicon
                  src="outline/files/file-money"
                  size={12}
                  color={getColorByLevel(2)}
                />
                <Text style={[styles.memberCount, {color: getColorByLevel(2)}]}>
                  {Ultis.money(item.revenue)} đ
                </Text>
              </View>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {loading ? (
          <LoadingUI isLoading={loading} />
        ) : (
          <>
            <StatsCard />
            <View style={styles.treeContainer}>
              {data?.treeData && data.treeData.length > 0 ? (
                data.treeData.map((item: any, index: number) => (
                  <TreeItem key={item.id} item={item} index={index} />
                ))
              ) : (
                <View style={styles.emptyContainer}>
                  <Winicon
                    src="outline/user interface/users"
                    size={48}
                    color={ColorThemes.light.neutral_text_subtitle_color}
                  />
                  <Text style={styles.emptyText}>Không có cấp con</Text>
                  <Text style={styles.emptySubText}>
                    Chưa có thành viên nào trong hệ thống
                  </Text>
                </View>
              )}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginBottom: 70,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },
  content: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  statsWrapper: {
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 12,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  statsTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  viewAllText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 8,
    paddingHorizontal: 8,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 8,
    textAlign: 'center',
    fontSize: 12,
  },
  statValue: {
    ...TypoSkin.heading5,
    fontWeight: '700',
  },
  treeContainer: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    margin: 16,
    borderRadius: 12,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  treeItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_lighter_border_color,
  },
  treeItemContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  treeItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  treeName: {
    ...TypoSkin.medium1,
    marginLeft: 8,
    flex: 1,
  },
  subtitle: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontWeight: '400',
  },
  treeItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  memberInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  memberCount: {
    ...TypoSkin.regular2,
    fontWeight: '500',
  },
  revenue: {
    ...TypoSkin.medium1,
    fontWeight: '600',
  },
  // Connection Lines Styles
  connectionLines: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  verticalLine: {
    position: 'absolute',
    top: 0,
    width: 1,
    height: 56,
    backgroundColor: ColorThemes.light.neutral_lighter_border_color,
  },
  verticalLineShort: {
    position: 'absolute',
    top: 0,
    width: 1,
    height: 24,
    backgroundColor: ColorThemes.light.neutral_lighter_border_color,
  },
  horizontalLine: {
    position: 'absolute',
    top: 24,
    width: 20,
    height: 1,
    backgroundColor: ColorThemes.light.neutral_lighter_border_color,
  },
  // Empty State Styles
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default TreeAffiliate;
