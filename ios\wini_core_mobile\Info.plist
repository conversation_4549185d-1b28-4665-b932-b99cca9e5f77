<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Chainivo</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.801228927819-icrqv92i2uoficeo7ove6dr12p2klctq</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app-1-801228927819-ios-aa3744224a90f45f9faece</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>vnpayITM</string>
			</array>
		</dict>
		<!-- Deep Link: Custom scheme -->
		<dict>
			<key>CFBundleURLName</key>
			<string>chainivo.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>chainivo</string>
			</array>
		</dict>
		<!-- End deep link -->
	</array>
	<!-- Associated Domains cho Universal Links -->
    <key>com.apple.developer.associated-domains</key>
    <array>
        <string>applinks:chainivo.com</string>
        <string>applinks:www.chainivo.com</string>
    </array>
	<!-- End Associated Domains cho Universal Links -->
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>vnpayITM</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app requires to access your photo library to show image on profile and send via chat</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Authentication with Face ID</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your location is required for find the pitch around you on app</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your location is required for find the pitch around you on app</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires to access your microphone to record video with your voice send via chat</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires to access your photo library to show image on profile and send via chat</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app requires to save QR code to your photo library</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>Allow Notification to receive about important news in app</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
