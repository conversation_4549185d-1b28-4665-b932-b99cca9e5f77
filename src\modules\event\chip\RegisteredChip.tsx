import {StyleSheet, Text, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {useTranslation} from '../../../locales/useTranslation';

const RegisteredChip = () => {
  const {t} = useTranslation();
  return (
    <View style={styles.registeredChip}>
      <View style={styles.checkIcon}>
        <Text style={styles.checkText}>✓</Text>
      </View>
      <Text style={styles.registeredText}>{t('event.registered')}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  registeredChip: {
    height: 26,
    backgroundColor: ColorThemes.light.secondary2_sub_color,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  checkIcon: {
    width: 16,
    height: 16,
    backgroundColor: ColorThemes.light.secondary6_darker_color,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 6,
  },
  checkText: {
    color: ColorThemes.light.white,
    fontSize: 8,
    fontWeight: 'bold',
  },
  registeredText: {
    color: ColorThemes.light.white,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default RegisteredChip;
