import React from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {Text} from 'react-native-paper';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../assets/skin/colors';
import {TypoSkin} from '../assets/skin/typography';
import LinearGradient from 'react-native-linear-gradient';
import {Ultis} from '../utils/Utils';
import Svg, {Line} from 'react-native-svg';
import {TransactionStatus, TransactionType} from '../Config/Contanst';

interface TransactionSummaryProps {
  status: TransactionStatus;
  transactionId: string;
  amount: number;
  recipientName: string;
  recipientPhone: string;
  senderName: string;
  timestamp: string;
  onCopyTransactionId?: () => void;
  type: TransactionType;
  accountNumber: string;
}

const TransactionSummary: React.FC<TransactionSummaryProps> = ({
  status,
  transactionId,
  amount,
  recipientName,
  accountNumber,
  senderName,
  timestamp,
  onCopyTransactionId,
  type,
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getHours().toString().padStart(2, '0')}:${date
      .getMinutes()
      .toString()
      .padStart(2, '0')}, Ngày ${date.getDate().toString().padStart(2, '0')}/${(
      date.getMonth() + 1
    )
      .toString()
      .padStart(2, '0')}/${date.getFullYear()}`;
  };

  return (
    <View style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 16,
        }}>
        <View
          style={{
            borderRadius: 100,
            backgroundColor:
              status === TransactionStatus.success
                ? ColorThemes.light.success_main_color
                : status === TransactionStatus.failed
                ? ColorThemes.light.error_main_color
                : ColorThemes.light.warning_main_color,
            padding: 12,
          }}>
          <Winicon
            src={
              status === TransactionStatus.success
                ? 'outline/layout/check'
                : status === TransactionStatus.failed
                ? 'outline/layout/close'
                : 'outline/user interface/clock'
            }
            size={60}
            color={
              status === TransactionStatus.success
                ? ColorThemes.light.success_background
                : status === TransactionStatus.failed
                ? ColorThemes.light.error_background
                : ColorThemes.light.warning_background
            }
          />
        </View>
      </View>
      {/* Status Header */}
      <LinearGradient
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
        style={{borderRadius: 16}}>
        <View style={styles.statusContainer}>
          <View
            style={{
              ...styles.statusBadge,
              backgroundColor:
                status === TransactionStatus.success
                  ? ColorThemes.light.success_main_color
                  : status === TransactionStatus.failed
                  ? ColorThemes.light.error_main_color
                  : ColorThemes.light.warning_main_color,
              borderRadius: 16,
            }}>
            <Winicon
              src={
                status === TransactionStatus.success
                  ? 'outline/layout/check'
                  : status === TransactionStatus.failed
                  ? 'outline/layout/close'
                  : 'outline/user interface/clock'
              }
              size={10}
              color={ColorThemes.light.white}
            />
            <Text style={styles.statusText}>
              {status === TransactionStatus.success
                ? 'Thành công'
                : status === TransactionStatus.failed
                ? 'Thất bại'
                : 'Đang xử lý'}
            </Text>
          </View>
          <Text style={styles.timestampText}>{formatDate(timestamp)}</Text>
        </View>
      </LinearGradient>

      {/* Transaction Details */}
      <LinearGradient
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
        style={{
          borderRadius: 16,
        }}>
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Mã giao dịch</Text>
            <Text style={styles.amountValue}>{transactionId}</Text>
            <TouchableOpacity
              style={{paddingLeft: 8}}
              onPress={onCopyTransactionId}
              activeOpacity={0.7}>
              <Winicon
                src="outline/layout/copy-2"
                size={16}
                color={ColorThemes.light.primary_main_color}
              />
            </TouchableOpacity>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>
              Người {type === TransactionType.tranfer ? 'gửi' : 'rút'}
            </Text>
            <Text style={styles.detailValue}>{senderName}</Text>
          </View>
          {type === TransactionType.tranfer && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Người nhận</Text>
              <Text style={styles.detailValue}>{recipientName}</Text>
            </View>
          )}
          <Svg height="1" width="100%">
            <Line
              x1="0"
              y1="0"
              x2="100%"
              y2="0"
              stroke="black"
              strokeWidth="1"
              strokeDasharray="5,5" // 5px nét, 5px hở
            />
          </Svg>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Tổng số</Text>
            <Text style={styles.amountValue}>{Ultis.money(amount)} Point</Text>
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 16,
  },
  statusContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.success_main_color,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    gap: 6,
  },
  statusText: {
    ...TypoSkin.label3,
    color: ColorThemes.light.white,
    fontSize: 12,
  },
  timestampText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  detailsContainer: {
    borderRadius: 16,
    padding: 16,
    gap: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 1,
    fontSize: 14,
    fontWeight: '600',
  },
  detailValue: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 2,
    textAlign: 'right',
    fontSize: 14,
    fontWeight: '600',
  },
  recipientInfo: {
    flex: 2,
    alignItems: 'flex-end',
  },
  recipientPhone: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    // marginTop: 2,
    fontSize: 12,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: ColorThemes.light.neutral_lighter_border_color,
    marginVertical: 8,
    //dotte border
    borderStyle: 'dashed',
    borderWidth: 1,
    borderBottomColor: '#000',
  },
  amountValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.primary_main_color,
    flex: 2,
    textAlign: 'right',
    fontSize: 14,
    fontWeight: '600',
  },
  transactionIdContainer: {
    backgroundColor: ColorThemes.light.primary_background,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionIdLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  transactionIdRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 8,
  },
  transactionIdValue: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontFamily: 'monospace',
    fontWeight: '600',
  },
});

export default TransactionSummary;
