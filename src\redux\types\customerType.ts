import {RankInfo} from './rankTypes';

export interface Customer {
  Id: string;
  AvatarUrl: string;
  Description: string;
  Name: string;
  Mobile: string;
  Email: string;
  DeviceToken: string;
  RefCode: string;
  ListParent: string;
  Google: string;
  SecretOTP: string;
  WalletAddress: string;
  PrivateKey: string;
  Address: string;
  Password: string;
  DateCreated: number;
  Dob: number;
  Gender: number;
  IdUserGoogle: string;
  Token: number;
  ParentId: string;
  ConfigRank: RankInfo;
  ConfigRankId: string;
  IsVerify: boolean;
  IsEnable2FA: boolean;
  IsAdmin: boolean;
}
