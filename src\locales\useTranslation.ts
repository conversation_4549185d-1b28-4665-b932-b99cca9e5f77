import {useTranslation as useI18nTranslation} from 'react-i18next';
import {useLanguage} from './languageContext';

/**
 * Custom hook để sử dụng translation với type safety
 * Kết hợp useTranslation từ react-i18next với LanguageContext
 */
export const useTranslation = () => {
  const {t, i18n} = useI18nTranslation();
  const {language, changeLanguage} = useLanguage();

  return {
    t,
    language,
    changeLanguage,
    currentLanguage: i18n.language,
    isLanguageLoaded: i18n.isInitialized,
  };
};

/**
 * Utility functions cho việc xử lý ngôn ngữ
 */
export const LanguageUtils = {
  /**
   * Kiểm tra xem ngôn ngữ hiện tại có phải là tiếng Việt không
   */
  isVietnamese: (language: string): boolean => language === 'vi',

  /**
   * Kiểm tra xem ngôn ngữ hiện tại có phải là tiếng Anh không
   */
  isEnglish: (language: string): boolean => language === 'en',

  /**
   * Kiểm tra xem ngôn ngữ hiện tại có phải là tiếng Nhật không
   */
  isJapanese: (language: string): boolean => language === 'ja',

  /**
   * Kiểm tra xem ngôn ngữ hiện tại có phải là tiếng Trung không
   */
  isChinese: (language: string): boolean => language === 'zh',

  /**
   * Kiểm tra xem ngôn ngữ hiện tại có phải là tiếng Đức không
   */
  isGerman: (language: string): boolean => language === 'de',

  /**
   * Lấy tên hiển thị của ngôn ngữ
   */
  getLanguageDisplayName: (language: string): string => {
    switch (language) {
      case 'vi':
        return 'Tiếng Việt';
      case 'en':
        return 'English';
      case 'ja':
        return '日本語';
      case 'zh':
        return '中文';
      case 'de':
        return 'Deutsch';
      default:
        return 'Tiếng Việt';
    }
  },

  /**
   * Lấy danh sách các ngôn ngữ được hỗ trợ
   */
  getSupportedLanguages: () => [
    {code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt'},
    {code: 'en', name: 'English', nativeName: 'English'},
    {code: 'ja', name: 'Japanese', nativeName: '日本語'},
    {code: 'zh', name: 'Chinese', nativeName: '中文'},
    {code: 'de', name: 'German', nativeName: 'Deutsch'},
  ],

  /**
   * Kiểm tra xem ngôn ngữ có được hỗ trợ không
   */
  isSupportedLanguage: (language: string): boolean => {
    return ['vi', 'en', 'ja', 'zh', 'de'].includes(language);
  },
};

/**
 * Type definitions cho translation keys
 * Có thể mở rộng để có type safety tốt hơn
 */
export type TranslationKey = string;

/**
 * Hook để lấy translation với fallback
 */
export const useTranslationWithFallback = () => {
  const {t} = useTranslation();

  const translate = (key: TranslationKey, fallback?: string, options?: any) => {
    const translated = t(key, options);
    // Nếu translation trả về chính key (nghĩa là không tìm thấy translation)
    // thì sử dụng fallback
    if (translated === key && fallback) {
      return fallback;
    }
    return translated;
  };

  return translate;
};

/**
 * Constants cho các ngôn ngữ được hỗ trợ
 */
export const SUPPORTED_LANGUAGES = {
  VIETNAMESE: 'vi',
  ENGLISH: 'en',
  JAPANESE: 'ja',
  CHINESE: 'zh',
  GERMAN: 'de',
} as const;

export type SupportedLanguage =
  (typeof SUPPORTED_LANGUAGES)[keyof typeof SUPPORTED_LANGUAGES];
