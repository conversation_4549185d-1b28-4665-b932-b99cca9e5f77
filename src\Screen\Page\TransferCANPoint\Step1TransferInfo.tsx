import React, {useMemo} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Text,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  FPopup,
  showBottomSheet,
  showPopup,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import RecipientSelector from '../../../components/RecipientSelector';
import PointAmountInput, {
  formatNumber,
} from '../../../components/PointAmountInput';
import {
  FeeTransaction,
  TransactionStatus,
  TransactionType,
} from '../../../Config/Contanst';
import {DataController} from '../../../base/baseController';
import store from '../../../redux/store/store';
import {TypoSkin} from '../../../assets/skin/typography';
import {Ultis} from '../../../utils/Utils';
import {Tooltip} from 'react-native-paper';

interface Step1TransferInfoProps {
  currentPoints: number;
  transferAmount: string;
  recipientName?: string;
  recipientPhone?: string;
  onAmountChange: (amount: string) => void;
  onSelectRecipient: () => void;
  onDeleteRecipient: () => void;
  onNext: (totalTranfer?: number) => void;
  type: TransactionType;
}

const Step1TransferInfo: React.FC<Step1TransferInfoProps> = ({
  currentPoints,
  transferAmount,
  recipientName,
  recipientPhone,
  onAmountChange,
  onSelectRecipient,
  onDeleteRecipient,
  onNext,
  type,
}) => {
  const isValidAmount = () => {
    if (
      totalTransferWithFee > currentPoints &&
      type === TransactionType.tranfer
    )
      return false;
    const amount = parseInt(transferAmount || '0');
    return amount > 0 && amount <= currentPoints;
  };

  const isFormValid = () => {
    if (type === TransactionType.Withdraw) return isValidAmount();
    if (type === TransactionType.tranfer)
      return recipientName && recipientPhone && isValidAmount();
  };

  const continueTransfer = async () => {
    const controller = new DataController('HistoryReward');
    const customer = store.getState().customer.data;
    const res = await controller.getListSimple({
      query: `@CustomerId: {${customer?.Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      returns: ['Value'],
    });
    if (res?.code === 200 && res?.data.length) {
      // get total from res.data with field Value
      const total = res.data.reduce(
        (total: number, item: any) => total + parseFloat(item.Value),
        0,
      );
      const transferAmountWithFee =
        type === TransactionType.tranfer
          ? totalTransferWithFee
          : parseFloat(transferAmount);
      // check total < transferAmountWithFee
      if (total < transferAmountWithFee) {
        showSnackbar({
          message: 'Số điểm không đủ để thực hiện giao dịch',
          status: ComponentStatus.ERROR,
        });
        return;
      } else {
        if (onNext) onNext(totalTransferWithFee);
      }
    } else {
      showSnackbar({
        message: 'Có lỗi xảy ra khi lấy thông tin số dư ví của bạn',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const bottomSheetRef = React.useRef<any>(null);

  const amount = useMemo(() => {
    return parseFloat(transferAmount || '0');
  }, [transferAmount]);

  // phí
  const feeTotal = useMemo(() => {
    return (amount / 100) * FeeTransaction.feeTransfer;
  }, [transferAmount]);

  // tổng chuyển + phí
  const totalTransferWithFee = useMemo(() => {
    return amount + feeTotal;
  }, [transferAmount, feeTotal]);

  // point nhận được
  const totalReceive = useMemo(() => {
    return amount - (amount / 100) * FeeTransaction.feeReceiver;
  }, [transferAmount]);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <FPopup ref={bottomSheetRef} />
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled">
        {type === TransactionType.tranfer && (
          <RecipientSelector
            recipientName={recipientName}
            recipientPhone={recipientPhone}
            onPress={onSelectRecipient}
            onDelete={onDeleteRecipient}
          />
        )}

        <PointAmountInput
          currentPoints={currentPoints}
          transferAmount={transferAmount}
          onAmountChange={onAmountChange}
          type={type}
        />
        {type === TransactionType.tranfer && (
          <View style={{paddingHorizontal: 16}}>
            {/* Tính toán số point thực nhận được */}
            {/* mất % Point khi thực hiện chuyển điểm. Người chuyền 5%; người nhận 0,5% */}
            <View style={styles.currentPointsContainer}>
              <View
                style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
                <Text style={styles.currentPointsLabel}>Phí giao dịch</Text>
                <TouchableOpacity
                  style={{padding: 4}}
                  onPress={() => {
                    showPopup({
                      ref: bottomSheetRef,
                      enableDismiss: true,
                      children: (
                        <View
                          style={{
                            height: Dimensions.get('window').height / 5,
                            padding: 16,
                            borderTopLeftRadius: 16,
                            borderTopRightRadius: 16,
                            width: '100%',
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                            gap: 8,
                          }}>
                          <Winicon
                            src="fill/travel/c-info"
                            size={16}
                            color={ColorThemes.light.infor_main_color}
                          />
                          <Text
                            style={{
                              ...TypoSkin.subtitle1,
                              color: ColorThemes.light.infor_main_color,
                            }}>
                            {`Phí chuyển điểm: \n- Phí người chuyển 5% \n- Phí người nhận 0.5% (Trừ vào số point thực nhận)`}
                          </Text>
                        </View>
                      ),
                    });
                  }}>
                  <Winicon
                    src="outline/travel/c-info"
                    size={16}
                    color={ColorThemes.light.primary_main_color}
                  />
                </TouchableOpacity>
              </View>
              <Text
                style={{
                  ...TypoSkin.subtitle1,
                  color: ColorThemes.light.infor_main_color,
                  flex: 1,
                  textAlign: 'right',
                }}
                numberOfLines={3}>
                {formatNumber(feeTotal)} P
              </Text>
            </View>
            <View style={styles.currentPointsContainer}>
              <View
                style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
                <Text
                  style={{
                    ...styles.currentPointsLabel,
                    color:
                      totalTransferWithFee > currentPoints
                        ? ColorThemes.light.error_main_color
                        : ColorThemes.light.primary_main_color,
                  }}>
                  Tổng point chuyển
                </Text>
                {totalTransferWithFee > currentPoints && (
                  <Tooltip title="Số dư không đủ để thực hiện giao dịch">
                    <TouchableOpacity
                      style={{padding: 4}}
                      onPress={() => {
                        showPopup({
                          ref: bottomSheetRef,
                          enableDismiss: true,
                          children: (
                            <View
                              style={{
                                height: Dimensions.get('window').height / 5,
                                padding: 16,
                                borderTopLeftRadius: 16,
                                borderTopRightRadius: 16,
                                width: '100%',
                                backgroundColor:
                                  ColorThemes.light
                                    .neutral_absolute_background_color,
                                gap: 8,
                              }}>
                              <Winicon
                                src="fill/travel/c-info"
                                size={16}
                                color={ColorThemes.light.infor_main_color}
                              />
                              <Text
                                style={{
                                  ...TypoSkin.subtitle1,
                                  color: ColorThemes.light.infor_main_color,
                                }}>
                                {`Số dư không đủ để thực hiện giao dịch`}
                              </Text>
                            </View>
                          ),
                        });
                      }}>
                      <Winicon
                        src="outline/travel/c-info"
                        size={16}
                        color={ColorThemes.light.error_main_color}
                      />
                    </TouchableOpacity>
                  </Tooltip>
                )}
              </View>
              <Text
                style={{
                  ...styles.currentPointsValue,
                  fontWeight: 'bold',
                  flex: 1,
                  textAlign: 'right',
                  color:
                    totalTransferWithFee > currentPoints
                      ? ColorThemes.light.error_main_color
                      : ColorThemes.light.primary_main_color,
                }}
                numberOfLines={3}>
                {Ultis.money(totalTransferWithFee)} P
              </Text>
            </View>
            <View style={styles.currentPointsContainer}>
              <View
                style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
                <Text style={styles.currentPointsLabel}>Point thực nhận</Text>
                <TouchableOpacity
                  style={{padding: 4}}
                  onPress={() => {
                    showPopup({
                      ref: bottomSheetRef,
                      enableDismiss: true,
                      children: (
                        <View
                          style={{
                            height: Dimensions.get('window').height / 5,
                            padding: 16,
                            borderTopLeftRadius: 16,
                            borderTopRightRadius: 16,
                            width: '100%',
                            backgroundColor:
                              ColorThemes.light
                                .neutral_absolute_background_color,
                            gap: 8,
                          }}>
                          <Winicon
                            src="fill/travel/c-info"
                            size={16}
                            color={ColorThemes.light.infor_main_color}
                          />
                          <Text
                            style={{
                              ...TypoSkin.subtitle1,
                              color: ColorThemes.light.infor_main_color,
                            }}>
                            {`0.5% phí của người nhận. Trừ vào số point cần thực hiện (Chưa tính phí)`}
                          </Text>
                        </View>
                      ),
                    });
                  }}>
                  <Winicon
                    src="outline/travel/c-info"
                    size={16}
                    color={ColorThemes.light.primary_main_color}
                  />
                </TouchableOpacity>
              </View>

              <Text
                style={{
                  ...styles.currentPointsValue,
                  fontWeight: 'bold',
                  flex: 1,
                  textAlign: 'right',
                }}
                numberOfLines={3}>
                {formatNumber(totalReceive)} P
              </Text>
            </View>
          </View>
        )}
      </ScrollView>

      <View style={styles.buttonContainer}>
        {type === TransactionType.tranfer &&
          totalTransferWithFee > currentPoints && (
            <Text
              style={{
                ...styles.currentPointsLabel,
                color: ColorThemes.light.error_main_color,
                marginBottom: 8,
              }}>
              Số dư không đủ để thực hiện giao dịch.
            </Text>
          )}
        <AppButton
          title="Tiếp theo"
          onPress={continueTransfer}
          disabled={!isFormValid()}
          backgroundColor={
            isFormValid()
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          containerStyle={styles.button}
          borderColor="transparent"
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  buttonContainer: {
    padding: 16,
    backgroundColor: ColorThemes.light.white,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  button: {
    borderRadius: 24,
    height: 48,
    width: '100%',
    marginBottom: 16,
  },
  currentPointsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.neutral_lighter_border_color,
  },
  currentPointsLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 14,
  },
  currentPointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.primary_main_color,
  },
});

export default Step1TransferInfo;
