import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import i18n from './i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 🔹 Định nghĩa kiểu dữ liệu cho Context
interface LanguageContextType {
  language: string;
  changeLanguage: (lng: string) => Promise<void>;
}

// 🔹 Tạo Context với giá trị mặc định
const LanguageContext = createContext<LanguageContextType>({
  language: 'vi',
  changeLanguage: async () => {}, // Hàm mặc định, sẽ được override bởi Provider
});

// 🔹 Định nghĩa kiểu cho `children`
interface LanguageProviderProps {
  children: ReactNode;
}

// 🔹 Tạo Provider để bọc toàn bộ ứng dụng
export const LanguageProvider: React.FC<LanguageProviderProps> = ({
  children,
}) => {
  const [language, setLanguage] = useState<string>('vi');

  useEffect(() => {
    // Load ngôn ngữ từ AsyncStorage khi app khởi động
    const loadLanguage = async () => {
      const storedLang = await AsyncStorage.getItem('language');
      if (storedLang) {
        i18n.changeLanguage(storedLang);
        setLanguage(storedLang);
      }
    };
    loadLanguage();
  }, []);

  // 🔹 Hàm đổi ngôn ngữ
  const changeLanguage = async (lng: string) => {
    await AsyncStorage.setItem('language', lng);
    i18n.changeLanguage(lng);
    setLanguage(lng);
  };

  return (
    <LanguageContext.Provider value={{language, changeLanguage}}>
      {children}
    </LanguageContext.Provider>
  );
};

// 🔹 Hook để sử dụng trong component
export const useLanguage = (): LanguageContextType =>
  useContext(LanguageContext);
