/**
 * Deep Link Utilities
 * Centralized utilities for parsing and validating deeplinks
 */

import {RootScreen} from '../../router/router';
import {
  DeepLinkUrl,
  DeepLinkValidationResult,
  DeepLinkParsingResult,
  ParsedDeepLink,
  DeepLinkType,
  DEEPLINK_PATTERNS,
  VALID_SCHEMES,
  VALID_HOSTS,
} from './DeepLinkTypes';

/**
 * Validates if a URL is a valid deeplink
 */
export const validateDeepLink = (url: DeepLinkUrl): DeepLinkValidationResult => {
  if (!url || typeof url !== 'string') {
    return {
      isValid: false,
      error: 'URL is not a valid string',
    };
  }

  try {
    // Check for custom scheme
    if (url.startsWith('chainivo://')) {
      return {
        isValid: true,
        scheme: 'chainivo',
      };
    }

    // Check for HTTPS universal links
    if (url.startsWith('https://')) {
      const urlObj = new URL(url);
      const host = urlObj.host;
      
      if (VALID_HOSTS.includes(host as any)) {
        return {
          isValid: true,
          scheme: 'https',
          host,
        };
      }
      
      return {
        isValid: false,
        error: `Invalid host: ${host}`,
      };
    }

    return {
      isValid: false,
      error: 'URL does not match any supported scheme',
    };
  } catch (error) {
    return {
      isValid: false,
      error: `URL parsing error: ${error}`,
    };
  }
};

/**
 * Parses a deeplink URL into structured data
 */
export const parseDeepLink = (url: DeepLinkUrl): DeepLinkParsingResult => {
  // First validate the URL
  const validation = validateDeepLink(url);
  if (!validation.isValid) {
    return {
      success: false,
      error: validation.error,
    };
  }

  try {
    // Extract query parameters
    const queryParams = extractQueryParams(url);
    
    // Try to match against each pattern
    for (const [type, pattern] of Object.entries(DEEPLINK_PATTERNS)) {
      const match = url.match(pattern);
      if (match) {
        const parsedData = createParsedDeepLink(
          type.toLowerCase() as DeepLinkType,
          match,
          queryParams
        );
        
        return {
          success: true,
          data: parsedData,
        };
      }
    }

    // If no pattern matches, default to home
    return {
      success: true,
      data: {
        type: 'home',
        screen: RootScreen.navigateEComView,
        params: {},
        queryParams,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: `Parsing error: ${error}`,
    };
  }
};

/**
 * Creates a ParsedDeepLink object based on type and regex match
 */
const createParsedDeepLink = (
  type: DeepLinkType,
  match: RegExpMatchArray,
  queryParams: Record<string, string>
): ParsedDeepLink => {
  const id = match[3]; // ID is always the 3rd capture group in our patterns

  switch (type) {
    case 'posts':
      return {
        type,
        id,
        screen: RootScreen.PostDetail,
        params: {id},
        queryParams,
      };

    case 'news':
      return {
        type,
        id,
        screen: RootScreen.DetailNews,
        params: {id},
        queryParams,
      };

    case 'product':
      return {
        type,
        id,
        screen: RootScreen.ProductDetail,
        params: {id},
        queryParams,
      };

    case 'events':
      return {
        type,
        id,
        screen: RootScreen.DetailEvent,
        params: {id},
        queryParams,
      };

    case 'share':
      return {
        type,
        id,
        screen: RootScreen.login,
        params: {id},
        queryParams,
      };

    case 'home':
    default:
      return {
        type: 'home',
        screen: RootScreen.navigateEComView,
        params: {},
        queryParams,
      };
  }
};

/**
 * Extracts query parameters from URL
 */
const extractQueryParams = (url: string): Record<string, string> => {
  try {
    const urlObj = new URL(url.replace('chainivo://', 'https://chainivo.com/'));
    const params: Record<string, string> = {};
    
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });
    
    return params;
  } catch {
    return {};
  }
};

/**
 * Checks if a deeplink has a valid ID parameter
 */
export const hasValidId = (parsed: ParsedDeepLink): boolean => {
  return !!(parsed.id && parsed.id.trim().length > 0);
};

/**
 * Creates a deeplink URL for sharing
 */
export const createDeepLink = (type: DeepLinkType, id?: string, queryParams?: Record<string, string>): string => {
  let path = '';
  
  switch (type) {
    case 'posts':
      path = `posts/${id}`;
      break;
    case 'news':
      path = `news/${id}`;
      break;
    case 'product':
      path = `product/${id}`;
      break;
    case 'events':
      path = `events/${id}`;
      break;
    case 'share':
      path = `share/${id}`;
      break;
    case 'home':
    default:
      path = '';
      break;
  }

  let url = `chainivo://${path}`;
  
  if (queryParams && Object.keys(queryParams).length > 0) {
    const searchParams = new URLSearchParams(queryParams);
    url += `?${searchParams.toString()}`;
  }
  
  return url;
};

/**
 * Creates a universal link URL for sharing
 */
export const createUniversalLink = (type: DeepLinkType, id?: string, queryParams?: Record<string, string>): string => {
  let path = '';
  
  switch (type) {
    case 'posts':
      path = `posts/${id}`;
      break;
    case 'news':
      path = `news/${id}`;
      break;
    case 'product':
      path = `product/${id}`;
      break;
    case 'events':
      path = `events/${id}`;
      break;
    case 'share':
      path = `share/${id}`;
      break;
    case 'home':
    default:
      path = '';
      break;
  }

  let url = `https://chainivo.com/${path}`;
  
  if (queryParams && Object.keys(queryParams).length > 0) {
    const searchParams = new URLSearchParams(queryParams);
    url += `?${searchParams.toString()}`;
  }
  
  return url;
};
