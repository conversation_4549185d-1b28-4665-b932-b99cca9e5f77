import {Dimensions, Text, TouchableOpacity} from 'react-native';
import {View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  showDialog,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {CustomerRankType} from '../../../Config/Contanst';
import {useEffect, useRef, useState} from 'react';
import WScreenFooter from '../../../Screen/Layout/footer';
import {useForm} from 'react-hook-form';
import {TextFieldForm} from '../../Default/form/component-form';
import {DataController} from '../../../base/baseController';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {randomGID} from '../../../utils/Utils';
import {useTranslation} from '../../../locales/useTranslation';

export default function About({data, isProfile, onUpdate}: any) {
  const {t} = useTranslation();
  const bottomSheetRef = useRef<any>(null);
  const customer = useSelectorCustomerState().data;
  const dispatch: AppDispatch = useDispatch();

  const [rule, setRule] = useState<any[]>([]);
  const dialogRef = useRef<any>(null);

  const getRules = async () => {
    if (isProfile) return;
    const ruleController = new DataController('Rule');
    const result = await ruleController.getListSimple({
      query: `@GroupId:{${data?.Id}}`,
    });
    if (result.code === 200) {
      setRule(result.data);
    }
  };

  useEffect(() => {
    getRules();
  }, [data]);

  return (
    <View
      style={{
        flex: 1,
        padding: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
        gap: 16,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <View
        style={{
          borderRadius: 8,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          padding: 16,
          gap: 16,
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <Text
            style={{
              ...TypoSkin.heading6,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            Mô tả
          </Text>
          {!isProfile && customer?.Id === data?.CustomerId && (
            <TouchableOpacity
              style={{padding: 4}}
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  title: 'Edit description',
                  suffixAction: <View />,
                  prefixAction: (
                    <TouchableOpacity
                      onPress={() => hideBottomSheet(bottomSheetRef)}
                      style={{padding: 6, alignItems: 'center'}}>
                      <Winicon
                        src="outline/layout/xmark"
                        size={20}
                        color={ColorThemes.light.Neutral_Text_Color_Body}
                      />
                    </TouchableOpacity>
                  ),
                  children: (
                    <EditAbout
                      ref={bottomSheetRef}
                      currentData={data}
                      onUpdate={(value: any) => {
                        onUpdate(value);
                      }}
                    />
                  ),
                });
              }}>
              <Winicon src="fill/user interface/n-edit" size={16} />
            </TouchableOpacity>
          )}
        </View>
        {!isProfile && data?.Name && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <Text style={TypoSkin.regular2}>Tên Group</Text>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  flex: 1,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                  textAlign: 'right',
                  paddingLeft: 32,
                },
              ]}>
              {data?.Name ?? '-'}
            </Text>
          </View>
        )}
        {!isProfile && data?.Description && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <Text style={TypoSkin.regular2}>Mô tả</Text>
            <Text
              style={[
                TypoSkin.regular2,
                {
                  flex: 1,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                  textAlign: 'right',
                  paddingLeft: 32,
                },
              ]}>
              {data?.Description ?? '-'}
            </Text>
          </View>
        )}
        {isProfile && (
          <View
            style={{
              gap: 16,
            }}>
            {data?.Name && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Họ tên</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.Neutral_Text_Color_Title,
                      textAlign: 'right',
                    },
                  ]}>
                  {data?.Name ?? '-'}
                </Text>
              </View>
            )}
            {data?.Email && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Email</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.Neutral_Text_Color_Title,
                      textAlign: 'right',
                    },
                  ]}>
                  {data?.Email ?? '-'}
                </Text>
              </View>
            )}
            {data?.Mobile && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Số điện thoại</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.Neutral_Text_Color_Title,
                      textAlign: 'right',
                    },
                  ]}>
                  {customer.Id === data?.Id
                    ? data?.Mobile
                    : data?.Mobile?.replace(/(\d{3})\d{3}(\d{3})/, '$1***$2') ||
                      ''}
                </Text>
              </View>
            )}
            {data?.Rank && (
              <View
                style={{
                  flexDirection: 'row',
                  gap: 12,
                  alignItems: 'center',
                }}>
                <Text style={TypoSkin.regular2}>Rank</Text>
                <Text
                  style={[
                    TypoSkin.regular2,
                    {
                      flex: 1,
                      color: ColorThemes.light.Neutral_Text_Color_Title,
                      textAlign: 'right',
                    },
                  ]}>
                  {data?.Rank
                    ? `Hạng ${
                        data?.Rank == CustomerRankType.normal ? 'Thường' : 'VIP'
                      }`
                    : ''}
                </Text>
              </View>
            )}
            <View
              style={{
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>Địa chỉ</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {data?.Address ?? '-'}
              </Text>
            </View>
          </View>
        )}
      </View>
      {!isProfile ? (
        <View
          style={{
            borderRadius: 8,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            padding: 16,
            gap: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text
              style={{
                ...TypoSkin.heading8,
                color: ColorThemes.light.Neutral_Text_Color_Placeholder,
              }}>
              Quy định
            </Text>
            {!isProfile && customer?.Id === data?.CustomerId && (
              <TouchableOpacity
                style={{padding: 4}}
                onPress={() => {
                  showBottomSheet({
                    ref: bottomSheetRef,
                    enableDismiss: true,
                    title: 'Add rule',
                    suffixAction: <View />,
                    prefixAction: (
                      <TouchableOpacity
                        onPress={() => hideBottomSheet(bottomSheetRef)}
                        style={{padding: 6, alignItems: 'center'}}>
                        <Winicon
                          src="outline/layout/xmark"
                          size={20}
                          color={ColorThemes.light.Neutral_Text_Color_Body}
                        />
                      </TouchableOpacity>
                    ),
                    children: (
                      <AddRule
                        ref={bottomSheetRef}
                        group={data}
                        onUpdate={(value: any) => {
                          setRule(prev => [...prev, value]);
                        }}
                      />
                    ),
                  });
                }}>
                <Winicon src="outline/user interface/c-add" size={16} />
              </TouchableOpacity>
            )}
          </View>

          {rule.map((item: any, index: number) => (
            <View key={item.Id} style={{gap: 8}}>
              <ListTile
                key={item.Id}
                style={{
                  borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                  borderWidth: 1,
                }}
                listtileStyle={{alignItems: 'flex-start'}}
                leading={
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.Neutral_Text_Color_Placeholder,
                    }}>
                    #{index + 1}.{' '}
                  </Text>
                }
                title={item?.Name ?? ''}
                titleStyle={{
                  ...TypoSkin.heading7,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}
                subtitle={`${item?.Content ?? ''}`}
                trailing={
                  customer?.Id === data?.CustomerId ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        gap: 4,
                        alignItems: 'center',
                      }}>
                      <TouchableOpacity
                        style={{padding: 4}}
                        onPress={() => {
                          showBottomSheet({
                            ref: bottomSheetRef,
                            enableDismiss: true,
                            title: 'Edit rule',
                            suffixAction: <View />,
                            prefixAction: (
                              <TouchableOpacity
                                onPress={() => hideBottomSheet(bottomSheetRef)}
                                style={{padding: 6, alignItems: 'center'}}>
                                <Winicon
                                  src="outline/layout/xmark"
                                  size={20}
                                  color={
                                    ColorThemes.light.Neutral_Text_Color_Body
                                  }
                                />
                              </TouchableOpacity>
                            ),
                            children: (
                              <AddRule
                                ref={bottomSheetRef}
                                group={data}
                                currentData={item}
                                onUpdate={(value: any) => {
                                  setRule(prev =>
                                    prev.map((a: any) => {
                                      if (a.Id === value?.Id) {
                                        return value;
                                      }
                                      return a;
                                    }),
                                  );
                                }}
                              />
                            ),
                          });
                        }}>
                        <Winicon src="fill/user interface/n-edit" size={16} />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{padding: 4}}
                        onPress={() => {
                          showDialog({
                            ref: dialogRef,
                            status: ComponentStatus.WARNING,
                            title: 'Bạn có chắc muốn xóa quy định này?',
                            onSubmit: async () => {
                              const ruleController = new DataController('Rule');
                              const res = await ruleController.delete([
                                item.Id,
                              ]);
                              if (res?.code === 200) {
                                setRule(prev =>
                                  prev.filter((a: any) => a.Id !== item.Id),
                                );
                                showSnackbar({
                                  message: 'Đã xóa thành công',
                                  status: ComponentStatus.SUCCSESS,
                                });
                              } else {
                                showSnackbar({
                                  message: res?.message ?? 'Đã có lỗi xảy ra',
                                  status: ComponentStatus.ERROR,
                                });
                              }
                            },
                          });
                        }}>
                        <Winicon
                          src="outline/user interface/c-delete"
                          size={16}
                        />
                      </TouchableOpacity>
                    </View>
                  ) : null
                }
              />
            </View>
          ))}
        </View>
      ) : null}
    </View>
  );
}

const EditAbout = ({
  ref,
  currentData,
  onUpdate,
}: {
  ref: any;
  currentData: any;
  onUpdate: any;
}) => {
  const [loading, setLoading] = useState(false);

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {...currentData},
  });
  const groupController = new DataController('Group');
  const submitForm = async (data: any) => {
    const res = await groupController.edit([{...data, memberList: undefined}]);
    if (res?.code === 200) {
      hideBottomSheet(ref);
      onUpdate({Name: data.Name, Description: data.Description});
      showSnackbar({
        message: 'Cập nhật thành công',
        status: ComponentStatus.SUCCSESS,
      });
    } else {
      showSnackbar({
        message: res?.message ?? 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const errorForm = (errors: any) => {
    console.log(errors);
  };

  return (
    <View
      style={{
        width: '100%',
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        height: Dimensions.get('window').height / 2,
      }}>
      <View style={{padding: 16, gap: 24}}>
        <TextFieldForm
          required
          style={{
            width: '100%',
            backgroundColor: '#fff',
            borderRadius: 8,
          }}
          placeholder={t('community.groupName')}
          label={t('community.groupName')}
          control={methods.control}
          errors={methods.formState.errors}
          register={methods.register}
          name="Name"
          textFieldStyle={{paddingHorizontal: 16}}
        />
        <TextFieldForm
          label={t('common.description')}
          textStyle={{textAlignVertical: 'top'}}
          numberOfLines={10}
          multiline={true}
          textFieldStyle={{
            paddingHorizontal: 16,
            paddingTop: 16,
            paddingBottom: 16,
            height: 100,
            justifyContent: 'flex-start',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          style={{width: '100%'}}
          register={methods.register}
          control={methods.control}
          errors={methods.formState.errors}
          name="Description"
        />
      </View>
      <WScreenFooter style={{marginHorizontal: 16}}>
        <AppButton
          title={t('community.save')}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={methods.handleSubmit(submitForm, errorForm)}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </WScreenFooter>
    </View>
  );
};

const AddRule = ({
  ref,
  group,
  currentData,
  onUpdate,
}: {
  ref: any;
  group: any;
  currentData?: any;
  onUpdate: any;
}) => {
  const [loading, setLoading] = useState(false);

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: currentData
      ? {...currentData}
      : {
          Id: randomGID(),
          DateCreated: new Date().getTime(),
          GroupId: group.Id,
        },
  });
  const ruleController = new DataController('Rule');
  const submitForm = async (data: any) => {
    const res = currentData
      ? await ruleController.edit([
          {...currentData, Name: data.Name, Content: data.Content},
        ])
      : await ruleController.add([
          {
            ...data,
          },
        ]);
    if (res?.code === 200) {
      hideBottomSheet(ref);
      onUpdate(res.data[0]);
      showSnackbar({
        message: 'Cập nhật thành công',
        status: ComponentStatus.SUCCSESS,
      });
    } else {
      showSnackbar({
        message: res?.message ?? 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const errorForm = (errors: any) => {
    console.log(errors);
  };

  return (
    <View
      style={{
        width: '100%',
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        height: Dimensions.get('window').height / 2,
      }}>
      <View style={{padding: 16, gap: 24}}>
        <TextFieldForm
          required
          style={{
            width: '100%',
            backgroundColor: '#fff',
            borderRadius: 8,
          }}
          placeholder={t('community.ruleName')}
          label={t('community.ruleName')}
          control={methods.control}
          errors={methods.formState.errors}
          register={methods.register}
          name="Name"
          textStyle={{textAlignVertical: 'top', paddingBottom: 3}}
          textFieldStyle={{
            paddingHorizontal: 16,
            paddingBottom: 16,
          }}
        />
        <TextFieldForm
          label={t('community.content')}
          textStyle={{textAlignVertical: 'top'}}
          numberOfLines={10}
          multiline={true}
          textFieldStyle={{
            paddingHorizontal: 16,
            paddingTop: 16,
            paddingBottom: 16,
            height: 100,
            justifyContent: 'flex-start',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          style={{width: '100%'}}
          register={methods.register}
          control={methods.control}
          errors={methods.formState.errors}
          name="Content"
        />
      </View>
      <WScreenFooter style={{marginHorizontal: 16}}>
        <AppButton
          title={t('community.save')}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
          borderColor="transparent"
          containerStyle={{
            flex: 1,
            borderRadius: 8,
            paddingHorizontal: 12,
            paddingVertical: 5,
          }}
          onPress={methods.handleSubmit(submitForm, errorForm)}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </WScreenFooter>
    </View>
  );
};
