// src/utils/testDeepLink.ts
// Simple utility to test deep links

import {Linking, Alert, Platform} from 'react-native';
import {getDataToAsyncStorage} from './AsyncStorage';

/**
 * Test deep link functionality
 */
export const testDeepLink = async (url: string): Promise<void> => {
  try {
    console.log(`🧪 Testing deep link: ${url}`);
    console.log('📱 Platform:', Platform.OS);

    // Check if the URL can be opened
    const canOpen = await Linking.canOpenURL(url);
    console.log(`🔗 Can open URL: ${canOpen}`);

    if (canOpen) {
      // Add a small delay to see the log
      console.log('⏳ Opening URL in 1 second...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Open the URL
      await Linking.openURL(url);
      console.log(`✅ Successfully opened: ${url}`);
      console.log(
        '👀 Watch console for "[DeepLinkListener] URL received" message',
      );

      // Check after a delay if the listener worked
      setTimeout(() => {
        console.log(
          '🔍 If you see "[DeepLinkListener] URL received" above, the listener is working!',
        );
        console.log(
          '🔍 If not, check App.tsx to ensure DeepLinkListener is properly setup',
        );
      }, 2000);
    } else {
      console.warn(`❌ Cannot open URL: ${url}`);
      Alert.alert('Test Failed', `Cannot open URL: ${url}`);
    }
  } catch (error) {
    console.error(`❌ Error testing deep link: ${url}`, error);
    Alert.alert('Test Error', `Error testing: ${url}\n${error}`);
  }
};

/**
 * Check if RefCodeShared was saved correctly
 */
export const checkRefCodeShared = async (): Promise<string | null> => {
  try {
    const refCode = await getDataToAsyncStorage('RefCodeShared');
    console.log('🔍 Current RefCodeShared in AsyncStorage:', refCode);

    Alert.alert(
      'RefCodeShared Status',
      refCode ? `Current value: ${refCode}` : 'No RefCodeShared found',
      [{text: 'OK'}],
    );

    return refCode;
  } catch (error) {
    console.error('❌ Error checking RefCodeShared:', error);
    Alert.alert('Error', 'Failed to check RefCodeShared');
    return null;
  }
};

/**
 * Test share links specifically
 */
export const testShareLinks = (): void => {
  const shareLinks = [
    'chainivo://share/TEST123',
    'chainivo://share/ABC456?ref=social&campaign=test',
    'https://chainivo.com/share/WEB789',
    'https://www.chainivo.com/share/WWW999?utm_source=app',
  ];

  Alert.alert(
    'Test Share Links',
    'Select a share link to test:',
    [
      ...shareLinks.map((url, index) => ({
        text: `${index + 1}. ${url.split('/').pop()}`,
        onPress: () => testDeepLink(url),
      })),
      {
        text: 'Check RefCodeShared',
        onPress: checkRefCodeShared,
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ],
    {cancelable: true},
  );
};

/**
 * Test all types of deep links
 */
export const testAllDeepLinks = (): void => {
  const testLinks = [
    {name: 'Share Link', url: 'chainivo://share/TEST123'},
    {name: 'Product Link', url: 'chainivo://product/12345'},
    {name: 'Post Link', url: 'chainivo://posts/67890'},
    {name: 'News Link', url: 'chainivo://news/11111'},
    {name: 'Event Link', url: 'chainivo://events/22222'},
    {name: 'Universal Share', url: 'https://chainivo.com/share/UNI123'},
  ];

  Alert.alert(
    'Test Deep Links',
    `Platform: ${Platform.OS}\nSelect a link to test:`,
    [
      ...testLinks.map(link => ({
        text: link.name,
        onPress: () => testDeepLink(link.url),
      })),
      {
        text: 'Test Share Links',
        onPress: testShareLinks,
      },
      {
        text: 'Check RefCodeShared',
        onPress: checkRefCodeShared,
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ],
    {cancelable: true},
  );
};

/**
 * Generate a test share link
 */
export const generateTestShareLink = (refCode: string): string => {
  return `chainivo://share/${refCode}?ref=test&campaign=manual&utm_source=app`;
};

/**
 * Test URL listener directly
 */
export const testUrlListener = (): void => {
  console.log('🧪 Testing URL Listener Directly...');

  let listenerWorking = false;
  let subscription: any = null;

  const testHandler = (event: {url: string}) => {
    listenerWorking = true;
    console.log('🎯 LISTENER TEST SUCCESS - received:', event.url);
    Alert.alert(
      'Listener Test',
      `✅ Listener working!\nReceived: ${event.url}`,
    );

    // Clean up test listener
    subscription?.remove();
  };

  try {
    // Try to set up a test listener
    subscription = Linking.addEventListener('url', testHandler);
    console.log('✅ Test listener setup successful');

    // Test with a simple URL
    const testUrl = 'chainivo://share/LISTENER_TEST';

    Alert.alert(
      'Listener Test',
      `Test listener setup complete.\n\nNow testing with: ${testUrl}\n\nWatch console for results.`,
      [
        {
          text: 'Test Now',
          onPress: async () => {
            try {
              await Linking.openURL(testUrl);

              // Check if listener worked after 2 seconds
              setTimeout(() => {
                if (!listenerWorking) {
                  console.warn('❌ Listener test failed - no event received');
                  Alert.alert(
                    'Listener Test Failed',
                    'No URL event received. The listener may not be working properly.',
                    [{text: 'OK'}],
                  );
                }
                subscription?.remove();
              }, 2000);
            } catch (error) {
              console.error('❌ Test URL open failed:', error);
              Alert.alert('Test Error', `Failed to open test URL: ${error}`);
              subscription?.remove();
            }
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => subscription?.remove(),
        },
      ],
    );
  } catch (error) {
    console.error('❌ Test listener setup failed:', error);
    Alert.alert('Test Failed', `Cannot setup test listener: ${error}`);
  }
};

/**
 * Show quick test menu
 */
export const showQuickTestMenu = (): void => {
  Alert.alert(
    'Deep Link Quick Test',
    'Choose an action:',
    [
      {
        text: '🧪 Test URL Listener',
        onPress: testUrlListener,
      },
      {
        text: '🔗 Test Share Links',
        onPress: testShareLinks,
      },
      {
        text: '📱 Test All Links',
        onPress: testAllDeepLinks,
      },
      {
        text: '🔍 Check RefCodeShared',
        onPress: checkRefCodeShared,
      },
      {
        text: '✏️ Custom Share Link',
        onPress: () => {
          Alert.prompt(
            'Custom Share Link',
            'Enter reference code:',
            [
              {text: 'Cancel', style: 'cancel'},
              {
                text: 'Test',
                onPress: refCode => {
                  if (refCode) {
                    const shareLink = generateTestShareLink(refCode);
                    testDeepLink(shareLink);
                  }
                },
              },
            ],
            'plain-text',
          );
        },
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ],
    {cancelable: true},
  );
};

// Export for easy testing in development
if (__DEV__) {
  // Make functions available globally for testing in console
  (global as any).testDeepLink = testDeepLink;
  (global as any).checkRefCodeShared = checkRefCodeShared;
  (global as any).testShareLinks = testShareLinks;
  (global as any).testAllDeepLinks = testAllDeepLinks;
  (global as any).testUrlListener = testUrlListener;
  (global as any).showQuickTestMenu = showQuickTestMenu;

  console.log('🧪 Deep link testing functions available globally:');
  console.log('- testDeepLink(url)');
  console.log('- checkRefCodeShared()');
  console.log('- testShareLinks()');
  console.log('- testAllDeepLinks()');
  console.log('- testUrlListener() - Test if listener is working');
  console.log('- showQuickTestMenu()');
  console.log('');
  console.log('🔗 Quick test: testDeepLink("chainivo://share/TEST123")');
  console.log(
    '📱 App should now have DeepLinkListener inside NavigationContainer',
  );
}
