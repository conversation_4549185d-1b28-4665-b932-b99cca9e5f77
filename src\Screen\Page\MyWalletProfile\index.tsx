import React, {useRef} from 'react';
import {ScrollView, View, Text, StyleSheet, RefreshControl} from 'react-native';

import {ColorThemes} from '../../../assets/skin/colors';
import {
  ComponentStatus,
  showSnackbar,
  showDialog,
  FDialog,
} from 'wini-mobile-components';
import {navigate, RootScreen} from '../../../router/router';
import {LoadingUI} from '../../../features/loading';
import {InforHeader} from '../../Layout/headers/inforHeader';
import {TransactionType} from '../../../Config/Contanst';
import {Ultis} from '../../../utils/Utils';

import {useMyWalletProfile} from './hooks/useMyWalletProfile';
import QRSection from './components/QRSection';
import HistorySection from './components/HistorySection';

const MyWalletProfile = () => {
  const {
    customer,
    walletId,
    transactionHistory,
    walletBalance,
    tokenBalance,
    loading,
    refreshing,
    handleRefresh,
    copyToClipboard,
  } = useMyWalletProfile();
  const dialogRef = useRef<any>(null);

  const actions = [
    {id: 0, name: 'Sao chép'},
    {id: 1, name: 'Chuyển point nội bộ'},
    {id: 2, name: 'Rút điểm CAN'},
    ...(customer?.IsAdmin == true ? [{id: 3, name: 'Chia quỹ thưởng'}] : []),
  ];

  const onActionPress = (id: number) => {
    if (id === 2) {
      showDialog({
        ref: dialogRef,
        status: ComponentStatus.INFOR,
        title:
          'Chức năng này đang trong giai đoạn phát triển, vui lòng quay lại sau',
        onSubmit: async () => {},
      });
      return;
    }

    if (id === 1 || id === 2) {
      if (!customer?.Id) return;
      if (!customer.IsVerify) {
        showSnackbar({
          message: 'Vui lòng xác minh tài khoản',
          status: ComponentStatus.ERROR,
        });
        return;
      }
      if (!customer?.IsEnable2FA) {
        showSnackbar({
          message: 'Vui lòng Bật xác thực 2 lớp để thực hiện giao dịch',
          status: ComponentStatus.ERROR,
        });
        return;
      }
    }
    if (id === 3) {
      return navigate(RootScreen.BonusFundPage);
    }

    if (id === 0) copyToClipboard();
    else if (id === 1)
      navigate(RootScreen.TransferCANPoint, {Type: TransactionType.tranfer});
    else if (id === 2)
      navigate(RootScreen.TransferCANPoint, {Type: TransactionType.Withdraw});
  };

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <InforHeader title={'Ví của tôi'} />
      {loading ? (
        <LoadingUI isLoading={loading} />
      ) : (
        <ScrollView
          style={{flex: 1}}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[ColorThemes.light.primary_main_color]}
              tintColor={ColorThemes.light.primary_main_color}
            />
          }>
          <QRSection
            walletId={walletId}
            actions={actions}
            onCopy={copyToClipboard}
            onActionPress={onActionPress}
          />

          <View style={styles.balanceSectionWallet}>
            <View>
              <Text style={styles.balanceLabel}>POINT</Text>
              <Text style={styles.balanceAmount}>
                {Ultis.money(walletBalance)}
              </Text>
            </View>
            <View style={{marginVertical: 16}}>
              <Text style={styles.balanceLabel}>TOKEN</Text>
              <Text style={styles.balanceAmount}>
                {Ultis.money(tokenBalance)}
              </Text>
            </View>
          </View>

          {/* <StatsSection income={incomeAmount} expense={expenseAmount} /> */}

          <HistorySection
            data={transactionHistory as any}
            onPressViewAll={() => navigate(RootScreen.TransactionHistory)}
          />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  balanceSectionWallet: {
    alignItems: 'flex-start',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 10,
    width: '100%',
  },
  balanceLabel: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
  balanceAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: ColorThemes.light.primary_main_color,
  },
});

export default MyWalletProfile;
