import React from 'react';
import {View, Text} from 'react-native';
import {AppButton, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {stylesDefault} from './styles';
import {useTranslation} from '../../../../locales/useTranslation';

interface Props {
  listItems?: Array<any>;
  listTags?: Array<any>;
  onPressSeeMore?: () => void;
}

export const renderListItems = (props: Props) => {
  if (!props.listItems?.length) return null;

  return (
    <View style={{width: '100%', paddingTop: 16}}>
      {props.listItems.map(item => (
        <View
          key={item.Id}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            paddingVertical: 4,
            gap: 8,
          }}>
          <View style={{width: 16, borderRadius: 100}}>
            {item.icon ? (
              <Winicon src={item.icon} size={16} />
            ) : (
              <Text style={[stylesDefault.inforTitle]}>*</Text>
            )}
          </View>
          <Text style={[stylesDefault.inforTitle, {color: '#313135'}]}>
            {item.title}
          </Text>
        </View>
      ))}
    </View>
  );
};

export const renderSeeMoreButton = (props: Props) => {
  const {t} = useTranslation();

  if (!props.onPressSeeMore || !props.listItems?.length) return null;

  return (
    <AppButton
      title={t('community.seeMore')}
      containerStyle={{
        justifyContent: 'flex-start',
        alignSelf: 'baseline',
        marginVertical: 8,
      }}
      backgroundColor={'transparent'}
      textStyle={TypoSkin.buttonText3}
      borderColor="transparent"
      suffixIconSize={16}
      suffixIcon={'outline/arrows/circle-arrow-right'}
      onPress={props.onPressSeeMore}
      textColor={ColorThemes.light.Info_Color_Main}
    />
  );
};

export const renderTags = (props: Props) => {
  if (!props.listTags?.length) return null;

  return (
    <View style={{width: '100%', flexDirection: 'row', gap: 8}}>
      {props.listTags.map(item => (
        <View
          key={item.Id}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            paddingHorizontal: 8,
            borderRadius: 24,
            borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
            borderWidth: 1,
            paddingVertical: 4,
            gap: 4,
          }}>
          <Text style={[stylesDefault.inforTitle]}>{item.title}</Text>
          <Winicon src={'outline/arrows/right-arrow'} size={12} />
        </View>
      ))}
    </View>
  );
};
