import ConfigAP<PERSON> from '../Config/ConfigAPI';
import {Alert, Share} from 'react-native';

const onShare = async ({message}: {message: string}) => {
  try {
    const result = await Share.share({
      message: message,
    });
    if (result.action === Share.sharedAction) {
      if (result.activityType) {
        // shared with activity type of result.activityType
      } else {
        // shared
      }
    } else if (result.action === Share.dismissedAction) {
      // dismissed
    }
  } catch (error: any) {
    Alert.alert(error.message);
  }
};

// Chia sẻ bài viết
export const sharePost = (postId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://posts/${postId}?ref=share`;
  onShare({message: url});
};

// Chia sẻ tin tức
export const shareNews = (newsId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://news/${newsId}?ref=share`;
  onShare({message: url});
};

// Chia sẻ sự kiện
export const shareEvents = (newsId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://events/${newsId}?ref=share`;
  onShare({message: url});
};

// Chia sẻ sản phẩm
export const shareProduct = (productId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://product/${productId}?ref=share`;
  onShare({message: url});
};

// Chia sẻ QR code
export const shareQRCode = (qrId: string) => {
  const url = `${ConfigAPI.urlDeeplink}chainivo://share/${qrId}?ref=qrcode`;
  onShare({message: url});
};
