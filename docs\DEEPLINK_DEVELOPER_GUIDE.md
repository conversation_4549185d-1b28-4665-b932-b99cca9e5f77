# Deep Link Developer Guide

## 🎯 Overview

This guide provides comprehensive documentation for developers working with the new DeepLink system in the Chainivo app.

## 🏗️ Architecture

### Core Components

```
DeepLinkManager (Singleton)
├── DeepLinkUtils (Parsing & Validation)
├── DeepLinkTypes (Type Definitions)
└── DeepLinkProvider (React Wrapper)
```

### Flow Diagram

```
URL Input → Validation → Parsing → Navigation → Error Handling
```

## 📚 API Reference

### DeepLinkManager

```typescript
import {deepLinkManager} from 'src/services/deeplink';

// Initialize (done automatically in App.tsx)
deepLinkManager.initialize(navigationRef, config);

// Handle deeplink manually
const result = await deepLinkManager.handleDeepLink('chainivo://posts/123');

// Add event listeners
deepLinkManager.addEventListener('url_received', (event, data) => {
  console.log('URL received:', data.url);
});

// Cleanup
deepLinkManager.cleanup();
```

### DeepLinkUtils

```typescript
import {
  validateDeepLink,
  parseDeepLink,
  createDeepLink,
  createUniversalLink,
} from 'src/services/deeplink';

// Validate URL
const validation = validateDeepLink('chainivo://posts/123');
if (validation.isValid) {
  // URL is valid
}

// Parse URL
const result = parseDeepLink('chainivo://posts/123');
if (result.success) {
  const {type, id, screen, params} = result.data;
}

// Create deeplinks
const customLink = createDeepLink('posts', '123', {ref: 'share'});
const universalLink = createUniversalLink('posts', '123');
```

## 🔧 Configuration

### iOS (Info.plist)

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>chainivo.deeplink</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>chainivo</string>
    </array>
  </dict>
</array>

<key>com.apple.developer.associated-domains</key>
<array>
  <string>applinks:chainivo.com</string>
  <string>applinks:www.chainivo.com</string>
</array>
```

### Android (AndroidManifest.xml)

```xml
<!-- Custom scheme -->
<intent-filter>
  <action android:name="android.intent.action.VIEW" />
  <category android:name="android.intent.category.DEFAULT" />
  <category android:name="android.intent.category.BROWSABLE" />
  <data android:scheme="chainivo" />
</intent-filter>

<!-- Universal links -->
<intent-filter android:autoVerify="true">
  <action android:name="android.intent.action.VIEW" />
  <category android:name="android.intent.category.DEFAULT" />
  <category android:name="android.intent.category.BROWSABLE" />
  <data android:scheme="https" android:host="chainivo.com" />
</intent-filter>
```

## 🎨 Usage Examples

### Basic Usage

```typescript
// App.tsx - Already configured
import DeepLinkProvider from 'src/components/DeepLinkProvider';

<NavigationContainer ref={navigationRef}>
  <DeepLinkProvider navigationRef={navigationRef}>
    <YourAppContent />
  </DeepLinkProvider>
</NavigationContainer>;
```

### Manual Deeplink Handling

```typescript
import {deepLinkManager} from 'src/services/deeplink';

const handleCustomDeeplink = async (url: string) => {
  const result = await deepLinkManager.handleDeepLink(url);

  if (result.success) {
    console.log('Navigation successful:', result.screen);
  } else {
    console.error('Navigation failed:', result.error);
  }
};
```

### Creating Share Links

```typescript
import {createDeepLink, createUniversalLink} from 'src/services/deeplink';

// For in-app sharing (custom scheme)
const sharePost = (postId: string) => {
  const url = createDeepLink('posts', postId, {
    ref: 'share',
    campaign: 'social',
  });
  Share.share({message: url});
};

// For external sharing (universal links)
const sharePostExternal = (postId: string) => {
  const url = createUniversalLink('posts', postId, {
    utm_source: 'app',
    utm_campaign: 'share',
  });
  Share.share({message: url});
};
```

### Event Listening

```typescript
import {deepLinkManager, DeepLinkEvent} from 'src/services/deeplink';

useEffect(() => {
  const handleUrlReceived = (event: DeepLinkEvent, data: any) => {
    // Track analytics
    analytics.track('deeplink_received', {
      url: data.url,
      timestamp: Date.now(),
    });
  };

  const handleNavigationError = (event: DeepLinkEvent, data: any) => {
    // Log errors
    crashlytics.recordError(new Error(data.error));
  };

  deepLinkManager.addEventListener('url_received', handleUrlReceived);
  deepLinkManager.addEventListener('navigation_error', handleNavigationError);

  return () => {
    deepLinkManager.removeEventListener('url_received', handleUrlReceived);
    deepLinkManager.removeEventListener(
      'navigation_error',
      handleNavigationError,
    );
  };
}, []);
```

## 🔍 Debugging

### Enable Logging

```typescript
// In development
deepLinkManager.initialize(navigationRef, {
  enableLogging: true,
  fallbackScreen: RootScreen.navigateEComView,
});
```

### Console Logs

Look for these patterns:

```
[DeepLinkManager] Handling deeplink: chainivo://posts/123
[DeepLinkManager] Successfully navigated to PostDetail
[DeepLinkProvider] URL received: chainivo://posts/123
```

### Testing Functions

```typescript
// Available in development
global.testDeepLink = (url: string) => {
  deepLinkManager.handleDeepLink(url);
};

// Usage in console
testDeepLink('chainivo://posts/123');
```

## 🧪 Testing

### Unit Tests

```typescript
import {validateDeepLink, parseDeepLink} from 'src/services/deeplink';

describe('DeepLink', () => {
  it('should validate custom scheme', () => {
    const result = validateDeepLink('chainivo://posts/123');
    expect(result.isValid).toBe(true);
  });

  it('should parse posts URL', () => {
    const result = parseDeepLink('chainivo://posts/123');
    expect(result.success).toBe(true);
    expect(result.data?.type).toBe('posts');
  });
});
```

### Integration Tests

```typescript
import DeepLinkManager from 'src/services/deeplink/DeepLinkManager';

describe('DeepLinkManager', () => {
  it('should handle valid URLs', async () => {
    const manager = DeepLinkManager.getInstance();
    manager.initialize(mockNavigationRef);

    const result = await manager.handleDeepLink('chainivo://posts/123');
    expect(result.success).toBe(true);
  });
});
```

## 🚀 Performance

### Best Practices

1. **Lazy Loading**: DeepLinkManager is a singleton, initialized only when needed
2. **Error Boundaries**: Wrap deeplink handling in try-catch blocks
3. **Debouncing**: Avoid rapid successive deeplink calls
4. **Memory Management**: Cleanup event listeners on unmount

### Monitoring

```typescript
// Track performance
const startTime = Date.now();
const result = await deepLinkManager.handleDeepLink(url);
const duration = Date.now() - startTime;

analytics.track('deeplink_performance', {
  url,
  duration,
  success: result.success,
});
```

## 🔒 Security

### URL Validation

- All URLs are validated before processing
- Only whitelisted schemes and hosts are allowed
- Malformed URLs are rejected gracefully

### Parameter Sanitization

```typescript
// IDs are validated
const hasValidId = (parsed: ParsedDeepLink): boolean => {
  return !!(parsed.id && parsed.id.trim().length > 0);
};
```

## 🐛 Troubleshooting

### Common Issues

1. **Navigation not working**

   - Check if navigationRef is properly initialized
   - Verify screen names match RootScreen enum

2. **URLs not opening app**

   - Check iOS/Android configuration
   - Verify URL scheme registration

3. **Share links not saving RefCode**
   - Check AsyncStorage permissions
   - Verify URL parsing for share type

### Error Codes

- `INVALID_URL`: URL doesn't match supported patterns
- `PARSING_ERROR`: Failed to parse URL structure
- `NAVIGATION_ERROR`: Failed to navigate to screen
- `MANAGER_NOT_INITIALIZED`: DeepLinkManager not properly set up

## 📈 Analytics Integration

```typescript
// Track deeplink events
deepLinkManager.addEventListener('url_received', (event, data) => {
  analytics.track('deeplink_received', {
    url: data.url,
    source: 'app_link',
  });
});

deepLinkManager.addEventListener('navigation_success', (event, data) => {
  analytics.track('deeplink_navigation_success', {
    screen: data.screen,
    params: data.params,
  });
});
```

## 🔄 Migration from Old System

If migrating from the old DeepLinkListener:

1. Replace `DeepLinkListener` with `DeepLinkProvider`
2. Update imports to use new service
3. Test all deeplink scenarios
4. Remove old files after verification

## 📞 Support

For issues or questions:

1. Check console logs for error messages
2. Verify configuration files
3. Test with manual deeplink guide
4. Contact development team with specific error details

## 📚 Related Documentation

- [Testing Guide](./DEEPLINK_TESTING_GUIDE.md) - Comprehensive testing instructions
- [Migration Plan](./DEEPLINK_MIGRATION_PLAN.md) - Migration from old system
- [API Reference](../src/services/deeplink/README.md) - Detailed API documentation
