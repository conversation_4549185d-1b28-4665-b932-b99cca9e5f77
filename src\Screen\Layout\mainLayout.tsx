import {
  Keyboard,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {
  ComponentStatus,
  FDialog,
  showDialog,
  showSnackbar,
} from 'wini-mobile-components';
import Home from '../Page/Home';
import {ColorThemes} from '../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import {useEffect, useRef, useState} from 'react';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import CustomTabBar from '../../components/CustomTabBar';
import {navigateReset, RootScreen} from '../../router/router';
import {useDispatch} from 'react-redux';
import MyWallet from '../Page/myWallet';
import ChatMainScreen from '../../modules/chat/screens/ChatMainScreen';
import NewsIndex from '../../modules/news/newsIndex';
import {TypoSkin} from '../../assets/skin/typography';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useForm} from 'react-hook-form';
import {TextFieldForm} from '../../modules/Default/form/component-form';
import ProductIndex from '../../modules/Product/ProductIndex';
import {DataController} from '../../base/baseController';
import {CustomerStatus} from '../../Config/Contanst';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import HomeCommunity from '../../modules/community/pages/Home';
import {validatePhoneNumber} from '../../utils/validate';
import ConfigAPI from '../../Config/ConfigAPI';
import ManageShop from '../../modules/shop/ManageShop';
import {CustomerDA} from 'modules/customer/da';
import { t } from 'i18next';
const Tab = createBottomTabNavigator();

const bottomNavigateData = [
  {
    id: 0,
    name: 'home',
    component: Home,
  },
  {
    id: 5,
    name: 'news',
    component: NewsIndex,
  },
  {
    id: 1,
    name: 'products',
    component: ProductIndex,
  },
  {
    id: 2,
    name: 'QRCode',
    component: MyWallet,
  },
  {
    id: 3,
    name: 'community',
    component: HomeCommunity,
  },
  {
    id: 6,
    name: 'chat',
    component: ChatMainScreen,
  },
  {
    id: 4,
    name: 'profile',
    component: ManageShop,
  },
];

export const dialogCheckAcc = (ref: any) => {
  showDialog({
    ref: ref,
    status: ComponentStatus.WARNING,
    title: t('common.noLogin'),
    onSubmit: async () => {
      navigateReset(RootScreen.login);
    },
  });
};

export default function EComLayout() {
  const route = useRoute<any>();
  const dialogCheckAccRef = useRef<any>(null);
  const user = useSelectorCustomerState().data;
  const dispatch = useDispatch<any>();
  const isFirstTime = route.params?.isFirstTime;
  const [isShowDialog, setIsShowDialog] = useState<boolean>(
    isFirstTime ?? false,
  );
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {RefCode: ''},
  });
  const customerController = new DataController('Customer');

  useEffect(() => {
    if (user?.ParentId) setIsShowDialog(false);
  }, [user]);

  useEffect(() => {
    if (isShowDialog) {
      // check local storage
      getDataToAsyncStorage('RefCodeShared').then(rs => {
        if (rs) {
          // for deep link
          methods.setValue('RefCode', rs);
        }
      });
    }
  }, [user, methods, isShowDialog]);

  useEffect(() => {
    if (isShowDialog && user) {
      showDialog({
        ref: dialogCheckAccRef,
        status: ComponentStatus.INFOR,
        title: t('common.welcome'),
        content: (
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={{height: 200, gap: 8}}>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.neutral_main_color,
                  textAlign: 'center',
                }}>
                {t('common.welcomeMessage')}
              </Text>
              <TextFieldForm
                control={methods.control}
                name="RefCode"
                placeholder={t('common.refCodePlaceholder')}
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  paddingLeft: 8,
                  backgroundColor: ColorThemes.light.transparent,
                  marginBottom: 8,
                }}
                register={methods.register}
                onBlur={async (ev: string) => {
                  var code = ev.trim();
                }}
              />
              <TextFieldForm
                required
                style={{
                  width: '100%',
                  backgroundColor: '#fff',
                  borderRadius: 8,
                }}
                placeholder={t('common.phoneNumber')}
                control={methods.control}
                register={methods.register}
                errors={methods.formState.errors}
                name="Mobile"
                textFieldStyle={{padding: 16}}
                type="number-pad"
                onBlur={async (ev: string) => {
                  // Check if the number doesn't already start with 0 or +84
                  // Check if the number doesn't already start with 0 or +84
                  if (!/^(\+84|0)/.test(ev)) {
                    ev = '0' + ev; // Add 0 at the beginning
                  }
                  if (/^(\+84|0)/.test(ev)) {
                    // check trùng sđt trên hệ thống, check 9 ký tự cuối của ev
                    const last9Digits = ev.slice(-9);
                    const res = await customerController.getListSimple({
                      page: 1,
                      size: 1,
                      query: `@Mobile:(*${last9Digits}*)`,
                    });
                    if (res.data.length > 0) {
                      methods.setError('Mobile', {
                        message: t('common.phoneNumberUsed'),
                      });
                      showSnackbar({
                        message: t('common.phoneNumberUsed'),
                        status: ComponentStatus.ERROR,
                      });
                      return;
                    }
                    //
                    const val = validatePhoneNumber(ev);
                    if (!val) {
                      showSnackbar({
                        message: t('common.phoneNumberInvalid'),
                        status: ComponentStatus.ERROR,
                      });
                      methods.setError('Mobile', {
                        message: t('common.phoneNumberInvalid'),
                      });
                      return;
                    }
                  }
                  if (ev?.length !== 0) {
                    if (!/^(\+84|0)/.test(ev)) {
                      methods.setValue('Mobile', '0' + ev); // Add 0 at the beginning
                    }
                    methods.clearErrors('Mobile');
                  } else {
                    showSnackbar({
                      message: t('common.phoneNumberRequired'),
                      status: ComponentStatus.ERROR,
                    });
                    methods.setError('Mobile', {
                      message: t('common.phoneNumberRequired'),
                    });
                  }
                }}
              />
            </View>
          </TouchableWithoutFeedback>
        ),
        onCancel: async () => {
          setIsShowDialog(false);
          methods.setValue('RefCode', '');
          methods.setValue('Mobile', '');
          if (user) {
            var parentId;
            var listParent;
            // START case: nếu k có mã giới thiệu. Hệ thống tự add mã của chainivo
            // lấy thông tin của admin chainivo
            const resAdmin = await customerController.getListSimple({
              page: 1,
              size: 1,
              query: `@Id: {${ConfigAPI.adminCHAINIVO}}`,
              returns: ['Id', 'Name', 'AvatarUrl', 'ListParent', 'RefCode'],
            });
            if (resAdmin.code === 200 && resAdmin.data.length > 0) {
              parentId = resAdmin.data[0].Id;
              listParent = resAdmin.data[0].ListParent
                ? resAdmin.data[0].ListParent + ',' + resAdmin.data[0].Id
                : resAdmin.data[0].Id;
            }
            // END

            const deviceToken = await getDataToAsyncStorage('fcmToken');
            if (parentId) {
              dispatch(
                CustomerActions.edit({
                  ...user,
                  RanksData: undefined,
                  Status: CustomerStatus.active,
                  DeviceToken: deviceToken,
                  ParentId: parentId,
                  ListParent: listParent,
                }),
              );
              dispatch(CustomerActions.getInfor());
            }

            setIsShowDialog(false);
          }
        },
        onSubmit: async () => {
          const code = methods.watch('RefCode')?.trim();
          const mobile = methods.watch('Mobile')?.trim();
          if (mobile === undefined || mobile?.length == 0) {
            methods.setError('Mobile', {
              message: t('common.phoneNumberRequired'),
            });
            return;
          }
          if (code === undefined || code?.length == 0) {
            return;
          }

          if (user) {
            //lấy thông tin theo ref code
            var parentId;
            var listParent;
            var hasParent = false;
            if (methods.watch('RefCode')?.length > 0) {
              const resRef = await customerController.getListSimple({
                page: 1,
                size: 1,
                query: `@RefCode: (*${methods.watch('RefCode')}*)`,
                returns: ['Id', 'Name', 'AvatarUrl', 'ListParent'],
              });
              if (resRef.code === 200 && resRef.data.length > 0) {
                if (resRef.data[0].Id === user.Id) {
                  showSnackbar({
                    message: t('common.refCodeInvalid'),
                    status: ComponentStatus.ERROR,
                  });
                  return;
                }
                hasParent = true;

                parentId = resRef.data[0].Id;
                listParent = resRef.data[0].ListParent
                  ? resRef.data[0].ListParent + ',' + resRef.data[0].Id
                  : resRef.data[0].Id;
              } else {
                showSnackbar({
                  message: t('common.refCodeInvalid'),
                  status: ComponentStatus.ERROR,
                });
                return;
              }
            } else {
              // START case: nếu k có mã giới thiệu. Hệ thống tự add mã của chainivo
              // lấy thông tin của admin chainivo
              const resAdmin = await customerController.getListSimple({
                page: 1,
                size: 1,
                query: `@Id: {${ConfigAPI.adminCHAINIVO}}`,
                returns: ['Id', 'Name', 'AvatarUrl', 'ListParent', 'RefCode'],
              });
              if (resAdmin.code === 200 && resAdmin.data.length > 0) {
                parentId = resAdmin.data[0].Id;
                listParent = resAdmin.data[0].ListParent
                  ? resAdmin.data[0].ListParent + ',' + resAdmin.data[0].Id
                  : resAdmin.data[0].Id;
              }
              // END
            }

            const deviceToken = await getDataToAsyncStorage('fcmToken');

            dispatch(
              CustomerActions.edit({
                ...user,
                RanksData: undefined,
                Status: CustomerStatus.active,
                DeviceToken: deviceToken,
                ParentId: parentId,
                ListParent: listParent,
                Mobile: mobile,
              }),
            );
            // call cộng point cho người giới thiệu
            // nếu refCode == refcodeshare
            const refcodeInput = methods
              .watch('RefCode')
              ?.toLowerCase()
              ?.trim();
            const refcodeShare = await getDataToAsyncStorage('RefCodeShared');
            if (
              refcodeInput === refcodeShare?.toLowerCase()?.trim() ||
              hasParent
            ) {
              // call cộng point cho người giới thiệu
              const customerDA = new CustomerDA();
              await customerDA.pointPlusRefCode({
                ...user,
                ParentId: parentId,
                ListParent: listParent,
              });
            }
            dispatch(CustomerActions.getInfor());
            showSnackbar({
              message: t('common.updateRefCodeSuccess'),
              status: ComponentStatus.SUCCSESS,
            });
            setIsShowDialog(false);
            methods.setValue('RefCode', '');
            methods.setValue('Mobile', '');
          }
        },
      });
    }
  }, [isFirstTime, user]);

  return (
    <View style={styles.container}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          headerShown: false,
        }}
        tabBar={props => (
          <CustomTabBar props={props} ref={dialogCheckAccRef} />
        )}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: (e: any) => {
                  if (
                    !user &&
                    item.name !== 'news' &&
                    item.name !== 'home' &&
                    item.name !== 'profile' &&
                    item.name !== 'category' &&
                    item.name !== 'products'
                  ) {
                    dialogCheckAcc(dialogCheckAccRef);
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={{
                headerShown: false,
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    paddingBottom: 16,
  },
});
