import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {PermissionsAndroid, Platform} from 'react-native';
import {navigate, navigationRef, RootScreen} from '../../../router/router';
import {getApp, initializeApp} from '@react-native-firebase/app';
import {getAuth} from '@react-native-firebase/auth';
import {NotificationActions} from '../../../redux/reducers/notificationReducer';
import store from '../../../redux/store/store';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';

// Define the CustomGlobal type
interface CustomGlobal {
  RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS: boolean;
  RNFB_MODULAR_DEPRECATION_STRICT_MODE: boolean;
}

declare var globalThis: CustomGlobal;

globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;
globalThis.RNFB_MODULAR_DEPRECATION_STRICT_MODE = true;

// Configure PushNotification
PushNotification.configure({
  // (optional) Called when Token is generated (iOS and Android)
  onRegister: function (token) {
    console.log('TOKEN:', token);
  },

  // (required) Called when a remote is received or opened, or local notification is opened
  onNotification: function (notification) {
    console.log('NOTIFICATION:', notification);

    // Handle notification tap
    if (notification.userInteraction) {
      // User tapped on notification
      navigate(RootScreen.NotifCommunity);
    }

    // (required) Called when a remote is received or opened, or local notification is opened
    if (Platform.OS === 'ios') {
      notification.finish(PushNotificationIOS.FetchResult.NoData);
    }
  },

  // (optional) Called when Registered Action is pressed and invokeApp is false, if true onNotification will be called (Android)
  onAction: function (notification) {
    console.log('ACTION:', notification.action);
    console.log('NOTIFICATION:', notification);
  },

  // (optional) Called when the user fails to register for remote notifications. Typically occurs when APNS is having issues, or the device is a simulator. (iOS)
  onRegistrationError: function (err) {
    console.error(err.message, err);
  },

  // IOS ONLY (optional): default: all - Permissions to register.
  permissions: {
    alert: true,
    badge: true,
    sound: true,
  },

  // Should the initial notification be popped automatically
  // default: true
  popInitialNotification: true,

  /**
   * (optional) default: true
   * - Specified if permissions (ios) and token (android and ios) will requested or not,
   * - if not, you must call PushNotification.requestPermissions() later
   * - if you are not using remote notification or do not have Firebase installed, use this:
   *     requestPermissions: Platform.OS === 'ios'
   */
  requestPermissions: Platform.OS === 'ios',
});

// Create notification channel for Android
if (Platform.OS === 'android') {
  PushNotification.createChannel(
    {
      channelId: 'default-channel-id',
      channelName: 'Default Channel',
      channelDescription: 'A default channel for notifications',
      playSound: true,
      soundName: 'default',
      importance: 4, // High importance
      vibrate: true,
    },
    created => console.log(`createChannel returned '${created}'`),
  );
}

// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: 'AIzaSyC2fTM2N7fKelqgp9NUN87meUV7XUH_JV4',
  authDomain: 'chainivo-2f990.firebaseapp.com',
  projectId: 'chainivo-2f990',
  storageBucket: 'chainivo-2f990.firebasestorage.app',
  messagingSenderId: '801228927819',
  appId: '1:801228927819:web:5a99dae333c7fc429faece',
  measurementId: 'G-T2WG4KRMEG',
};

// Initialize Firebase
let initFirebase;
try {
  initFirebase = getApp(); // This will get the default app if it's already initializeduccncx
  getAuth(initFirebase);
} catch (error) {
  initFirebase = initializeApp(firebaseConfig); // Initialize app with the provided config
}

export const initNotificationPermission = async () => {
  await registerAppWithFCM();

  const authStatus = await messaging().requestPermission();

  // ANDROID
  if (Platform.OS === 'android') {
    try {
      await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      ).then(response => {
        if (response) {
          return getFcmToken();
        }
      });
      // requestNotificationPermission();
    } catch (err) {
      console.warn('requestNotificationPermission error: ', err);
    }
  } else {
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    // Request permissions (required for iOS) using PushNotification
    try {
      const permissions = await PushNotification.requestPermissions();
      console.log('iOS notification permissions:', permissions);

      if (permissions.alert || permissions.badge || permissions.sound) {
        console.log('User granted some permissions');
      } else {
        console.log('User denied all permissions');
      }
    } catch (error) {
      console.log('Error requesting iOS permissions:', error);
    }

    if (enabled) {
      return getFcmToken();
    }
  }
};

//method was called to listener events from firebase for notification triger
// main hàm xử lý thông tin và logic
export function registerListenerWithFCM() {
  const unsubscribe = messaging().onMessage(async remoteMessage => {
    console.log(
      '📱 [FCM] onMessage Received:',
      JSON.stringify(remoteMessage, null, 2),
    );
    if (
      remoteMessage?.notification?.title &&
      remoteMessage?.notification?.body
    ) {
      // Increment badge count
      await NotificationActions.incrementBadge(store.dispatch);

      // show notification
      onDisplayNotification(
        remoteMessage.notification?.title,
        remoteMessage.notification?.body,
        remoteMessage?.data,
      );
    }
  });

  // Note: Notification event handling is now managed by PushNotification.configure() above
  // The onNotification callback in the configuration handles taps and interactions

  // Handle notification when app is opened from background
  messaging().onNotificationOpenedApp(async remoteMessage => {
    console.log(
      '📱 [FCM] onNotificationOpenedApp:',
      JSON.stringify(remoteMessage, null, 2),
    );
    await handleFirebaseNotificationOpened(remoteMessage, 'background');
  });

  // Check whether an initial notification is available (app opened from quit state)
  messaging()
    .getInitialNotification()
    .then(async remoteMessage => {
      if (remoteMessage) {
        console.log(
          '📱 [FCM] getInitialNotification (quit state):',
          JSON.stringify(remoteMessage, null, 2),
        );
        await handleFirebaseNotificationOpened(remoteMessage, 'quit');
      }
    });

  return unsubscribe;
}

// Handle notification dismissed
const handleNotificationDismissed = async (detail: any) => {
  console.log('❌ [FCM] Notification dismissed:', detail.notification?.id);
  await NotificationActions.decrementBadge(store.dispatch);
};

// Handle notification pressed
const handleNotificationPressed = async (
  detail: any,
  context: 'foreground' | 'background',
) => {
  // Use the centralized notification click handler
  await NotificationActions.handleNotificationClick(
    store.dispatch,
    detail.notification,
    context,
  );

  // Navigate to notification screen
  navigate(RootScreen.NotifCommunity);
};

// Handle notification action button pressed
const handleNotificationActionPressed = async (
  detail: any,
  context: 'foreground' | 'background',
) => {
  console.log(`🎯 [FCM] Notification action pressed in ${context}:`, {
    notificationId: detail.notification?.id,
    actionId: detail.pressAction?.id,
    data: detail.notification?.data,
  });

  await NotificationActions.decrementBadge(store.dispatch);

  // Handle different action types
  switch (detail.pressAction?.id) {
    case 'mark_read':
      console.log('📖 [FCM] Mark as read action');
      // Handle mark as read
      break;
    case 'reply':
      console.log('💬 [FCM] Reply action');
      // Handle reply action
      break;
    default:
      console.log('🎯 [FCM] Default action - navigate to app');
      store.dispatch(CustomerActions.getInfor());
      navigate(RootScreen.NotifCommunity);
  }
};

// Handle Firebase notification opened (from onNotificationOpenedApp or getInitialNotification)
const handleFirebaseNotificationOpened = async (
  remoteMessage: any,
  context: 'background' | 'quit',
) => {
  // Use the centralized notification click handler
  await NotificationActions.handleNotificationClick(
    store.dispatch,
    {
      id: remoteMessage.messageId,
      data: remoteMessage.data,
      notification: remoteMessage.notification,
    },
    context,
  );

  // Navigate to notification screen
  navigate(RootScreen.NotifCommunity);
};

//method was called to get FCM tiken for notification
export const getFcmToken = async () => {
  let token = '';
  try {
    token = await messaging().getToken();
    console.log('getFcmToken-->', Platform.OS, token);
    await AsyncStorage.setItem('fcmToken', token);
  } catch (error) {
    console.log('getFcmToken Device Token error ', error);
  }
  return token;
};

//method was called on  user register with firebase FCM for notification
export async function registerAppWithFCM() {
  if (!messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .registerDeviceForRemoteMessages()
      .then(status => {
        console.log('registerDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('registerDeviceForRemoteMessages error ', error);
      });
  }
  messaging().subscribeToTopic('all-devices');
}

//method was called on un register the user from firebase for stoping receiving notifications
export async function unRegisterAppWithFCM() {
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );

  if (messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .unregisterDeviceForRemoteMessages()
      .then(status => {
        console.log('unregisterDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('unregisterDeviceForRemoteMessages error ', error);
      });
  }
  await messaging().deleteToken();
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );
}

export const decrementBadgeCount = async () => {
  await NotificationActions.decrementBadge(store.dispatch);
};

// Badge management functions
export const incrementBadgeCount = async () => {
  await NotificationActions.incrementBadge(store.dispatch);
};

export const setBadgeCount = async (count: number) => {
  try {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.setApplicationIconBadgeNumber(count);
    } else {
      // Android badge count is handled by the notification library
      PushNotification.setApplicationIconBadgeNumber(count);
    }
    await NotificationActions.setBadge(store.dispatch);
    console.log(`🔢 [FCM] Badge count set to: ${count}`);
  } catch (error) {
    console.error('Error setting badge count:', error);
  }
};

export const clearBadgeCount = async () => {
  await NotificationActions.clearBadge(store.dispatch);
};

export const getBadgeCount = async (): Promise<number> => {
  try {
    if (Platform.OS === 'ios') {
      return new Promise(resolve => {
        PushNotificationIOS.getApplicationIconBadgeNumber(count => {
          resolve(count);
        });
      });
    } else {
      // Android doesn't have a direct way to get badge count
      // Return 0 as default or implement custom storage
      return 0;
    }
  } catch (error) {
    console.error('Error getting badge count:', error);
    return 0;
  }
};

// Listen for badge count changes
export const listenToBadgeChanges = () => {
  // This function can be called to set up badge count monitoring
  console.log('🔢 [FCM] Badge change listener initialized');

  // You can add additional badge monitoring logic here
  // For example, periodic badge count sync
  setInterval(async () => {
    try {
      await NotificationActions.setBadge(store.dispatch);
    } catch (error) {
      console.error('Error syncing badge count:', error);
    }
  }, 30000); // Sync every 30 seconds
};

//method was called to display notification
async function onDisplayNotification(
  title: string,
  body: string,
  data:
    | {
        [key: string]: string | number | object;
      }
    | any
    | undefined,
) {
  // Display a local notification using react-native-push-notification
  PushNotification.localNotification({
    title: title,
    message: body,
    userInfo: data, // Custom data
    playSound: true,
    soundName: 'default',
    actions: ['ReplyInput'],
    category: 'SOME_CATEGORY',
    channelId: 'default-channel-id', // Required for Android
    priority: 'high',
    importance: 'high',
    allowWhileIdle: true,
    ignoreInForeground: false,
  });
}
