import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import FastImage from '@d11/react-native-fast-image';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {RankInfo} from '../../../../redux/types/rankTypes';
import {navigate, RootScreen} from '../../../../router/router';
import {useSelector} from 'react-redux';
import {RootState} from 'redux/store/store';
import {Ultis} from 'utils/Utils';

interface StatusCardProps {
  currentRankInfo: RankInfo | null;
}

export const StatusCard: React.FC<StatusCardProps> = ({currentRankInfo}) => {
  const [isVip, setIsVip] = useState<boolean>(false);
  const [showBenefits, setShowBenefits] = useState<boolean>(false);
  const {point} = useSelector((state: RootState) => state.customer);

  useEffect(() => {
    if (!currentRankInfo || !currentRankInfo?.Sort) setIsVip(false);
    else setIsVip(currentRankInfo?.Sort >= 3);
  }, [currentRankInfo]);

  // Render cho trường hợp chưa có hạng
  const renderNoRank = () => (
    <TouchableOpacity
      onPress={() => navigate(RootScreen.ProfileRankScreen)}
      style={styles.statusCard}>
      <Text style={styles.rankTitle}>Chưa có hạng</Text>
      <Text style={styles.description}>
        Nâng hạng ngay để hưởng quyền lợi độc quyền từ Chainivo
      </Text>
    </TouchableOpacity>
  );

  // Render cho trường hợp đã có hạng
  const renderWithRank = () => {
    // Lấy benefits từ Description và split theo dấu phẩy
    const benefits = (currentRankInfo as any)?.Description
      ? (currentRankInfo as any).Description.split(',')
          .map((benefit: string) => benefit.trim())
          .filter((benefit: string) => benefit.length > 0)
      : [];

    return (
      <View
        style={[
          styles.statusCardWithRank,
          {
            borderColor: isVip
              ? '#FFC043'
              : ColorThemes.light.primary_border_color,
          },
        ]}>
        <TouchableOpacity
          onPress={() => navigate(RootScreen.ProfileRankScreen)}
          style={styles.rankInfoContainer}>
          <View style={styles.rankInfo}>
            <FastImage
              source={{uri: ConfigAPI.urlImg + currentRankInfo?.Icon}}
              style={styles.rankIcon}
              resizeMode="contain"
            />
            <View style={styles.rankTextContainer}>
              <Text style={styles.rankName}>{currentRankInfo?.Name}</Text>
              <Text
                style={[
                  styles.pointText,
                  {
                    color: isVip
                      ? '#FFC043'
                      : ColorThemes.light.primary_main_color,
                  },
                ]}>
                {Ultis.money(point)} Point
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        <View style={styles.toggleButtonContainer}>
          <TouchableOpacity
            onPress={() => setShowBenefits(!showBenefits)}
            style={styles.toggleButton}>
            <Text
              style={[
                styles.toggleButtonText,
                {
                  color: isVip
                    ? '#FFC043'
                    : ColorThemes.light.primary_main_color,
                },
              ]}>
              {showBenefits ? 'Ẩn bớt' : 'Xem quyền lợi'}
            </Text>
          </TouchableOpacity>
        </View>

        {showBenefits && benefits.length > 0 && (
          <View style={styles.benefitsContainer}>
            <View style={styles.divider} />
            <View style={styles.benefitsList}>
              {benefits.map((benefit: string, index: number) => (
                <View key={index} style={styles.benefitRow}>
                  <View style={styles.checkIcon}>
                    <Text style={styles.checkMark}>✓</Text>
                  </View>
                  <Text style={styles.benefitText}>{benefit}</Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </View>
    );
  };

  return currentRankInfo ? renderWithRank() : renderNoRank();
};

const styles = StyleSheet.create({
  statusCard: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderRadius: 20,
    padding: 16,
    gap: 12,
    borderColor: ColorThemes.light.primary_border_color,
    borderWidth: 1,
    marginTop: 16,
  },
  statusCardWithRank: {
    borderRadius: 20,
    padding: 12,
    gap: 12,
    borderColor: ColorThemes.light.primary_border_color,
    borderWidth: 1,
    marginTop: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rankTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  rankInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  rankIcon: {
    width: 32,
    height: 32,
  },
  rankTextContainer: {
    flexDirection: 'column',
    gap: 2,
  },
  rankName: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: 'bold',
  },
  pointText: {
    ...TypoSkin.body3,
    fontWeight: 'bold',
  },
  description: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_subtitle_color,
    lineHeight: 20,
  },
  benefitsContainer: {
    gap: 6,
  },
  benefitsList: {
    gap: 8,
    marginLeft: 8,
  },

  benefitRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.success_main_color,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkMark: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 10,
    fontWeight: 'bold',
  },
  benefitText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_title_color,
    flex: 1,
    fontSize: 12,
  },
  rankInfoContainer: {
    width: '100%',
  },
  toggleButtonContainer: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  toggleButton: {
    paddingHorizontal: 8,
  },
  toggleButtonText: {
    ...TypoSkin.body3,
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: ColorThemes.light.neutral_bolder_border_color,
    marginHorizontal: 12,
  },
  // Legacy styles - keeping for compatibility
  statusTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
  },
});
