import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {View, Text, TouchableOpacity, ScrollView} from 'react-native';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
import {RootScreen} from '../../router/router';
import {Title} from '../../Config/Contanst';
import LeaderShopInfo from './component/LeaderShopInfo';
import {useDispatch, useSelector} from 'react-redux';
import {OrderActions} from '../../redux/reducers/OrderReducer';
import {useSelectorShopState} from '../../redux/hook/shopHook ';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import MenuHaveShop from '../../components/Field/menu/MenuHaveShop/MenuHaveShop';
import MenuCards from '../../components/Field/menu/MenuHaveShop/MenuCards';
import iconSvg from '../../svg/icon';
import {fetchCategories} from '../../redux/actions/categoryAction';
import {RootState} from '../../redux/store/store';
import {shopStyles} from './styles';
import ShopDA from './da';
import {HaveShopProps} from './types';
const HaveShop = (props: HaveShopProps) => {
  const navigation = useNavigation<any>();
  const [getNewOrder, SetGetNewOrder] = useState<number>(0);
  const [getProcessingOrder, SetGetProcessingOrder] = useState<number>(0);
  const [getCancelOrder, SetGetCancelOrder] = useState<number>(0);
  const dispatch = useDispatch<any>();
  const shopInfo = useSelectorShopState().data;
  const {data: OrderData} = useSelector((state: RootState) => state.order);
  let [shopMoney, setShopMoney] = useState<number>(0);
  let shopDA = new ShopDA();
  const customer = useSelectorCustomerState().data;
  const getShopMoney = async () => {
    try {
      if (shopInfo && shopInfo.length > 0 && shopInfo[0]?.Id) {
        const res = await shopDA.getShopFinancialSummary(shopInfo[0]?.Id);
        if (res?.code === 200) {
          console.log('check-res', res?.data?.balance);
          setShopMoney(res?.data?.balance || 0);
        } else {
          console.error('Failed to get shop money:', res);
        }
      }
    } catch (error) {
      console.error('Error getting shop money:', error);
    }
  };

  const refreshOrderData = () => {
    SetGetNewOrder(OrderData?.NewOrder?.number || 0);
    SetGetProcessingOrder(OrderData?.ProcessOrder?.number || 0);
    SetGetCancelOrder(OrderData?.CancelOrder?.number || 0);
  };

  const handleWithDrawMoney = () => {
    if (!customer?.IsEnable2FA) {
      showSnackbar({
        message: 'Vui lòng Bật xác thực 2 lớp để thực hiện giao dịch',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    if (!customer?.IsVerify) {
      showSnackbar({
        message: 'Vui lòng Bật xác minh tài khoản để thực hiện giao dịch',
        status: ComponentStatus.ERROR,
      });
      return;
    }
    console.log('Navigating to WithDrawMoney with Type: 2');
    navigation.navigate(RootScreen.WithDrawMoney, {
      Type: 2,
    });
  };
  useEffect(() => {
    if (shopInfo && shopInfo.length > 0) {
      dispatch(OrderActions.getInforOrder(shopInfo[0]?.Id));
      dispatch(fetchCategories());
      getShopMoney();
      refreshOrderData(); // Refresh order data khi khởi tạo
    }
  }, [shopInfo]);
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', async () => {
      if (shopInfo && shopInfo.length > 0) {
        dispatch(OrderActions.getInforOrder(shopInfo[0]?.Id));
        getShopMoney(); // Refresh shop money every time page comes into focus
        refreshOrderData(); // Refresh order data khi focus
      }
    });
    return unsubscribe;
  }, [navigation, shopInfo]);

  useEffect(() => {
    refreshOrderData();
  }, [OrderData]);

  return (
    <ScrollView
      style={{flex: 1}}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}>
      <View style={shopStyles.containerHaveShop}>
        <View style={shopStyles.navBarHaveShop}>
          <MenuHaveShop
            svgIcon={iconSvg.walletAction}
            title="Đơn hàng mới"
            getNewOrder={getNewOrder}
            order={RootScreen.OrderDetail}
            type={Title.New}
            status={1}
            numberOrder={getNewOrder ? getNewOrder : 0}
          />
          <MenuHaveShop
            svgIcon={iconSvg.deliveryIcon}
            title="Đang xử lý"
            getNewOrder={getProcessingOrder}
            order={RootScreen.OrderDetail}
            type={Title.Processing}
            status={1}
            numberOrder={getProcessingOrder ? getProcessingOrder : 0}
          />
          <MenuHaveShop
            svgIcon={iconSvg.done}
            title="Hoàn thành"
            order={RootScreen.OrderDetail}
            type={Title.Done}
            status={1}
          />
          <MenuHaveShop
            svgIcon={iconSvg.cancel}
            title="Hủy/hoàn"
            getNewOrder={getCancelOrder}
            order={RootScreen.OrderDetail}
            type={Title.Cancel}
            status={1}
            // numberOrder={getCancelOrder ? getCancelOrder : 0}
          />
          <MenuHaveShop
            svgIcon={iconSvg.star}
            title="Đánh giá"
            order={RootScreen.Review}
            type={Title.Shop}
          />
        </View>

        <View style={shopStyles.ShopMoney}>
          <View style={shopStyles.ShopMoneyContent}>
            <View style={shopStyles.ShopMoneyTitle}>
              <View style={{padding: 12}}>
                <Text style={shopStyles.ShopMoneyTitleText}>
                  Số dư bán hàng
                </Text>
                <Text style={shopStyles.ShopMoneyTitleTextNumber}>
                  {shopMoney < 0
                    ? 0
                    : Number(shopMoney).toLocaleString('vi-VN', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0,
                      })}{' '}
                  VNĐ
                </Text>
              </View>
              <TouchableOpacity
                style={shopStyles.ShopMoneyTitleButton}
                onPress={() => handleWithDrawMoney()}>
                <Text style={shopStyles.ShopMoneyTitleButtonText}>
                  Rút tiền
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <LeaderShopInfo shop={props.shop} />
          <MenuCards
            svgIcon={iconSvg.manageProduct}
            color="orange"
            title="QL sản phẩm"
            order={RootScreen.ManageProduct}
          />
          <MenuCards
            svgIcon={iconSvg.manageReport}
            color="orange"
            title="Báo cáo"
            order={RootScreen.ChartReport}
          />
          <MenuCards
            svgIcon={iconSvg.promotion}
            color="orange"
            title="Khuyến mại"
            order={RootScreen.ShopPromotion}
          />
          <MenuCards
            svgIcon={iconSvg.promotion}
            color="orange"
            title="Cấu hình Affiliate"
            order={RootScreen.ConfigAffiliate}
          />
        </View>
      </View>
    </ScrollView>
  );
};
export default HaveShop;
