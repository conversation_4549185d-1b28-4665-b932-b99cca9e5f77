import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {Title} from '../../../../Config/Contanst';
import {TypoSkin} from '../../../../assets/skin/typography';

interface OrderInfoSectionProps {
  typeCard: string;
  isLoading: boolean;
  dataSearch: string;
  numberCardSearch: number;
  numberCard: number;
}

const OrderInfoSection: React.FC<OrderInfoSectionProps> = ({
  typeCard,
  isLoading,
  dataSearch,
  numberCardSearch,
  numberCard,
}) => {
  if (typeCard === Title.Done) {
    return null;
  }

  const getDisplayText = () => {
    if (isLoading) {
      return 'Đang tải dữ liệu...';
    }

    if (dataSearch?.length > 0) {
      return `${numberCardSearch} đơn hàng`;
    }

    return `${numberCard} đơn hàng`;
  };

  return (
    <View style={styles.container}>
      <Text style={styles.numberOrder}>{getDisplayText()}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
  },
  numberOrder: {
    ...TypoSkin.title4,
    marginTop: 10,
    color: '#999',
    marginBottom: 8,
  },
});

export default OrderInfoSection;
