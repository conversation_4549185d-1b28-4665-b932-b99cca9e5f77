/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {View, Text, StyleSheet, Dimensions, FlatList} from 'react-native';
import {AppButton, FDialog} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {ProductDA} from './productDA';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {RootScreen} from '../../router/router';
import {AppDispatch} from '../../redux/store/store';
import {useDispatch} from 'react-redux';
import {CartActions} from '../../redux/reducers/CartReducer';
import {updateFavoriteProduct} from '../../redux/actions/productAction';
import {Product} from '../../redux/models/product';
import ProductCard from './card/ProductCard';

const {width} = Dimensions.get('window');

// Kích thước của mỗi item sản phẩm
const ITEM_WIDTH = width * 0.45;
const ITEM_HEIGHT = ITEM_WIDTH * 2; // Tăng chiều cao một chút
const ITEM_SPACING = 12;

// Tạo component ItemSeparator bên ngoài component chính
const ItemSeparator = React.memo(() => <View style={{width: ITEM_SPACING}} />);

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
  onPressSeeMore?: () => void;
  onRefresh?: boolean;
}

export default function ProductBestSeller(props: Props) {
  const [data, setData] = useState<Array<any>>([]);
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const productDA = new ProductDA();
  const dispatch: AppDispatch = useDispatch();

  useEffect(() => {
    getData();
  }, [props.onRefresh]);

  const getData = async () => {
    const data = await productDA.getProductBestSeller();
    setData(data);
  };

  const renderItem = ({item}: {item: Product}) => {
    return (
      <ProductCard
        key={item.Id}
        item={item}
        onPress={() => {
          navigation.push(RootScreen.ProductDetail, {id: item.Id});
        }}
        onAddToCart={() => {
          dispatch(CartActions.addItemToCart(item, 1));
        }}
        onFavoritePress={() => {}}
        width={ITEM_WIDTH}
        height={ITEM_HEIGHT}
      />
    );
  };

  return (
    <View style={{marginTop: 20}}>
      <FDialog ref={dialogRef} />
      {props.titleList ? (
        <View style={styles.titleContainer}>
          <Text style={styles.titleText}>{props.titleList}</Text>
          {props.isSeeMore ? (
            <AppButton
              title={'Xem thêm'}
              containerStyle={styles.seeMoreButtonContainer}
              backgroundColor={'transparent'}
              textStyle={styles.seeMoreButtonText}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={props.onPressSeeMore}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      {data ? (
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={(item: {Id: {toString: () => any}}) =>
            item.Id?.toString()
          }
          horizontal
          showsHorizontalScrollIndicator={false}
          snapToInterval={ITEM_WIDTH + ITEM_SPACING}
          snapToAlignment="start"
          decelerationRate="fast"
          pagingEnabled={false}
          disableIntervalMomentum={true}
          ItemSeparatorComponent={ItemSeparator}
        />
      ) : (
        <></>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  titleText: {
    ...TypoSkin.heading5,
    color: ColorThemes.light.neutral_text_title_color,
  },
  seeMoreButtonContainer: {
    justifyContent: 'flex-start',
    alignSelf: 'baseline',
  },
  seeMoreButtonText: {
    ...TypoSkin.buttonText3,
    color: ColorThemes.light.infor_main_color,
  },
});
