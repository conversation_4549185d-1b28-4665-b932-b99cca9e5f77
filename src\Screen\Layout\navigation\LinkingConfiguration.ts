/**
 * React Navigation Linking Configuration
 * Updated to work with the new DeepLinkManager service
 */

import {LinkingOptions} from '@react-navigation/native';
import {RootStackParamList} from './ecomNavigator';
import {RootScreen} from '../../../router/router';

const linking: LinkingOptions<RootStackParamList> = {
  prefixes: ['chainivo://', 'https://chainivo.com', 'https://www.chainivo.com'],
  config: {
    screens: {
      // Home screen (default)
      [RootScreen.navigateEComView]: '',

      // Content screens
      [RootScreen.PostDetail]: 'posts/:id',
      [RootScreen.DetailNews]: 'news/:id',
      [RootScreen.DetailEvent]: 'events/:id',
      [RootScreen.ProductDetail]: 'product/:id',

      // Share/QR code screen
      [RootScreen.login]: 'share/:id',
    },
  },

  // Disable React Navigation's built-in linking to avoid conflicts
  getInitialURL: async () => {
    return null; // Let DeepLinkManager handle this
  },

  subscribe: () => {
    return () => {}; // Let DeepLinkManager handle this
  },
};

export default linking;
