import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ViewStyle,
  StyleProp,
} from 'react-native';
import {Winicon, FDialog} from 'wini-mobile-components';
import {Title} from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {Ultis} from '../../../utils/Utils';
import {CardOrderStyles} from '../styles/CardOrderStyles';
import PopupUpdateStatusOrder from 'modules/Product/Popup/PopupUpdateStatusOrder1';

const CardOrder = ({
  item,
  rate,
  style,
  action,
  handleUpdateStatusProcessOrder,
  handleViewDetailOrder,
}: {
  item: any;
  rate: number;
  style?: StyleProp<ViewStyle>;
  action?: string;
  handleUpdateStatusProcessOrder: (item: any, type?: string) => void;
  handleViewDetailOrder: (item: any, refundInfo: any) => void;
}) => {
  const [showStatusPopup, setShowStatusPopup] = useState(false);
  const [showAllProducts, setShowAllProducts] = useState(false);
  const [refundInfo, setRefundInfo] = useState({
    all: 0,
    allRefund: 0,
    detail: [],
  });

  const dialogRef = useRef<any>(null);

  const handleStatusPress = () => {
    setShowStatusPopup(true);
  };

  const handleUpdate = async (itemWithCancelReason: any, status?: string) => {
    handleUpdateStatusProcessOrder(itemWithCancelReason, status);
    setShowStatusPopup(false);
  };

  const handleToggleProducts = () => {
    setShowAllProducts(!showAllProducts);
  };

  // Lấy danh sách sản phẩm để hiển thị
  const getDisplayProducts = () => {
    if (!item?.orderDetails || item.orderDetails.length === 0) return [];
    return showAllProducts ? item.orderDetails : [item.orderDetails[0]];
  };

  const getTotalRefund = () => {
    return !item?.Refund
      ? 0
      : item.orderDetails?.reduce(
          (total: number, orderDetail: any) =>
            total +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f0 ?? 0) +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f1 ?? 0) +
            (JSON.parse(orderDetail?.Refund ?? '{}')?.f2 ?? 0),
          0,
        );
  };

  useEffect(() => {
    let totalRefund = 0;
    let refundCount = 0;
    const refundedProducts = new Set();

    if (item?.orderDetails) {
      item.orderDetails.forEach((orderDetail: any) => {
        if (orderDetail.historyReward) {
          const productHasRefund = orderDetail.historyReward.some(
            (history: any) => history.Value > 0,
          );

          if (productHasRefund) {
            refundedProducts.add(orderDetail.Id);
            orderDetail.historyReward.forEach((history: any) => {
              totalRefund += history.Value || 0;
            });
          }
        }
      });
    }

    refundCount = refundedProducts.size;

    const firstOrderDetail = item?.orderDetails;
    let arrayDetail: any[] = [];
    firstOrderDetail?.forEach((orderDetail: any) => {
      arrayDetail.push({
        id: orderDetail.Id,
        refundCustomer: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 0,
        ),
        refundF1: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 1,
        ),
        refundF2: orderDetail.historyReward?.find(
          (history: any) => history.Filial === 2,
        ),
      });
    });

    setRefundInfo({
      all: refundCount,
      allRefund: totalRefund,
      detail: arrayDetail as any,
    });
  }, [item]);

  const renderAffiliate = (productItem: any) => {
    const rewardFilial0 = productItem.historyReward?.find(
      (history: any) => history.Filial === 0,
    );
    const rewardFilial1 = productItem.historyReward?.find(
      (history: any) => history.Filial === 1,
    );
    const rewardFilial2 = productItem.historyReward?.find(
      (history: any) => history.Filial === 2,
    );
    let text = '';
    if (rewardFilial0 && rewardFilial0.Value > 0) {
      text += `(kh: ${Ultis.money(rewardFilial0.Value)} point)`;
    }
    if (rewardFilial1 && rewardFilial1.Value > 0) {
      text += `(f1: ${Ultis.money(rewardFilial1.Value)} point)`;
    }
    if (rewardFilial2 && rewardFilial2.Value > 0) {
      text += `(f2: ${Ultis.money(rewardFilial2.Value)} point)`;
    }
    return <Text>{text}</Text>;
  };

  return (
    <TouchableOpacity
      style={style}
      onPress={() => handleViewDetailOrder(item, refundInfo)}>
      <FDialog ref={dialogRef} />
      <View style={CardOrderStyles.container}>
        <View style={CardOrderStyles.header}>
          <Text
            style={{...CardOrderStyles.orderId, gap: 2, flexDirection: 'row'}}>
            {'Đơn hàng '}
            <Text style={{color: '#000', fontWeight: 'bold'}}>
              #{item?.Code}
            </Text>
          </Text>
          <Text
            style={
              item?.Status == 3
                ? CardOrderStyles.statusDone
                : item?.Status == 2 || item?.Status == 1
                ? CardOrderStyles.statusProcessing
                : CardOrderStyles.status
            }>
            {item?.Status == 1 && 'Chờ xác nhận'}
            {item?.Status == 2 && 'Đang thực hiện'}
            {item?.Status == 3 && 'Hoàn thành'}
            {item?.Status == 4 && 'Hủy'}
          </Text>
        </View>
        {/* Thông tin sản phẩm */}
        {getDisplayProducts().map((productItem: any, index: number) => (
          <View
            style={CardOrderStyles.productContainer}
            key={`${index}-${productItem?.Id}`}>
            <Image
              source={{uri: ConfigAPI.urlImg + productItem?.productInfo?.Img}}
              style={CardOrderStyles.productImage}
            />
            <View style={CardOrderStyles.productInfo}>
              <Text style={CardOrderStyles.productName}>
                {productItem?.productInfo?.Name}
              </Text>
              <Text style={CardOrderStyles.productDetails}>
                {
                  <>
                    <Text style={CardOrderStyles.productName}>Hoàn tiền: </Text>
                    <Text style={CardOrderStyles.productDetails}>
                      {renderAffiliate(productItem)}
                    </Text>{' '}
                  </>
                }
              </Text>
              <Text style={CardOrderStyles.productPrice}>
                <Text style={CardOrderStyles.productName}>
                  Số lượng: {productItem?.Quantity}
                </Text>
              </Text>
              <Text style={CardOrderStyles.productPrice}>
                <Text style={CardOrderStyles.productName}>Giá: </Text>
                <Text style={{color: ColorThemes.light.error_main_color}}>
                  {Ultis.money(
                    (productItem?.Price ?? 0) *
                      (1 - (productItem?.Discount ?? 0) / 100),
                  )}{' '}
                  VNĐ
                </Text>
              </Text>
            </View>
          </View>
        ))}

        {/* Số lượng và tổng tiền */}
        <View style={CardOrderStyles.quantityTotal}>
          {item?.orderDetails?.length > 1 && (
            <TouchableOpacity
              style={CardOrderStyles.quantityButton}
              onPress={handleToggleProducts}>
              <Text style={CardOrderStyles.quantityText}>
                <Text
                  style={{
                    ...TypoSkin.title3,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {showAllProducts ? 'Thu gọn' : 'Xem thêm'}
                </Text>
                <Winicon
                  src={
                    showAllProducts
                      ? 'color/arrows/arrow-sm-up'
                      : 'color/arrows/arrow-sm-down'
                  }
                  size={13}
                  color={ColorThemes.light.neutral_text_title_color}
                />
              </Text>
            </TouchableOpacity>
          )}
          <View style={CardOrderStyles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                minWidth: 200,
              }}>
              <Text style={CardOrderStyles.quantity}>
                Tổng hoàn ({refundInfo.all} sản phẩm):
              </Text>
              <View style={{width: 30}}></View>
              <Text style={CardOrderStyles.money}>
                {Ultis.money(refundInfo.allRefund)} VNĐ
              </Text>
            </View>
          </View>
          <View style={CardOrderStyles.quantityDetail}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                minWidth: 200,
              }}>
              <Text style={CardOrderStyles.quantity}>
                Tổng tiền ({item?.orderDetails?.length ?? 0} sản phẩm):
              </Text>
              <View style={{width: 30}}></View>
              <Text style={CardOrderStyles.money}>
                {Ultis.money(item?.Value)} VNĐ
              </Text>
            </View>
          </View>
        </View>
        {action && action == 'Xác nhận đơn' && (
          <View style={CardOrderStyles.button}>
            <View style={{flexDirection: 'row', gap: 10}}></View>
            <View style={{flexDirection: 'row', gap: 10}}>
              <TouchableOpacity
                style={CardOrderStyles.confirmButton}
                onPress={handleStatusPress}>
                <Text style={CardOrderStyles.confirmButtonText}>
                  Xác nhận đơn hàng
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
        {action &&
          action !== Title.Cancel &&
          action !== 'Xác nhận đơn' &&
          item?.Status !== 3 && (
            <View style={CardOrderStyles.button}>
              <View style={{flexDirection: 'row', gap: 10}}></View>
              <View style={{flexDirection: 'row', gap: 10}}>
                <TouchableOpacity
                  style={CardOrderStyles.confirmButton}
                  onPress={handleStatusPress}>
                  <Text style={CardOrderStyles.confirmButtonText}>
                    Cập nhật trạng thái
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
      </View>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}>
        <PopupUpdateStatusOrder
          visible={showStatusPopup}
          onClose={() => setShowStatusPopup(false)}
          item={item}
          handleUpdateStatusProcessOrder={handleUpdate}
        />
      </KeyboardAvoidingView>
    </TouchableOpacity>
  );
};

export default CardOrder;
