import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {LineChart} from 'react-native-chart-kit';
import {ScrollView} from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import {ChartType} from '../../Config/Contanst';
import ChartMoney from './ChartMakeMoney';
import ChartProduct from './ChartProduct';
import ChartOrder from './ChartOrder';
import {ColorThemes} from '../../assets/skin/colors';
import {useTranslation} from '../../locales/useTranslation';

const {width} = Dimensions.get('window');

const Chart = () => {
  const {t} = useTranslation();
  const [SelectChart, setSelectChart] = useState(ChartType.MakeMoney);

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.tabContainer}
        horizontal={true}
        showsHorizontalScrollIndicator={false}>
        {SelectChart == ChartType.MakeMoney ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={styles.tabAction}
            start={{x: 0, y: 0}}
            end={{x: 1, y: 0}}>
            <TouchableOpacity
              onPress={() => setSelectChart(ChartType.MakeMoney)}>
              <Text style={styles.tabTextAction}>
                {t('chart.revenueReport')}
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        ) : (
          <TouchableOpacity
            style={styles.tab}
            onPress={() => setSelectChart(ChartType.MakeMoney)}>
            <Text style={styles.tabText}>{t('chart.revenueReport')}</Text>
          </TouchableOpacity>
        )}

        {SelectChart == ChartType.ProductData ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={styles.tabAction}>
            <TouchableOpacity
              onPress={() => setSelectChart(ChartType.ProductData)}>
              <Text style={styles.tabTextAction}>
                {t('chart.productReport')}
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        ) : (
          <TouchableOpacity
            style={styles.tab}
            onPress={() => setSelectChart(ChartType.ProductData)}>
            <Text style={styles.tabText}>{t('chart.productReport')}</Text>
          </TouchableOpacity>
        )}

        {SelectChart == ChartType.OrderData ? (
          <LinearGradient
            colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
            style={styles.tabAction}>
            <TouchableOpacity
              onPress={() => setSelectChart(ChartType.OrderData)}>
              <Text style={styles.tabTextAction}>{t('chart.orderReport')}</Text>
            </TouchableOpacity>
          </LinearGradient>
        ) : (
          <TouchableOpacity
            style={styles.tab}
            onPress={() => setSelectChart(ChartType.OrderData)}>
            <Text style={styles.tabText}>{t('chart.orderReport')}</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
      {SelectChart === ChartType.MakeMoney && <ChartMoney />}
      {SelectChart === ChartType.ProductData && <ChartProduct />}
      {SelectChart === ChartType.OrderData && <ChartOrder />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 10,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  tab: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    marginLeft: 5,
    marginRight: 5,
    fontFamily: 'Roboto',
    minHeight: 42,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabAction: {
    borderBottomColor: 'transparent',
    marginLeft: 5,
    marginRight: 5,
    borderRadius: 10,
    fontFamily: 'Roboto',
    color: ColorThemes.light.primary_main_color,
    minHeight: 42,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabTextAction: {
    fontSize: 15,
    color: 'blue',
    paddingHorizontal: 8,
    paddingVertical: 4,
    textAlign: 'center',
    fontWeight: '500',
  },
  tabText: {
    fontSize: 15,
    color: '#696969',
    paddingHorizontal: 8,
    paddingVertical: 4,
    textAlign: 'center',
    fontWeight: '400',
  },
});

export default Chart;
