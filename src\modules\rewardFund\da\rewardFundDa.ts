import {Customer} from 'redux/types/customerType';
import {DataController} from '../../../base/baseController';
import {RewardFund} from '../../../redux/types/rewardFundTypes';
import {CustomerDA} from 'modules/customer/da';
import ConfigRewardRankDa from 'modules/configRewardRank/da/configRewardRankDa';
import WalletDA from 'modules/wallet/walletDa';
import {TransactionType, TransactionStatus} from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';

class RewardFundDa {
  private controller: DataController;
  private customerDA: CustomerDA;
  private configRewardRankDA: ConfigRewardRankDa;
  private walletDA: WalletDA;

  constructor() {
    this.controller = new DataController('RewardFund');
    this.customerDA = new CustomerDA();
    this.configRewardRankDA = new ConfigRewardRankDa();
    this.walletDA = new WalletDA();
  }

  /**
   * Lấy RewardFund theo tháng và năm
   * @param month - Tháng (1-12)
   * @param year - Năm (ví dụ: 2025)
   * @returns RewardFund hoặc null nếu không tìm thấy
   */
  async getRewardFundByMonthYear(
    month: number,
    year: number,
  ): Promise<RewardFund | null> {
    try {
      const res = await this.controller.getListSimple({
        query: `@Sort: [${month}] @Year: [${year}]`,
        page: 1,
        size: 1,
        returns: [
          'Id',
          'Name',
          'Sort',
          'Status',
          'Value',
          'Year',
          'DateCreated',
        ],
      });
      if (res?.code === 200 && res.data && res.data.length > 0) {
        return res.data[0] as RewardFund;
      }
      return null;
    } catch (error) {
      console.error('Error getting reward fund by month/year:', error);
      return null;
    }
  }

  /**
   * Lấy tất cả RewardFund
   * @returns Danh sách RewardFund
   */
  async getAllRewardFunds(): Promise<RewardFund[]> {
    try {
      const res = await this.controller.getAll();

      if (res?.code === 200) {
        return res.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error getting all reward funds:', error);
      return [];
    }
  }

  async shareRewardFund({
    month,
    year,
    userId,
  }: {
    month: number;
    year: number;
    userId: string;
  }) {
    const rewardFund = await this.getRewardFundByMonthYear(month, year);
    if (!rewardFund) throw new Error('Reward fund not found');

    const customers = await this.customerDA.getAllCustomerWithRank();

    const configRewardRank =
      await this.configRewardRankDA.fetchConfigRewardRank({
        type: 2,
      });
    if (!configRewardRank.length)
      throw new Error('Config reward rank not found');

    // 1. Loại bỏ các user không có rank hoặc rank.Sort < 3
    const eligibleCustomers = customers.filter(
      (customer: Customer) =>
        customer.ConfigRank &&
        customer.ConfigRank.Sort &&
        customer.ConfigRank.Sort >= 3,
    );

    if (!eligibleCustomers.length) {
      throw new Error('No eligible customers found for reward distribution');
    }

    // 2. Tập hợp các người có cùng ConfigRank lại với nhau
    const customersByRank = new Map<string, Customer[]>();
    eligibleCustomers.forEach((customer: Customer) => {
      const rankId = customer.ConfigRankId;
      if (!customersByRank.has(rankId)) {
        customersByRank.set(rankId, []);
      }
      customersByRank.get(rankId)!.push(customer);
    });

    // 3. Mỗi nhóm rank sẽ được chia 1 phần point theo % từ Value của ConfigRewardRank
    const totalRewardFundValue = rewardFund.Value;
    const bills: any[] = [];

    for (const [rankId, customersInRank] of customersByRank) {
      // Tìm config reward rank tương ứng
      const rankConfig = configRewardRank.find(
        config => config.ConfigRankId === rankId,
      );

      if (!rankConfig) continue;

      // Tính tổng point cho nhóm rank này
      const rankTotalPoints = (totalRewardFundValue * rankConfig.Value) / 100;

      // Chia đều cho các thành viên trong nhóm
      const pointPerCustomer = rankTotalPoints / customersInRank.length;

      // lấy ngày cuối tháng theo tháng và năm và có dạng timestamp
      const dateCreated = new Date(year, month, 0, 23).getTime();

      // 4. Tạo giao dịch bằng hàm renderTransferPoint
      customersInRank.forEach((customer: Customer) => {
        bills.push(
          ...this.walletDA.renderTransferPoint({
            senderId: ConfigAPI.adminCHAINIVO,
            senderName: 'CHAINIVO ADMIN',
            senderMobile: '',
            recipientId: customer.Id,
            recipientName: customer.Name,
            recipientMobile: customer.Mobile,
            amount: pointPerCustomer,
            type: TransactionType.WithDrawMoney,
            status: TransactionStatus.success,
            dateCreated: dateCreated,
            descriptionSender: `Chia quỹ thưởng tháng ${month}/${year}`,
            descriptionRecipient: `Nhận quỹ thưởng tháng ${month}/${year}`,
          }),
        );
      });
    }

    // Gửi tất cả giao dịch
    if (bills.length > 0) {
      const result = await this.walletDA.transferManyBills(bills);

      if (!result) {
        throw new Error('Failed to transfer reward fund');
      }

      await this.controller.edit([
        {
          Id: rewardFund.Id,
          Status: 2,
        },
      ]);
      return {
        success: true,
        message: `Successfully distributed reward fund to ${eligibleCustomers.length} customers`,
        totalAmount: totalRewardFundValue,
        customersCount: eligibleCustomers.length,
      };
    }

    return {
      success: false,
      message: 'No transactions to process',
    };
  }
}

export default RewardFundDa;
