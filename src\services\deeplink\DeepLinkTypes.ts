/**
 * Deep Link Types and Interfaces
 * Centralized type definitions for the deeplink system
 */

import {RootScreen} from '../../router/router';

// Supported URL schemes
export type DeepLinkScheme = 'chainivo' | 'https';

// Supported deeplink types
export type DeepLinkType = 'posts' | 'news' | 'product' | 'share' | 'events' | 'home';

// Raw URL string
export type DeepLinkUrl = string;

// Parsed deeplink structure
export interface ParsedDeepLink {
  type: DeepLinkType;
  id?: string;
  screen: RootScreen;
  params: Record<string, any>;
  queryParams: Record<string, string>;
}

// Navigation parameters for each screen type
export interface DeepLinkNavigationParams {
  [RootScreen.PostDetail]: {id: string};
  [RootScreen.DetailNews]: {id: string};
  [RootScreen.ProductDetail]: {id: string};
  [RootScreen.DetailEvent]: {id: string};
  [RootScreen.login]: {id: string};
  [RootScreen.navigateEComView]: undefined;
}

// Deeplink validation result
export interface DeepLinkValidationResult {
  isValid: boolean;
  error?: string;
  scheme?: DeepLinkScheme;
  host?: string;
}

// Deeplink parsing result
export interface DeepLinkParsingResult {
  success: boolean;
  data?: ParsedDeepLink;
  error?: string;
}

// Navigation result
export interface DeepLinkNavigationResult {
  success: boolean;
  screen?: RootScreen;
  error?: string;
}

// DeepLink Manager configuration
export interface DeepLinkManagerConfig {
  enableLogging: boolean;
  fallbackScreen: RootScreen;
  retryAttempts: number;
  retryDelay: number;
}

// Event types for deeplink handling
export type DeepLinkEvent = 'url_received' | 'navigation_success' | 'navigation_error' | 'parsing_error';

// Event listener callback
export type DeepLinkEventListener = (event: DeepLinkEvent, data?: any) => void;

// Supported URL patterns
export const DEEPLINK_PATTERNS = {
  POSTS: /^(chainivo:\/\/|https:\/\/(www\.)?chainivo\.com\/)posts\/([^/?]+)/,
  NEWS: /^(chainivo:\/\/|https:\/\/(www\.)?chainivo\.com\/)news\/([^/?]+)/,
  PRODUCT: /^(chainivo:\/\/|https:\/\/(www\.)?chainivo\.com\/)product\/([^/?]+)/,
  SHARE: /^(chainivo:\/\/|https:\/\/(www\.)?chainivo\.com\/)share\/([^/?]+)/,
  EVENTS: /^(chainivo:\/\/|https:\/\/(www\.)?chainivo\.com\/)events\/([^/?]+)/,
  HOME: /^(chainivo:\/\/|https:\/\/(www\.)?chainivo\.com\/?)$/,
} as const;

// Valid schemes and hosts
export const VALID_SCHEMES = ['chainivo', 'https'] as const;
export const VALID_HOSTS = ['chainivo.com', 'www.chainivo.com'] as const;

// Default configuration
export const DEFAULT_DEEPLINK_CONFIG: DeepLinkManagerConfig = {
  enableLogging: __DEV__,
  fallbackScreen: RootScreen.navigateEComView,
  retryAttempts: 2,
  retryDelay: 500,
};
