import React from 'react';
import {useState, useEffect} from 'react';
import Clipboard from '@react-native-clipboard/clipboard';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';
import {DataController} from '../../../../base/baseController';
import {Ultis} from '../../../../utils/Utils';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../../redux/store/store';
import {showSnackbar, ComponentStatus} from 'wini-mobile-components';
import {useFocusEffect} from '@react-navigation/native';
import {TransactionStatus} from '../../../../Config/Contanst';

export interface ITransactionItem {
  id: number | string;
  type: 'income' | 'expense';
  amount: number;
  description?: string;
  time: string;
}

export const useMyWalletProfile = () => {
  const customer = useSelectorCustomerState().data;
  const [transactionHistory, setTransactionHistory] = useState<
    ITransactionItem[]
  >([]);
  const [walletBalance, setWalletBalance] = useState(0);
  const [tokenBalance, setTokenBalance] = useState(0);
  const [incomeAmount, setIncomeAmount] = useState(0);
  const [expenseAmount, setExpenseAmount] = useState(0);
  const [walletId, setWalletId] = useState(customer?.WalletAddress ?? '');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const dispatch: AppDispatch = useDispatch();

  const copyToClipboard = () => {
    if (!walletId) return;
    Clipboard.setString(walletId);
    showSnackbar({
      status: ComponentStatus.SUCCSESS,
      message: 'Đã sao chép ID wallet',
    });
  };

  const initWallet = async () => {
    try {
      const customerController = new DataController('Customer');
      const resultWallet = await CustomerActions.createWallet(
        customer?.Name ?? '',
      );
      if (resultWallet && resultWallet?.address && resultWallet?.privateKey) {
        setWalletId(resultWallet.address);
        await customerController.edit([
          {
            Id: customer?.Id,
            WalletAddress: resultWallet.address,
            PrivateKey: resultWallet.privateKey,
          },
        ]);
        dispatch(CustomerActions.getInfor());
      }
    } catch (walletError) {
      console.error('Wallet creation failed:', walletError);
    }
  };

  const buildQuery = () => {
    return `@CustomerId: {${customer?.Id}} ((@Status: [${TransactionStatus.success}] @Value: [0 +inf]) | (@Status: [${TransactionStatus.pending} ${TransactionStatus.success}] @Value: [-inf 0]))`;
  };

  const loadWalletData = async () => {
    if (!customer?.Id) return;

    const wa = customer?.WalletAddress?.trim?.() ?? '';
    if (!wa || wa === 'null' || wa === 'undefined' || wa === '0x') {
      await initWallet();
    }

    const controller = new DataController('HistoryReward');
    const res = await controller.getListSimple({
      query: buildQuery(),
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (res?.code === 200) {
      const data: ITransactionItem[] = (res.data || []).map((item: any) => {
        const date = Ultis.numberToTime(item.DateCreated, true);
        const valueNum = Number(item.Value) || 0;
        return {
          id: item.Id,
          type: valueNum > 0 ? 'income' : 'expense',
          amount: valueNum,
          description: item.Description,
          time: date,
        };
      });
      setTransactionHistory(data.length > 5 ? data.slice(0, 5) : data);

      const values: number[] = (res.data || []).map(
        (i: any) => Number(i.Value) || 0,
      );
      const total = values.reduce((sum, v) => sum + v, 0);
      const income = values.filter(v => v > 0).reduce((s, v) => s + v, 0);
      const expense = values.filter(v => v < 0).reduce((s, v) => s + v, 0);

      setWalletBalance(total);
      setIncomeAmount(income);
      setExpenseAmount(expense);
    }
  };

  const fetchToken = async () => {
    try {
      if (!customer?.WalletAddress) return;
      const balance = await CustomerActions.getBalance(customer.WalletAddress);
      setTokenBalance(Number(balance) || 0);
    } catch (e) {
      console.warn('fetchToken error', e);
    }
  };

  useEffect(() => {
    setLoading(true);
    loadWalletData().finally(() => setLoading(false));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchToken();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [walletId, refreshing]);

  useFocusEffect(
    React.useCallback(() => {
      (async () => {
        await loadWalletData();
      })();
      return () => {};
    }, []),
  );

  const handleRefresh = async () => {
    if (!customer?.Id) return;
    setRefreshing(true);
    try {
      await loadWalletData();
    } finally {
      setRefreshing(false);
    }
  };

  return {
    customer,
    walletId,
    transactionHistory,
    walletBalance,
    tokenBalance,
    incomeAmount,
    expenseAmount,
    loading,
    refreshing,
    handleRefresh,
    copyToClipboard,
  } as const;
};
