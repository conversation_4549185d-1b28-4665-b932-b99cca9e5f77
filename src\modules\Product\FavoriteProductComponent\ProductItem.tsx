import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {FavoriteProduct} from '../../../redux/models/favoriteProduct';
import {faHeart, faCartShopping} from '@fortawesome/free-solid-svg-icons';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {ColorThemes} from '../../../assets/skin/colors';
import {AppSvg} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import {unFavoriteProduct} from '../../../redux/actions/favoriteProductAction';
import {useDispatch} from 'react-redux';
import {CartActions} from '../../../redux/reducers/CartReducer';
import {Product} from '../../../redux/models/product';
import {navigate, RootScreen} from '../../../router/router';

const defaultImage = require('../../../assets/images/default_img.png');

interface ProductItemProps {
  item: Product;
  showFavorite?: boolean;
  onFavoritePress?: (id: string) => void;
}

const ProductItem = ({
  item,
  showFavorite = false,
  onFavoritePress,
}: ProductItemProps) => {
  const dispatch = useDispatch();

  const addToCart = () => {
    CartActions.addItemToCart(item, 1)(dispatch);
  };

  const isFavorite = item.IsFavorite ?? false;

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={() => {
        navigate(RootScreen.ProductDetail, {id: item.Id});
      }}
      style={styles.productItemContainer}>
      <View style={styles.imageContainer}>
        <Image
          source={item.Img ? {uri: item.Img} : defaultImage}
          resizeMode="cover"
          style={styles.productImage}
        />
      </View>

      <View style={styles.infoActionContainer}>
        <View style={styles.infoContainer}>
          <Text style={styles.productName} numberOfLines={2}>
            {item.Name}
          </Text>
          <Text style={styles.productPrice}>
            {item.Price ? item.Price.toLocaleString() : 0}đ
          </Text>
        </View>

        <View style={styles.actionsContainer}>
          {showFavorite && (
            <TouchableOpacity
              style={[styles.actionButton, isFavorite && styles.favoriteButton]}
              onPress={() => {
                onFavoritePress?.(item.Id);
              }}>
              <FontAwesomeIcon
                icon={faHeart}
                size={16}
                color={
                  isFavorite
                    ? ColorThemes.light.error_main_color
                    : ColorThemes.light.transparent
                }
              />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[styles.actionButton, styles.cartButton]}
            onPress={() => addToCart()}>
            <AppSvg SvgSrc={iconSvg.carShopping} size={18} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  productItemContainer: {
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    marginVertical: 8,
  },
  imageContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  productImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  infoActionContainer: {
    flex: 1,
    height: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
  },
  infoContainer: {
    width: '60%',
    height: '100%',
    justifyContent: 'space-between',
  },
  productName: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold',
  },
  productPrice: {
    fontSize: 16,
    color: 'red',
  },
  actionsContainer: {
    width: 90,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  favoriteButton: {
    backgroundColor: '#FFD700',
  },
  cartButton: {
    backgroundColor: '#E0E0E0',
  },
});

export default ProductItem;
