import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import QRCode from 'react-native-qrcode-svg';
import {ColorThemes} from '../../../../assets/skin/colors';

export interface IActionItem {
  id: number;
  name: string;
}

interface Props {
  walletId: string;
  actions: IActionItem[];
  onCopy: () => void;
  onActionPress: (id: number) => void;
}

export const QRSection: React.FC<Props> = ({
  walletId,
  actions,
  onCopy,
  onActionPress,
}) => {
  return (
    <View style={styles.qrSection}>
      <View style={styles.qrContainer}>
        <ImageBackground
          source={require('../../../../assets/bg-qr.png')}
          style={styles.qrBackground}
          imageStyle={styles.qrBackgroundImage}>
          <View style={styles.qrCodeWrapper}>
            {walletId && walletId.trim() !== '' ? (
              <QRCode
                value={walletId}
                size={110}
                backgroundColor="white"
                color="black"
              />
            ) : (
              <View style={styles.qrPlaceholder}>
                <Text style={styles.qrPlaceholderText}>No QR Data</Text>
              </View>
            )}
          </View>
        </ImageBackground>
      </View>

      <View style={styles.walletInfo}>
        <Text style={styles.walletIdLabel}>ID wallet</Text>
        <Text onPress={onCopy} style={styles.walletIdText} numberOfLines={2}>
          {walletId}
        </Text>

        <View style={styles.actionButtons}>
          {actions.map(item => (
            <TouchableOpacity
              style={styles.actionButton}
              key={item.id}
              onPress={() => onActionPress(item.id)}>
              <LinearGradient
                start={{x: 0, y: 0}}
                end={{x: 1, y: 0}}
                colors={['#90C8FB', '#8DC4F7', '#B6F5FE']}
                style={styles.actionButtonGradient}>
                <Text style={styles.actionButtonText}>{item.name}</Text>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  qrSection: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    alignItems: 'center',
    width: '100%',
  },
  qrContainer: {marginRight: 15, width: '50%'},
  qrBackground: {
    width: '100%',
    height: 200,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  qrBackgroundImage: {borderRadius: 60, resizeMode: 'cover'},
  qrCodeWrapper: {
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    padding: 15,
    borderRadius: 15,
  },
  qrPlaceholder: {
    width: 110,
    height: 110,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrPlaceholderText: {color: '#666', fontSize: 12},
  walletInfo: {flex: 1},
  walletIdLabel: {
    fontSize: 12,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginBottom: 4,
  },
  walletIdText: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
    marginBottom: 12,
  },
  actionButtons: {gap: 8},
  actionButton: {width: '100%', flex: 1, alignContent: 'center'},
  actionButtonGradient: {
    borderRadius: 20,
    height: 36,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 12,
    color: ColorThemes.light.neutral_main_reverse_background_color,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default QRSection;
