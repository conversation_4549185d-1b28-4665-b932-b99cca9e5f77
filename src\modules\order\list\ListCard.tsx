import React, {useEffect, useState} from 'react';
import {View, RefreshControl} from 'react-native';
import {FlatList} from 'react-native';
import {ComponentStatus, FLoading, showSnackbar} from 'wini-mobile-components';
import {Title} from '../../../Config/Contanst';
import CardOrder from '../card/CardOrder';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {OrderActions} from '../../../redux/reducers/OrderReducer';
import {useSelectorOrderState} from '../../../redux/hook/orderHook ';
import EmptyPage from '../../../Screen/emptyPage';
import {ColorThemes} from '../../../assets/skin/colors';
import {RootScreen} from '../../../router/router';
import {useSelectorShopState} from '../../../redux/hook/shopHook ';
import {handleUpdateStatusOrder} from '../ultis';
import BasePopupConfirm from '../../../components/Popup/BasePopupConfirm';
import {OrderDA} from '../orderDA';
import {useSelectorCustomerState} from 'redux/hook/customerHook';
import ConfigPointToCurrencyDa from '../../configPointToCurrency/da/configPointToCurrencyDa';
import ConfigAPI from '../../../Config/ConfigAPI';
import {Ultis} from '../../../utils/Utils';
import WalletDA from '../../wallet/walletDa';

interface OrderCardProps {
  type: string;
  setTypeCard: (type: string) => void;
  dataSearchResult: any[];
  dataSearch: string;
}
const ListCard = (props: OrderCardProps) => {
  const orderDA = new OrderDA();
  const configPointToCurrencyDa = new ConfigPointToCurrencyDa();
  const walletDA = new WalletDA();
  const customer = useSelectorCustomerState().data;
  const {type, dataSearchResult, dataSearch} = props;
  const navigation = useNavigation<any>();
  const [data, setData] = useState<any[]>();
  const [action, setAction] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [rate, setRate] = useState<number>(1);
  const orderInfo = useSelectorOrderState().data;
  const dispatch = useDispatch<any>();
  const shopInfo = useSelectorShopState().data;
  //refresh
  const [refreshing, setRefreshing] = useState(false);
  // Popup confirm state - Đơn giản hóa bằng cách gộp thành 1 object
  const [confirmState, setConfirmState] = useState<{
    isVisible: boolean;
    item: any | null;
    type: string;
  }>({
    isVisible: false,
    item: null,
    type: '',
  });
  // State to hold the async calculated message
  const [confirmMessage, setConfirmMessage] = useState<string>('');
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (shopInfo && shopInfo[0]?.Id) {
        await dispatch(OrderActions.getInforOrder(shopInfo[0].Id));
      } else {
        showSnackbar({
          message: 'Không thể làm mới dữ liệu. Vui lòng thử lại sau.',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Có lỗi xảy ra khi làm mới dữ liệu',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Fetch ConfigPointToCurrency rate when component mounts
  useEffect(() => {
    const fetchConfigRate = async () => {
      try {
        const config = await configPointToCurrencyDa.getConfigByArea(
          ConfigAPI.area,
        );
        if (config && config.Rate) {
          setRate(config.Rate);
        }
      } catch (error) {
        console.error('Error fetching ConfigPointToCurrency:', error);
        // Keep default rate of 1 if error occurs
      }
    };

    fetchConfigRate();
  }, []);

  useEffect(() => {
    if (type === Title.New) {
      setAction('Xác nhận đơn');
      setData(orderInfo?.NewOrder?.data);
    } else if (type === Title.Processing) {
      setAction('Cập nhật trạng thái');
      setData(orderInfo?.ProcessOrder?.data);
    } else if (type === Title.Cancel) {
      setAction(type);
      setData(orderInfo?.CancelOrder?.data);
    } else if (type === Title.Done) {
      setData(orderInfo?.DoneOrder?.data);
    }
  }, [type, action, orderInfo]);

  const handleUpdateStatus = async (item: any, type?: string) => {
    // Nếu Status !== 1, xử lý trực tiếp không cần confirm
    if (item.Status !== 1) {
      return handleConfirmUpdate(item, type || '');
    }

    // Nếu Status === 1, cần hiển thị popup confirm
    // First calculate the message asynchronously
    const message = await calculateConfirmationMessage(item);
    setConfirmMessage(message);

    setConfirmState({
      isVisible: true,
      item: item,
      type: type || '',
    });
  };

  const handleConfirmUpdate = async (item?: any, type?: string) => {
    // Ưu tiên sử dụng tham số truyền vào, nếu không có thì lấy từ confirmState
    const itemToUpdate = item || confirmState.item;
    const typeToUpdate = type || confirmState.type;
debugger
    try {
      if (!customer?.Id) return;

      setLoading(true);
      // Đóng popup confirm
      setConfirmState(prev => ({...prev, isVisible: false}));

      if (itemToUpdate) {
        // Xử lý transfer tax nếu Status === 1
        if (itemToUpdate.Status === 1) {
          await orderDA.transferTaxOrder({
            customer,
            totalAmount: itemToUpdate.Value,
            orderId: itemToUpdate.Id,
          });
        }

        // Cập nhật status order
        await handleUpdateStatusOrder(
          itemToUpdate,
          typeToUpdate,
          dispatch,
          navigation,
          shopInfo,
        );
      }

      // Reset confirm state
      setConfirmState({
        isVisible: false,
        item: null,
        type: '',
      });
      setConfirmMessage('');
    } catch (error: any) {
      const msg = error.message || 'Có lỗi xảy ra khi cập nhật trạng thái';
      showSnackbar({
        message: msg,
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelUpdate = () => {
    // Reset toàn bộ confirm state
    setConfirmState({
      isVisible: false,
      item: null,
      type: '',
    });
    setConfirmMessage('');
  };

  /**
   * Calculate total affiliate rewards and 5% order fee for confirmation message
   * This version fetches orderDetails from getOrderDetailByOrderId and displays current available points
   */
  const calculateConfirmationMessage = async (
    orderItem: any,
  ): Promise<string> => {
    if (!orderItem) return 'Bạn có chắc chắn muốn xác nhận đơn hàng này?';

    try {
      // Fetch order details using orderDA.getOrderDetailByOrderId
      const orderDetails = await orderDA.getOrderDetailByOrderId(orderItem.Id);

      // Fetch current customer points
      const customerPoints = await walletDA.getPointCustomer({
        customerId: customer?.Id || '',
      });

      // Calculate total affiliate rewards from all order details
      let totalAffiliateRewards = 0;
      let totalF0Rewards = 0;
      let totalF1Rewards = 0;
      let totalF2Rewards = 0;

      // Check if we got order details
      if (orderDetails && orderDetails.length > 0) {
        // Get historyRewards for each order detail
        const orderDetailIds = orderDetails.map((detail: any) => detail.Id);
        const historyRewardResponse =
          await orderDA.getMoneyDetailsByListOrderDetailId(orderDetailIds);

        if (
          historyRewardResponse?.code === 200 &&
          historyRewardResponse?.data
        ) {
          // Map historyReward data to orderDetails
          const orderDetailsWithRewards = orderDetails.map((detail: any) => {
            const historyRewards = historyRewardResponse.data.filter(
              (reward: any) => reward.OrderDetailId === detail.Id,
            );
            return {...detail, historyReward: historyRewards};
          });

          // Calculate rewards from all order details with historyReward
          orderDetailsWithRewards.forEach((productItem: any) => {
            // Get rewards for each filial level
            const rewardFilial0 = productItem.historyReward?.find(
              (history: any) => history.Filial === 0,
            );
            const rewardFilial1 = productItem.historyReward?.find(
              (history: any) => history.Filial === 1,
            );
            const rewardFilial2 = productItem.historyReward?.find(
              (history: any) => history.Filial === 2,
            );

            // Sum up rewards by filial level
            if (rewardFilial0 && rewardFilial0.Value > 0) {
              totalF0Rewards += rewardFilial0.Value;
              totalAffiliateRewards += rewardFilial0.Value;
            }
            if (rewardFilial1 && rewardFilial1.Value > 0) {
              totalF1Rewards += rewardFilial1.Value;
              totalAffiliateRewards += rewardFilial1.Value;
            }
            if (rewardFilial2 && rewardFilial2.Value > 0) {
              totalF2Rewards += rewardFilial2.Value;
              totalAffiliateRewards += rewardFilial2.Value;
            }
          });
        }
      }

      // Calculate 5% of total order value
      const orderValue = orderItem.Value || 0;
      const feePercentage = 0.05; // 5%
      const orderFee = orderValue * feePercentage;

      // Convert to points using rate
      const totalAffiliatePoints = Math.round(totalAffiliateRewards / rate);
      const f0Points = Math.round(totalF0Rewards / rate);
      const f1Points = Math.round(totalF1Rewards / rate);
      const f2Points = Math.round(totalF2Rewards / rate);
      const orderFeePoints = Math.round(orderFee / rate);
      const totalPoints = totalAffiliatePoints + orderFeePoints;

      // Get current available points
      const availablePoints = customerPoints?.total || 0;

      // Create detailed message with F0, F1, F2 breakdown
      let affiliateBreakdown = '';
      if (f0Points > 0) {
        affiliateBreakdown += `  - F0: ${Ultis.money(f0Points)} point\n`;
      }
      if (f1Points > 0) {
        affiliateBreakdown += `  - F1: ${Ultis.money(f1Points)} point\n`;
      }
      if (f2Points > 0) {
        affiliateBreakdown += `  - F2: ${Ultis.money(f2Points)} point\n`;
      }

      // Create message with current points information
      return (
        `Bạn sẽ phải chịu:\n` +
        `• Hoa hồng affiliate: ${Ultis.money(totalAffiliatePoints)} point\n` +
        (affiliateBreakdown ? affiliateBreakdown : '') +
        `• Phí đơn hàng (5%): ${Ultis.money(orderFeePoints)} point\n` +
        `\nTổng cộng: ${Ultis.money(totalPoints)} point\n` +
        `Số point hiện có: ${Ultis.money(availablePoints)} point\n\n` +
        `${
          availablePoints < totalPoints
            ? '⚠️ Bạn không đủ point để thực hiện giao dịch này!'
            : 'Hãy chắc chắn rằng bạn muốn xác nhận đơn hàng này.'
        }`
      );
    } catch (error) {
      console.error('Error calculating confirmation message:', error);
      return 'Bạn có chắc chắn muốn xác nhận đơn hàng này?';
    }
  };
  const handleViewDetailOrder = (item: any, refundInfo: any) => {
    navigation.push(RootScreen.OrderDetailPageForShop, {
      orderId: item.Id,
      type: 'Shop',
      CancelReason: item.CancelReason,
      refundInfo: refundInfo,
    });
  };

  if (
    (data && data?.length > 0) ||
    (dataSearchResult && dataSearchResult.length > 0)
  ) {
    return (
      <>
        <FLoading visible={loading} />
        <FlatList
          data={dataSearch ? dataSearchResult : data}
          style={{flex: 1}}
          keyExtractor={item => item.Id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[ColorThemes.light.primary_main_color]}
              tintColor={ColorThemes.light.primary_main_color}
            />
          }
          renderItem={({item}) => (
            <CardOrder
              item={item}
              rate={rate}
              style={{marginBottom: 10}}
              action={action}
              handleUpdateStatusProcessOrder={handleUpdateStatus}
              handleViewDetailOrder={handleViewDetailOrder}
            />
          )}
        />
        <BasePopupConfirm
          visible={confirmState.isVisible}
          loading={loading}
          title="Xác nhận đơn hàng"
          message={confirmMessage}
          onCancel={handleCancelUpdate}
          onConfirm={() => handleConfirmUpdate()}
          cancelText="Hủy"
          confirmText="Xác nhận"
        />
      </>
    );
  } else {
    return (
      <>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
          }}>
          <EmptyPage />
        </View>
        <BasePopupConfirm
          visible={confirmState.isVisible}
          loading={loading}
          title="Xác nhận đơn hàng"
          message={confirmMessage}
          onCancel={handleCancelUpdate}
          onConfirm={() => handleConfirmUpdate()}
          cancelText="Hủy"
          confirmText="Xác nhận"
        />
      </>
    );
  }
};

export default ListCard;
