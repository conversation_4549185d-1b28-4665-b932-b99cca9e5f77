import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {TypoSkin} from 'assets/skin/typography';

interface OrderStatusHeaderProps {
  orderCode?: string;
  onClose: () => void;
}

const OrderStatusHeader: React.FC<OrderStatusHeaderProps> = ({
  orderCode,
  onClose,
}) => {
  return (
    <View style={styles.modalHeader}>
      <Text style={styles.modalTitle}>
        Cập nhật đơn hàng #<Text style={{fontWeight: 'bold'}}>{orderCode}</Text>
      </Text>
      <TouchableOpacity
        style={styles.closeButton}
        onPress={onClose}
        accessibilityLabel="Đóng popup"
        accessibilityRole="button">
        <Text style={styles.closeButtonText}>×</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 8,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    ...TypoSkin.title2,
    fontWeight: '700',
    color: '#1a1a1a',
    flex: 1,
    fontSize: 18,
    lineHeight: 24,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  closeButtonText: {
    fontSize: 22,
    color: '#6c757d',
    fontWeight: '400',
    lineHeight: 22,
    textAlign: 'center',
  },
});

export default OrderStatusHeader;
