import React from 'react';
import {View, Text, TouchableOpacity, FlatList, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {MonthItem} from '../hooks/useMonthFilter';

interface MonthPickerBottomSheetProps {
  months: MonthItem[];
  selectedMonth: number | null;
  onSelectMonth: (month: number) => void;
}

const MonthPickerBottomSheet: React.FC<MonthPickerBottomSheetProps> = ({
  months,
  selectedMonth,
  onSelectMonth,
}) => {
  const renderMonthItem = ({item}: {item: MonthItem}) => (
    <TouchableOpacity
      style={[
        styles.monthItem,
        selectedMonth === item.value && styles.selectedMonthItem,
      ]}
      onPress={() => onSelectMonth(item.value)}>
      <Text
        style={[
          styles.monthItemText,
          selectedMonth === item.value && styles.selectedMonthItemText,
        ]}>
        {item.name}
      </Text>
      {selectedMonth === item.value && (
        <View style={styles.checkIconContainer}>
          <Winicon
            src="outline/user interface/check"
            size={20}
            color={ColorThemes.light.primary_main_color}
          />
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.monthPickerContainer}>
      <FlatList
        data={months}
        renderItem={renderMonthItem}
        keyExtractor={item => item.id.toString()}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  monthPickerContainer: {
    paddingHorizontal: 16,
  },
  monthItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_main_border_color,
  },
  selectedMonthItem: {
    backgroundColor: ColorThemes.light.primary_background,
  },
  monthItemText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  selectedMonthItemText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.primary_main_color,
    fontWeight: '600',
  },
  checkIconContainer: {
    marginLeft: 10,
  },
  flatListContent: {
    paddingTop: 8,
    paddingBottom: 24,
  },
});

export default MonthPickerBottomSheet;
