import React, {useEffect} from 'react';
import {View, StyleSheet} from 'react-native';
import {FLoading, FBottomSheet} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {useTransferCANPoint} from './hooks/useTransferCANPoint';
import TransferHeader from './components/TransferHeader';
import TransferStepIndicator from './components/TransferStepIndicator';
import TransferPagerView from './components/TransferPagerView';

const TransferCANPointScreen1: React.FC = () => {
  const {
    // State
    currentStep,
    transferAmount,
    recipientName,
    recipientPhone,
    recipientId,
    otpValue,
    isVerifying,
    transactionData,
    currentPoints,
    loading,

    type,

    // Refs
    pagerRef,
    customerBottomSheetRef,

    // Handlers
    handleBack,
    handleSelectRecipient,
    handleStep1Next,
    handleOTPComplete,
    handleResendOTP,
    handleVerifyOTP,
    handleDone,
    setTransferAmount,
    setRecipientName,
    setRecipientPhone,
    setRecipientId,
  } = useTransferCANPoint();

  return (
    <View style={styles.container}>
      <TransferHeader type={type} onBack={handleBack} />

      {loading && (
        <FLoading
          visible={loading}
          avt={require('../../../../assets/appstore.png')}
        />
      )}

      <View style={styles.container}>
        <FBottomSheet ref={customerBottomSheetRef} />

        <TransferStepIndicator currentStep={currentStep} totalSteps={3} />

        <TransferPagerView
          pagerRef={pagerRef}
          currentPoints={currentPoints}
          transferAmount={transferAmount}
          recipientName={recipientName}
          recipientPhone={recipientPhone}
          recipientId={recipientId}
          otpValue={otpValue}
          isVerifying={isVerifying}
          transactionData={transactionData}
          type={type}
          onAmountChange={setTransferAmount}
          onSelectRecipient={handleSelectRecipient}
          onDeleteRecipient={() => {
            setRecipientName('');
            setRecipientPhone('');
            setRecipientId('');
          }}
          onNext={handleStep1Next}
          onOTPComplete={handleOTPComplete}
          onResendOTP={handleResendOTP}
          onVerify={handleVerifyOTP}
          onDone={handleDone}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
});

export default TransferCANPointScreen1;
