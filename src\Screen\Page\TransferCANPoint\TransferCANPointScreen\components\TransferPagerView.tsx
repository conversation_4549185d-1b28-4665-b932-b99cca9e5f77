import React, {useState} from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import PagerView from 'react-native-pager-view';
import Step1TransferInfo from '../../Step1TransferInfo';
import Step2OTPVerification from '../../Step2OTPVerification';
import Step3TransactionDetail from '../../Step3TransactionDetail';
import {FLoading} from 'wini-mobile-components';

const {width} = Dimensions.get('window');

interface TransferPagerViewProps {
  pagerRef: React.RefObject<PagerView>;
  currentPoints: number;
  transferAmount: string;
  recipientName: string;
  recipientPhone: string;
  recipientId: string;
  otpValue: string;
  isVerifying: boolean;
  transactionData: any;
  type: string;
  onAmountChange: (amount: string) => void;
  onSelectRecipient: () => void;
  onDeleteRecipient: () => void;
  onNext: (totalTranfer?: number) => void;
  onOTPComplete: (otp: string) => void;
  onResendOTP: () => void;
  onVerify: (otp: string) => Promise<void>;
  onDone: () => void;
}

const TransferPagerView: React.FC<TransferPagerViewProps> = ({
  pagerRef,
  currentPoints,
  transferAmount,
  recipientName,
  recipientPhone,
  otpValue,
  isVerifying,
  transactionData,
  type,
  onAmountChange,
  onSelectRecipient,
  onDeleteRecipient,
  onNext,
  onOTPComplete,
  onResendOTP,
  onVerify,
  onDone,
}) => {
  const [currentStep, setCurrentStep] = useState(1);

  return (
    <View style={{flex: 1}}>
      <FLoading
        visible={isVerifying}
        avt={require('../../../../../assets/appstore.png')}
      />
      <PagerView
        ref={pagerRef}
        style={styles.pagerView}
        onPageSelected={e => setCurrentStep(e.nativeEvent.position + 1)}
        initialPage={0}
        scrollEnabled={false}>
        <View key="step1" style={styles.pageContainer}>
          <Step1TransferInfo
            currentPoints={currentPoints}
            transferAmount={transferAmount}
            recipientName={recipientName}
            recipientPhone={recipientPhone}
            onAmountChange={onAmountChange}
            onSelectRecipient={onSelectRecipient}
            onDeleteRecipient={onDeleteRecipient}
            onNext={onNext}
            type={type}
          />
        </View>

        <View key="step2" style={styles.pageContainer}>
          {
            // get page with pagerRef
            currentStep === 2 && parseInt(transferAmount ?? '0') > 0 && (
              <Step2OTPVerification
                phoneNumber={recipientPhone}
                isVerifying={isVerifying}
                onOTPComplete={onOTPComplete}
                onResendOTP={onResendOTP}
                onVerify={onVerify}
                otpValue={otpValue}
              />
            )
          }
        </View>

        <View key="step3" style={styles.pageContainer}>
          {transactionData && currentStep === 3 && (
            <Step3TransactionDetail
              transactionData={transactionData}
              onDone={onDone}
            />
          )}
        </View>
      </PagerView>
    </View>
  );
};

const styles = StyleSheet.create({
  pagerView: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    width: width,
  },
});

export default TransferPagerView;
