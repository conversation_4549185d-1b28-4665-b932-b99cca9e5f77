import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import {Ultis} from '../../../../utils/Utils';

interface Props {
  income: number;
  expense: number;
}

export const StatsSection: React.FC<Props> = ({income, expense}) => {
  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <Winicon
          src="outline/arrows/arrow-bottom-left"
          size={20}
          color={'#8EDFEB'}
        />
        <Text style={[styles.label, {color: '#8EDFEB'}]}>Income</Text>
        <Text style={[styles.amount, {color: '#8EDFEB'}]}>
          {Ultis.money(income)}
        </Text>
      </View>

      <View style={styles.card}>
        <Winicon src="fill/arrows/arrow-top-right" size={20} color="#5166BF" />
        <Text style={[styles.label, {color: '#5166BF'}]}>Expense</Text>
        <Text style={[styles.amount, {color: '#5166BF'}]}>
          {Ultis.money(expense)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 10,
    width: '100%',
  },
  card: {
    flex: 1,
    paddingVertical: 24,
    borderRadius: 25,
    alignItems: 'center',
    width: '40%',
    shadowColor: '#1890FF4D',
    backgroundColor: ColorThemes.light.white,
    shadowOffset: {width: 1, height: 3},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
  },
  label: {
    fontSize: 20,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 4,
    marginBottom: 4,
    textAlign: 'center',
    fontWeight: '500',
  },
  amount: {fontSize: 18, fontWeight: '700'},
});

export default StatsSection;
