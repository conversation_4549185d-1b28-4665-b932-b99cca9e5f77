import React, {forwardRef, useEffect, useRef, useState} from 'react';

import {
  StyleSheet,
  Text,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  View,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {
  Camera,
  useCameraDevice,
  useCodeScanner,
} from 'react-native-vision-camera';
import {
  closePopup,
  showSnackbar,
  ComponentStatus,
  FLoading,
  Winicon,
  AppButton,
  showDialog,
  FDialog,
} from 'wini-mobile-components';
import ScreenHeader from '../../Screen/Layout/header';
import ImagePicker from 'react-native-image-crop-picker';
import RNQRGenerator from 'rn-qr-generator';
import {TypoSkin} from '../../assets/skin/typography';
import ConfigAPI from 'Config/ConfigAPI';

export const PopupQrcodeScan = forwardRef(function PopupQrcodeScan(
  data: {onDone?: any},
  ref: any,
) {
  const {onDone} = data;
  const camera = useRef<Camera>(null);

  const [isTorchOn, setIsTorchOn] = useState<any>('off');
  const [isLoading, setLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [refresh, setRefresh] = useState(false);
  const device = useCameraDevice('back');
  const dialogRef = useRef<any>(null);

  // Function to validate QR code
  const validateQRCode = (data: string) => {
    // Check if data is exactly 10 characters as per original logic
    return data.includes('chainivo://share/');
  };

  // Function to handle QR code result
  const handleQRCodeResult = (data: string) => {
    if (validateQRCode(data)) {
      closePopup(ref);
      setLoading(false);
      // tôi cần lấy refcode trong url
      const splitUrl = data.split('chainivo://share/')[1];
      const refcode = splitUrl.split('?')[0];
      if (onDone && refcode) onDone(refcode);
    } else {
      showSnackbar({
        message: 'Mã QR không hợp lệ. Vui lòng thử lại.',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
    }
  };

  // Function to pick image from gallery and scan QR code
  const pickImageAndScanQR = async () => {
    try {
      const image = await ImagePicker.openPicker({
        cropping: false,
        mediaType: 'photo',
        multiple: false,
      });
      if (image) {
        try {
          setLoading(true);
          console.log('Image object:', image);
          console.log('Original path:', image.path);

          let response = null;
          let lastError = null;
          let uri = image.sourceURL || image.path;
          try {
            response = await RNQRGenerator.detect({uri});
            console.log('QR detection response:', response);
          } catch (err) {
            console.log('Failed with URI:', uri, 'Error:', err);
            lastError = err;
          }

          if (response && response.values && response.values.length > 0) {
            console.log('QR scan result:', response.values[0]);
            handleQRCodeResult(response.values[0]);
            setLoading(false);
          } else {
            showDialog({
              ref: dialogRef,
              title: 'Không tìm thấy mã QR',
              content: 'Vui lòng thử lại với ảnh khác.',
              onSubmit: () => setLoading(false),
            });
            setLoading(false);
          }
        } catch (error) {
          console.error('QR scan error:', error);
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          showSnackbar({
            message:
              'Không thể đọc mã QR từ hình ảnh này. Lỗi: ' + errorMessage,
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    } catch (error) {
      if (error !== 'E_PICKER_CANCELLED') {
        console.error('Image picker error:', error);
        showSnackbar({
          status: ComponentStatus.ERROR,
          message: 'Không thể chọn ảnh từ thư viện',
        });
      }
    }
  };
  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: async (codes: any) => {
      // console.log(`onCodeScanned `, codes);
      // console.log(`onCodeScanned value`, codes[0].value);

      if (codes[0]?.value) {
        const data = codes[0].value;
        handleQRCodeResult(data);
      } else {
        showSnackbar({
          message: 'Không thể đọc mã QR. Vui lòng thử lại.',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    // exception case
    setRefresh(!refresh);
  }, [device, hasPermission]);

  useEffect(() => {
    const requestCameraPermission = async () => {
      const permission = await Camera.requestCameraPermission();
      // console.log("Camera.requestCameraPermission ", permission);
      setHasPermission(permission === 'granted');
    };

    requestCameraPermission();

    // if it is idle for 15 secs, it will be closed
    // setTimeout(() => {
    //     if (ref) closePopup(ref);
    // }, 15 * 1000);
  }, []);

  if (device == null || !hasPermission) {
    return (
      <View
        style={{
          flex: 1,
          position: 'absolute',
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Text style={{backgroundColor: 'white'}}>
          Camera not available or not permitted
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView
      style={{
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: '#fff',
      }}>
      <FLoading visible={isLoading} />
      <FDialog ref={dialogRef} />
      <ScreenHeader
        style={{
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          flexDirection: 'row',
          paddingVertical: 4,
          paddingHorizontal: 8,
        }}
        title={`Quét mã QR`}
        prefix={
          <View style={{flexDirection: 'row', gap: 4}}>
            {/* Flashlight button */}
            <TouchableOpacity
              onPress={() => setIsTorchOn(isTorchOn == 'on' ? 'off' : 'on')}
              style={{padding: 8, alignItems: 'center'}}>
              <Winicon
                src={
                  isTorchOn == 'on'
                    ? 'fill/buildings/flashlight'
                    : 'outline/buildings/flashlight'
                }
                size={20}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>

            {/* Image picker button */}
            <TouchableOpacity
              onPress={() => pickImageAndScanQR()}
              style={{padding: 8}}>
              <Winicon
                src="fill/editing/image"
                size={24}
                color={ColorThemes.light.neutral_text_body_color}
              />
            </TouchableOpacity>
          </View>
        }
        action={
          <TouchableOpacity
            onPress={() => closePopup(ref)}
            style={{padding: 8, alignItems: 'center'}}>
            <Winicon
              src="outline/layout/xmark"
              size={20}
              color={ColorThemes.light.neutral_text_body_color}
            />
          </TouchableOpacity>
        }
      />
      <View
        style={{
          flex: 1,
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <Camera
          ref={camera}
          codeScanner={codeScanner}
          style={StyleSheet.absoluteFill}
          device={device}
          isActive={true}
          torch={isTorchOn}
        />
      </View>
    </SafeAreaView>
  );
});
