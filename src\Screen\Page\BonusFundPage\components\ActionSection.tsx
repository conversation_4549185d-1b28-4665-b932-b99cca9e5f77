import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {AppButton} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {RewardFund} from '../../../../redux/types/rewardFundTypes';

interface ActionSectionProps {
  isLoading: boolean;
  onStartBonus: () => void;
  selectedMonth: number | null;
  selectedYear: number;
  rewardFund: RewardFund | null;
}

const ActionSection: React.FC<ActionSectionProps> = ({
  isLoading,
  onStartBonus,
  selectedMonth,
  selectedYear,
  rewardFund,
}) => {
  // Hàm kiểm tra trạng thái và quyết định hiển thị
  const getButtonState = () => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    // <PERSON><PERSON><PERSON> tra tháng được chọn phải nhỏ hơn tháng hiện tại
    const isFilterPastMonth =
      selectedMonth !== null &&
      (selectedYear < currentYear ||
        (selectedYear === currentYear && selectedMonth < currentMonth));

    // Nếu không phải tháng quá khứ
    if (!isFilterPastMonth) {
      return {
        canShow: false,
        title: '',
        message: 'Chỉ có thể chia thưởng cho các tháng đã qua',
        disabled: true,
      };
    }

    // Nếu không có RewardFund
    if (!rewardFund) {
      return {
        canShow: false,
        title: '',
        message: 'Không tìm thấy quỹ thưởng cho tháng này',
        disabled: true,
      };
    }

    // Kiểm tra Status của RewardFund
    if (rewardFund.Status === 1) {
      // Chưa chia thưởng - hiển thị nút
      return {
        canShow: true,
        title: 'Chia thưởng',
        message: 'Quỹ thưởng tháng này chưa được chia',
        disabled: false,
      };
    } else {
      // Đã chia thưởng - chỉ hiển thị thông báo
      return {
        canShow: false,
        title: '',
        message: 'Quỹ thưởng tháng này đã được chia',
        disabled: true,
      };
    }
  };

  const buttonState = getButtonState();

  return (
    <View style={styles.actionSection}>
      <Text style={styles.disabledMessage}>{buttonState.message}</Text>
      {buttonState.canShow && (
        <AppButton
          title={buttonState.title}
          onPress={onStartBonus}
          backgroundColor={
            buttonState.disabled
              ? ColorThemes.light.neutral_text_subtitle_color
              : ColorThemes.light.primary_main_color
          }
          containerStyle={styles.actionButton}
          textStyle={styles.actionButtonText}
          borderColor="transparent"
          disabled={isLoading || buttonState.disabled}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  actionSection: {
    paddingHorizontal: 16,
    marginTop: 24,
  },
  actionButton: {
    height: 52,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    ...TypoSkin.buttonText1,
    color: ColorThemes.light.white,
    fontWeight: '600',
  },
  disabledMessage: {
    ...TypoSkin.body3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    marginBottom: 12,
    paddingHorizontal: 16,
  },
});

export default ActionSection;
