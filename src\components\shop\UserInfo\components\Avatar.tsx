import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import FastImage from '@d11/react-native-fast-image';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import ConfigAPI from '../../../../Config/ConfigAPI';

interface AvatarProps {
  avt: string | null;
  customer: any;
  onPress: () => void;
}

const Avatar = ({avt, customer, onPress}: AvatarProps) => {
  const getAvatarSource = () => {
    if (avt?.includes('https')) {
      return {uri: avt};
    }
    if (avt) {
      return {uri: ConfigAPI.urlImg + avt};
    }
    return null;
  };

  const source = getAvatarSource();

  return (
    <TouchableOpacity style={styles.avatarContainer} onPress={onPress}>
      {source ? (
        <FastImage source={source} style={styles.avatarImage} />
      ) : (
        <View style={styles.initialsContainer}>
          <Text style={styles.initialsText}>
            {customer?.Name
              ? customer?.Name.charAt(0).toUpperCase()
              : customer?.Email
              ? customer?.Email.charAt(0).toUpperCase()
              : ''}
          </Text>
        </View>
      )}
      <View style={styles.cameraIconContainer}>
        <Winicon src="fill/entertainment/camera" size={10} color={'#000'} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    position: 'relative',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  initialsContainer: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: ColorThemes.light.primary_main_color,
  },
  initialsText: {
    fontSize: 24,
    color: '#fff',
    fontWeight: 'bold',
  },
  cameraIconContainer: {
    position: 'absolute',
    padding: 5,
    borderRadius: 24,
    backgroundColor: '#fff',
    right: -2,
    bottom: -2,
  },
});

export default Avatar;
